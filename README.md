# 商户服务器 (Merchant Server)

基于 GoFrame v2 框架开发的商户 API 服务系统，提供充值地址管理、提现功能、交易查询等核心功能。

## 功能特性

### 🏦 充值地址管理
- 动态分配充值地址
- 支持多链多币种
- 地址复用机制
- 二维码生成

### 💸 提现功能
- 提现申请创建
- 手续费计算
- 状态跟踪
- 取消功能

### 📊 交易查询
- 充值记录查询
- 提现记录查询
- 统一交易查询
- 详情查询

### 🔔 回调通知
- 状态变更通知
- 签名验证
- 重试机制
- 失败处理

### 🔐 安全认证
- API Key + Secret 认证
- HMAC-SHA256 签名
- 时间戳验证
- 权限控制

### 📈 监控日志
- 操作日志记录
- 性能监控
- 错误追踪
- 统计分析

## 技术栈

- **框架**: GoFrame v2
- **语言**: Go 1.22+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+ (可选)
- **Web服务器**: Nginx
- **部署**: Docker / Systemd

## 项目结构

```
merchant-server/
├── api/                    # API 定义
│   └── merchant/
│       └── v1/            # v1 版本 API
├── internal/              # 内部代码
│   ├── controller/        # 控制器
│   ├── logic/            # 业务逻辑
│   ├── service/          # 服务接口
│   ├── dao/              # 数据访问对象
│   ├── model/            # 数据模型
│   └── middleware/       # 中间件
├── config/               # 配置文件
├── docs/                 # 文档
├── scripts/              # 脚本
├── logs/                 # 日志文件
├── go.mod               # Go 模块文件
├── go.sum               # 依赖校验文件
├── main.go              # 入口文件
└── README.md            # 项目说明
```

## 快速开始

### 环境要求

- Go 1.22+
- MySQL 8.0+
- Redis 6.0+ (可选)

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-org/merchant-server.git
cd merchant-server

# 安装依赖
go mod download
```

### 配置数据库

```sql
-- 创建数据库
CREATE DATABASE merchant_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'merchant_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON merchant_server.* TO 'merchant_user'@'localhost';
FLUSH PRIVILEGES;
```

### 配置文件

复制并修改配置文件：

```bash
cp config/config.example.yaml config/config.yaml
```

编辑 `config/config.yaml`：

```yaml
server:
  address: "0.0.0.0:9000"
  
database:
  default:
    link: "mysql:merchant_user:your_password@tcp(127.0.0.1:3306)/merchant_server"
```

### 运行项目

```bash
# 开发模式
go run main.go

# 或者编译后运行
go build -o merchant-server
./merchant-server
```

访问 `http://localhost:9000/merchant-server/v1/health` 检查服务状态。

## API 文档

详细的 API 文档请参考：[API.md](docs/API.md)

### 认证示例

```bash
# 获取充值地址
curl -X POST http://localhost:9000/merchant-server/v1/deposit/address \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -H "X-Timestamp: **********" \
  -H "X-Signature: calculated_signature" \
  -d '{
    "user_label": "user_001",
    "chain": "trx",
    "token": "native"
  }'
```

### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "address": "TTest123456789",
    "qr_code": "base64_encoded_qr_code",
    "user_label": "user_001",
    "chain": "trx",
    "token": "native",
    "created_at": "2024-01-01 12:00:00",
    "is_reused": false
  }
}
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t merchant-server .

# 运行容器
docker run -d \
  --name merchant-server \
  -p 9000:9000 \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/logs:/app/logs \
  merchant-server
```

### 生产部署

详细的部署文档请参考：[DEPLOYMENT.md](docs/DEPLOYMENT.md)

## 开发

### 代码生成

```bash
# 生成 DAO
gf gen dao

# 生成 Service
gf gen service
```

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/logic/merchant/

# 运行测试并显示覆盖率
go test -cover ./...
```

### 代码检查

```bash
# 格式化代码
go fmt ./...

# 静态检查
go vet ./...

# 使用 golangci-lint
golangci-lint run
```

## 监控

### 健康检查

```bash
curl http://localhost:9000/health
```

### 指标监控

系统提供以下监控指标：

- API 调用次数
- 响应时间
- 错误率
- 活跃商户数
- 交易统计

### 日志

日志文件位置：

- 应用日志：`logs/merchant-server.log`
- 访问日志：`logs/access.log`
- 错误日志：`logs/error.log`

## 贡献

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 遵循 Go 官方代码规范
- 使用 `gofmt` 格式化代码
- 添加必要的注释
- 编写单元测试

## 许可证

本项目采用 MIT 许可证 - 详情请参考 [LICENSE](LICENSE) 文件。

## 支持

### 文档

- [API 文档](docs/API.md)
- [部署文档](docs/DEPLOYMENT.md)
- [开发文档](docs/DEVELOPMENT.md)

### 联系我们

- **邮箱**: <EMAIL>
- **电话**: +86-************
- **工作时间**: 周一至周五 9:00-18:00

### 问题反馈

如果您发现任何问题或有改进建议，请：

1. 查看 [Issues](https://github.com/your-org/merchant-server/issues) 是否已有相关问题
2. 如果没有，请创建新的 Issue
3. 提供详细的问题描述和复现步骤

## 更新日志

### v1.0.0 (2024-01-01)

- ✨ 初始版本发布
- 🏦 充值地址管理功能
- 💸 提现功能
- 📊 交易查询功能
- 🔔 回调通知系统
- 🔐 安全认证机制
- 📈 监控日志系统

## 致谢

感谢以下开源项目：

- [GoFrame](https://github.com/gogf/gf) - Go 应用开发框架
- [MySQL](https://www.mysql.com/) - 关系型数据库
- [Redis](https://redis.io/) - 内存数据库
- [Nginx](https://nginx.org/) - Web 服务器

---

**注意**: 这是一个示例项目，请根据实际需求进行调整和完善。在生产环境中使用前，请确保进行充分的测试和安全审计。
