package main

import (
	"context"
	"fmt"
	"log"

	"github.com/shopspring/decimal"
)

// 测试新的手续费计算系统
func main() {
	ctx := context.Background()
	
	fmt.Println("=== 新手续费系统测试 ===")
	
	// 测试用例数据
	testCases := []struct {
		Chain       string
		Token       string
		Amount      string
		Description string
		FeeType     string
		ExpectedToken string
	}{
		{"TRX", "native", "100.0", "TRX原生币提现", "rate", "TRX"},
		{"ETH", "native", "1.0", "ETH原生币提现", "rate", "ETH"},
		{"TRC20", "USDT", "100.0", "TRC20-USDT提现", "rate", "USDT"},
		{"ERC20", "USDT", "100.0", "ERC20-USDT提现", "fix", "USDT"},
		{"TRX", "native", "500.0", "TRX大额提现", "rate", "TRX"},
		{"ETH", "native", "0.1", "ETH小额提现", "fix", "ETH"},
	}
	
	fmt.Println("\n--- 代币映射测试 ---")
	for i, tc := range testCases {
		fmt.Printf("测试 %d: %s\n", i+1, tc.Description)
		
		// 测试代币映射
		expectedToken := mapChainToTokenSymbol(tc.Chain, tc.Token)
		if expectedToken == tc.ExpectedToken {
			fmt.Printf("  ✅ 代币映射: %s + %s → %s\n", tc.Chain, tc.Token, expectedToken)
		} else {
			fmt.Printf("  ❌ 代币映射错误: 期望 %s，得到 %s\n", tc.ExpectedToken, expectedToken)
		}
	}
	
	fmt.Println("\n--- 费用计算模拟测试 ---")
	
	// 模拟比例费用计算
	fmt.Println("\n1. 比例费用模式 (rate)")
	testRateFee("ETH", "1.0", "1.5") // 1 ETH, 1.5%
	testRateFee("TRX", "1000.0", "2.0") // 1000 TRX, 2.0%
	testRateFee("USDT", "100.0", "1.0") // 100 USDT, 1.0%
	
	// 模拟固定费用计算
	fmt.Println("\n2. 固定费用模式 (fix)")
	testFixedFee("ETH", "1.0", "0.001") // 1 ETH, 0.001 ETH 手续费
	testFixedFee("TRX", "1000.0", "1.0") // 1000 TRX, 1 TRX 手续费
	testFixedFee("USDT", "100.0", "2.0") // 100 USDT, 2 USDT 手续费
	
	fmt.Println("\n--- 配置示例验证 ---")
	validateConfigExamples()
	
	fmt.Println("\n--- 边界情况测试 ---")
	testEdgeCases()
	
	fmt.Println("\n✅ 所有测试完成")
}

// mapChainToTokenSymbol 模拟代币映射函数
func mapChainToTokenSymbol(chain, token string) string {
	switch chain {
	case "TRC20":
		return "USDT"
	case "ERC20":
		return "USDT"
	case "TRX":
		return "TRX"
	case "ETH":
		return "ETH"
	default:
		return token
	}
}

// testRateFee 测试比例费用计算
func testRateFee(token, amountStr, rateStr string) {
	amount, _ := decimal.NewFromString(amountStr)
	rate, _ := decimal.NewFromString(rateStr)
	
	// 计算: amount * (rate / 100)
	fee := amount.Mul(rate).Div(decimal.NewFromInt(100))
	netAmount := amount.Sub(fee)
	
	fmt.Printf("  %s: %s × %s%% = %s %s (净额: %s)\n", 
		token, amountStr, rateStr, fee.String(), token, netAmount.String())
}

// testFixedFee 测试固定费用计算
func testFixedFee(token, amountStr, feeStr string) {
	amount, _ := decimal.NewFromString(amountStr)
	fee, _ := decimal.NewFromString(feeStr)
	netAmount := amount.Sub(fee)
	
	fmt.Printf("  %s: %s - %s = %s %s (固定费用)\n", 
		token, amountStr, feeStr, netAmount.String(), token)
}

// validateConfigExamples 验证配置示例
func validateConfigExamples() {
	fmt.Println("验证配置结构合理性:")
	
	configs := map[string]interface{}{
		"withdrawals_setting.fee_type": "rate",
		"withdrawals_setting.fee_rate.ETH": "1.5",
		"withdrawals_setting.fee_rate.TRX": "2.0", 
		"withdrawals_setting.fee_rate.USDT": "1.0",
		"withdrawals_setting.fee_amount.ETH": "0.001",
		"withdrawals_setting.fee_amount.TRX": "1.0",
		"withdrawals_setting.fee_amount.USDT": "2.0",
	}
	
	for key, value := range configs {
		fmt.Printf("  ✅ %s = %v\n", key, value)
	}
}

// testEdgeCases 测试边界情况
func testEdgeCases() {
	fmt.Println("测试边界情况:")
	
	// 测试零金额
	fmt.Printf("  零金额: 0 × 1.5%% = 0 (正常)\n")
	
	// 测试极小金额
	amount := decimal.NewFromString("0.000001")
	rate := decimal.NewFromString("1.5")
	fee := amount.Mul(rate).Div(decimal.NewFromInt(100))
	fmt.Printf("  极小金额: %s × %s%% = %s\n", amount.String(), rate.String(), fee.String())
	
	// 测试负费率处理
	fmt.Printf("  负费率处理: 如果费率 < 0，应该使用 0\n")
	
	// 测试费用超过本金
	bigFee := decimal.NewFromString("100.0")
	smallAmount := decimal.NewFromString("1.0")
	if bigFee.GreaterThan(smallAmount) {
		fmt.Printf("  费用超过本金: %s > %s (应该报错)\n", bigFee.String(), smallAmount.String())
	}
}

// 注意：这个测试脚本只是模拟验证，实际运行需要与真实的配置系统集成