package main

import (
	"context"
	"fmt"
	"log"

	"github.com/shopspring/decimal"
	"merchant-server/internal/logic/merchant"
)

// 测试手续费配置系统
func main() {
	ctx := context.Background()
	
	// 创建提现服务实例
	withdrawService := merchant.NewWithdrawService()
	
	fmt.Println("=== 手续费配置系统测试 ===")
	
	// 测试用例
	testCases := []struct {
		Chain  string
		Token  string
		Amount string
		Desc   string
	}{
		{"TRX", "native", "50.0", "TRX原生币 - 小额"},
		{"TRX", "native", "500.0", "TRX原生币 - 中额"},
		{"TRX", "native", "5000.0", "TRX原生币 - 大额"},
		{"ETH", "native", "1.0", "ETH原生币"},
		{"ETH", "usdt", "100.0", "ETH链USDT代币"},
		{"BSC", "native", "0.1", "BSC原生币"},
		{"UNKNOWN", "UNKNOWN", "100.0", "未知链和代币"},
	}
	
	for i, testCase := range testCases {
		fmt.Printf("\n--- 测试 %d: %s ---\n", i+1, testCase.Desc)
		
		// 测试限额验证
		fmt.Printf("1. 限额验证 (%s %s %s): ", testCase.Chain, testCase.Token, testCase.Amount)
		err := withdrawService.ValidateWithdrawLimits(ctx, testCase.Chain, testCase.Token, testCase.Amount)
		if err != nil {
			fmt.Printf("❌ %v\n", err)
		} else {
			fmt.Printf("✅ 通过\n")
		}
		
		// 测试手续费计算
		fmt.Printf("2. 手续费计算: ")
		fee, netAmount, err := withdrawService.CalculateFee(ctx, testCase.Chain, testCase.Token, testCase.Amount)
		if err != nil {
			fmt.Printf("❌ %v\n", err)
		} else {
			fmt.Printf("✅ 手续费: %s, 到账金额: %s\n", fee, netAmount)
		}
		
		// 测试手续费预览
		fmt.Printf("3. 手续费预览: ")
		feeData, err := withdrawService.GetWithdrawFee(ctx, 1, &struct {
			Chain  string
			Token  string
			Amount string
		}{
			Chain:  testCase.Chain,
			Token:  testCase.Token,
			Amount: testCase.Amount,
		})
		if err != nil {
			fmt.Printf("❌ %v\n", err)
		} else {
			fmt.Printf("✅ 费率类型: %s, 预估时间: %s\n", 
				feeData.FeeInfo.FeeRate, feeData.EstimatedTime)
		}
	}
	
	fmt.Println("\n=== 配置降级测试 ===")
	
	// 测试配置降级机制
	testFallback(ctx, withdrawService)
	
	fmt.Println("\n=== 阶梯费率测试 ===")
	
	// 测试阶梯费率
	testTieredRates(ctx, withdrawService)
	
	fmt.Println("\n✅ 所有测试完成")
}

// 测试配置降级机制
func testFallback(ctx context.Context, service *merchant.WithdrawService) {
	fmt.Println("测试配置降级: 当Consul配置不可用时，系统应该使用硬编码默认值")
	
	// 测试一个不存在的配置
	_, err := service.CalculateFee(ctx, "NONEXISTENT", "NONEXISTENT", "100.0")
	if err != nil {
		fmt.Printf("❌ 降级失败: %v\n", err)
	} else {
		fmt.Printf("✅ 降级成功: 使用了默认配置\n")
	}
}

// 测试阶梯费率
func testTieredRates(ctx context.Context, service *merchant.WithdrawService) {
	fmt.Println("测试阶梯费率: 不同金额应该使用不同的费率")
	
	amounts := []string{"50", "500", "5000", "50000"}
	
	for _, amount := range amounts {
		fee, _, err := service.CalculateFee(ctx, "TRX", "native", amount)
		if err != nil {
			fmt.Printf("金额 %s: ❌ %v\n", amount, err)
		} else {
			amountDecimal, _ := decimal.NewFromString(amount)
			feeDecimal, _ := decimal.NewFromString(fee)
			rate := feeDecimal.Div(amountDecimal).Mul(decimal.NewFromInt(100))
			fmt.Printf("金额 %s: 手续费 %s (费率 %s%%)\n", 
				amount, fee, rate.StringFixed(3))
		}
	}
}

// 注意：这个文件使用了一些可能不存在的方法，仅作为测试示例
// 在实际使用中需要根据实际的API接口进行调整