package main

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"

	"merchant-server/internal/config"
)

// 真实配置管理器测试 - 使用实际的config.GetMapKey方法
func main() {
	ctx := context.Background()
	
	fmt.Println("=== 真实Config Manager测试 ===")
	
	// 初始化配置管理器
	if err := config.Initialize(ctx); err != nil {
		fmt.Printf("❌ 配置管理器初始化失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ 配置管理器初始化成功")
	
	// 测试用例
	testCases := []struct {
		Chain       string
		Token       string
		Amount      string
		Description string
	}{
		{"TRX", "native", "100.0", "TRX原生币提现"},
		{"ETH", "native", "1.0", "ETH原生币提现"},
		{"TRC20", "USDT", "100.0", "TRC20-USDT提现"},
		{"ERC20", "USDT", "100.0", "ERC20-USDT提现"},
		{"TRX", "native", "1000.0", "TRX大额提现"},
		{"ETH", "native", "0.1", "ETH小额提现"},
	}
	
	fmt.Println("\n--- 配置读取测试 ---")
	testConfigReading(ctx)
	
	fmt.Println("\n--- 手续费计算测试 ---")
	
	for i, tc := range testCases {
		fmt.Printf("\n测试 %d: %s\n", i+1, tc.Description)
		fmt.Printf("  输入: %s %s %s\n", tc.Chain, tc.Token, tc.Amount)
		
		// 测试代币映射
		tokenSymbol := mapChainToTokenSymbol(tc.Chain, tc.Token)
		fmt.Printf("  代币映射: %s + %s → %s\n", tc.Chain, tc.Token, tokenSymbol)
		
		// 测试手续费计算
		amount, err := decimal.NewFromString(tc.Amount)
		if err != nil {
			fmt.Printf("  ❌ 金额解析错误: %v\n", err)
			continue
		}
		
		// 使用真实的配置管理器计算手续费
		fee, err := calculateRealTokenFee(ctx, amount, tc.Chain, tc.Token)
		if err != nil {
			fmt.Printf("  ❌ 手续费计算错误: %v\n", err)
			continue
		}
		
		fmt.Printf("  ✅ 手续费: %s %s\n", fee, tokenSymbol)
		
		// 计算净额
		feeDec, _ := decimal.NewFromString(fee)
		netAmount := amount.Sub(feeDec)
		fmt.Printf("  净额: %s %s\n", netAmount.String(), tokenSymbol)
	}
	
	fmt.Println("\n✅ 真实配置管理器测试完成")
}

// testConfigReading 测试配置读取 - 使用真实的config manager
func testConfigReading(ctx context.Context) {
	fmt.Println("测试真实配置管理器连接:")
	
	// 测试withdrawals_setting配置读取
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "rate")
	if err != nil {
		fmt.Printf("  ❌ 无法读取 fee_type: %v\n", err)
	} else {
		fmt.Printf("  ✅ fee_type = %s\n", feeType)
	}
	
	// 测试GetMapKey方法 - 这是用户提到的方法
	tokens := []string{"ETH", "TRX", "USDT"}
	for _, token := range tokens {
		// 测试费率配置
		rate, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", token, "0")
		if err != nil {
			fmt.Printf("  ❌ 无法读取 %s 费率: %v\n", token, err)
		} else {
			fmt.Printf("  ✅ %s rate = %v (使用GetMapKey)\n", token, rate)
		}
		
		// 测试固定费用配置
		amount, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", token, "0")
		if err != nil {
			fmt.Printf("  ❌ 无法读取 %s 固定费用: %v\n", token, err)
		} else {
			fmt.Printf("  ✅ %s amount = %v (使用GetMapKey)\n", token, amount)
		}
	}
	
	// 测试直接获取完整配置Map
	feeRateMap, err := config.GetMap(ctx, "withdrawals_setting.fee_rate")
	if err != nil {
		fmt.Printf("  ❌ 无法读取 fee_rate 映射: %v\n", err)
	} else {
		fmt.Printf("  ✅ fee_rate 映射: %+v\n", feeRateMap)
	}
	
	feeAmountMap, err := config.GetMap(ctx, "withdrawals_setting.fee_amount")
	if err != nil {
		fmt.Printf("  ❌ 无法读取 fee_amount 映射: %v\n", err)
	} else {
		fmt.Printf("  ✅ fee_amount 映射: %+v\n", feeAmountMap)
	}
}

// calculateRealTokenFee 使用真实配置管理器计算手续费
func calculateRealTokenFee(ctx context.Context, amountDec decimal.Decimal, chain, token string) (string, error) {
	// 映射到标准代币符号
	tokenSymbol := mapChainToTokenSymbol(chain, token)
	
	// 使用真实config manager获取费用类型配置
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "rate")
	if err != nil {
		return "", fmt.Errorf("无法获取费用类型配置: %v", err)
	}
	
	feeDec := decimal.Zero
	
	switch feeType {
	case "rate":
		// 使用GetMapKey方法获取费率配置
		rateValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", tokenSymbol, "0")
		if err != nil {
			return "", fmt.Errorf("无法获取 %s 的费率配置: %v", tokenSymbol, err)
		}
		
		// 转换为字符串然后解析为decimal
		rateStr := fmt.Sprintf("%v", rateValue)
		rateDec, err := decimal.NewFromString(rateStr)
		if err != nil {
			return "", fmt.Errorf("费率格式不正确 %s: %v", rateStr, err)
		}
		
		// 计算费用: amount * (rate / 100)
		feeDec = amountDec.Mul(rateDec).Div(decimal.NewFromInt(100))
		
	case "fix":
		// 使用GetMapKey方法获取固定费用配置
		amountValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", tokenSymbol, "0")
		if err != nil {
			return "", fmt.Errorf("无法获取 %s 的固定费用配置: %v", tokenSymbol, err)
		}
		
		// 转换为字符串然后解析为decimal
		amountStr := fmt.Sprintf("%v", amountValue)
		feeDec, err = decimal.NewFromString(amountStr)
		if err != nil {
			return "", fmt.Errorf("固定费用格式不正确 %s: %v", amountStr, err)
		}
		
	default:
		return "", fmt.Errorf("不支持的费用类型: %s", feeType)
	}
	
	// 确保费用不为负数
	if feeDec.IsNegative() {
		feeDec = decimal.Zero
	}
	
	return feeDec.String(), nil
}

// mapChainToTokenSymbol 映射链类型到标准代币符号
func mapChainToTokenSymbol(chain, token string) string {
	switch chain {
	case "TRC20":
		return "USDT"
	case "ERC20":
		return "USDT"
	case "TRX":
		return "TRX"
	case "ETH":
		return "ETH"
	default:
		return token
	}
}