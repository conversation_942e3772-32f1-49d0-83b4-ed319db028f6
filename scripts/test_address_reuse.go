package main

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

const (
	BaseURL    = "http://localhost:9000"
	APIKey     = "MK5Tn99YdKVTi5_4n1rTfkpAkytul0tMPtOooB_4Ndc="
	SecretHash = "$2a$10$UWtFj3OcABBDYvU008nPRuHZamzFjQUbhLDf1vs3oWAClV6FYLD7S"
)

func generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.StdEncoding.EncodeToString(bytes)
}

func generateSignature(method, uri, timestamp, nonce, body, secretHash string) string {
	signString := method + uri + timestamp + nonce + body
	h := hmac.New(sha256.New, []byte(secretHash))
	h.Write([]byte(signString))
	return hex.EncodeToString(h.Sum(nil))
}

func testDepositAddress(userLabel, description string) {
	fmt.Printf("\n=== %s ===\n", description)
	
	requestData := map[string]interface{}{
		"user_label": userLabel,
		"chain":      "TRX",
		"token":      "native",
	}
	
	jsonData, _ := json.Marshal(requestData)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := generateNonce()
	body := string(jsonData)
	uri := "/api/v1/deposits/address"
	
	signature := generateSignature("POST", uri, timestamp, nonce, body, SecretHash)
	
	req, _ := http.NewRequest("POST", BaseURL+uri, strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", APIKey)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	responseBody, _ := io.ReadAll(resp.Body)
	fmt.Printf("📄 响应: %s\n", string(responseBody))
	
	// 解析响应查看is_reused字段
	var result map[string]interface{}
	if json.Unmarshal(responseBody, &result) == nil {
		if data, ok := result["data"].(map[string]interface{}); ok {
			if innerData, ok := data["data"].(map[string]interface{}); ok {
				address := innerData["address"]
				isReused := innerData["is_reused"]
				fmt.Printf("🏠 地址: %v\n", address)
				fmt.Printf("🔄 是否复用: %v\n", isReused)
			}
		}
	}
}

func main() {
	fmt.Println("🔄 测试地址复用功能")
	
	// 第一次请求 - 应该创建新地址
	testDepositAddress("test_reuse_user", "第一次请求 - 应该创建新地址")
	
	time.Sleep(1 * time.Second)
	
	// 第二次请求相同用户 - 应该复用地址
	testDepositAddress("test_reuse_user", "第二次请求相同用户 - 应该复用地址")
	
	time.Sleep(1 * time.Second)
	
	// 不同用户 - 应该创建新地址
	testDepositAddress("test_different_user", "不同用户 - 应该创建新地址")
}