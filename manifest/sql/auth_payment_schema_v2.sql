-- 授权支付订单表（简化版本 - 无需预授权密钥）
CREATE TABLE IF NOT EXISTS `auth_payment_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(64) NOT NULL COMMENT '系统订单号',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商户ID',
  `user_account` varchar(255) NOT NULL COMMENT '用户账户标识(users.account)',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户ID(查询到后填充)',
  `order_type` enum('deduct','add') NOT NULL COMMENT '订单类型: deduct-扣款, add-加款',
  `token_symbol` varchar(32) NOT NULL COMMENT '代币符号',
  `amount` decimal(65,18) NOT NULL COMMENT '订单金额',
  `auth_reason` varchar(500) DEFAULT NULL COMMENT '授权原因/说明',
  `merchant_order_no` varchar(128) DEFAULT NULL COMMENT '商户订单号',
  `merchant_transaction_id` bigint(20) unsigned DEFAULT NULL COMMENT '商户交易记录ID',
  `user_transaction_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户交易记录ID',
  `status` enum('pending','completed','expired','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `callback_url` varchar(500) DEFAULT NULL COMMENT '回调URL(使用商户默认或指定)',
  `callback_status` enum('pending','success','failed') DEFAULT 'pending' COMMENT '回调状态',
  `callback_attempts` int(11) DEFAULT 0 COMMENT '回调尝试次数',
  `last_callback_at` datetime DEFAULT NULL COMMENT '最后回调时间',
  `next_callback_at` datetime DEFAULT NULL COMMENT '下次回调时间',
  `callback_response` text DEFAULT NULL COMMENT '最后回调响应',
  `expire_at` datetime DEFAULT NULL COMMENT '订单过期时间(仅扣款订单)',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `request_ip` varchar(45) DEFAULT NULL COMMENT '请求IP',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  UNIQUE KEY `uk_merchant_order_no` (`merchant_id`, `merchant_order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_account` (`user_account`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_at` (`expire_at`),
  KEY `idx_callback_status` (`callback_status`, `next_callback_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权支付订单表';

-- 添加索引优化查询性能
CREATE INDEX idx_merchant_status_created ON auth_payment_orders(merchant_id, status, created_at);
CREATE INDEX idx_callback_retry ON auth_payment_orders(callback_status, next_callback_at) WHERE callback_status = 'pending';