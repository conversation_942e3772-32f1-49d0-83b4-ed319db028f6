-- 授权支付密钥表
CREATE TABLE IF NOT EXISTS `auth_payment_keys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `key_uuid` varchar(64) NOT NULL COMMENT '授权密钥UUID',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商户ID',
  `user_account` varchar(255) NOT NULL COMMENT '用户账户标识(users.account)',
  `auth_type` enum('deduct','add') NOT NULL COMMENT '授权类型: deduct-扣款, add-加款',
  `token_symbol` varchar(32) NOT NULL COMMENT '授权代币符号',
  `token_id` int(11) unsigned NOT NULL COMMENT '代币ID',
  `authorized_amount` decimal(65,18) NOT NULL COMMENT '授权金额',
  `used_amount` decimal(65,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '已使用金额',
  `remaining_amount` decimal(65,18) NOT NULL COMMENT '剩余可用金额',
  `auth_reason` varchar(500) DEFAULT NULL COMMENT '授权原因/说明',
  `expire_at` datetime DEFAULT NULL COMMENT '授权过期时间',
  `status` enum('active','expired','revoked','exhausted') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key_uuid` (`key_uuid`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_account` (`user_account`),
  KEY `idx_status_expire` (`status`, `expire_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权支付密钥表';

-- 授权支付订单表
CREATE TABLE IF NOT EXISTS `auth_payment_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `auth_key_id` bigint(20) unsigned NOT NULL COMMENT '授权密钥ID',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商户ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `user_account` varchar(255) NOT NULL COMMENT '用户账户标识',
  `order_type` enum('deduct','add') NOT NULL COMMENT '订单类型: deduct-扣款, add-加款',
  `token_symbol` varchar(32) NOT NULL COMMENT '代币符号',
  `token_id` int(11) unsigned NOT NULL COMMENT '代币ID',
  `amount` decimal(65,18) NOT NULL COMMENT '订单金额',
  `merchant_order_no` varchar(128) DEFAULT NULL COMMENT '商户订单号',
  `transaction_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联交易ID',
  `merchant_transaction_id` bigint(20) unsigned DEFAULT NULL COMMENT '商户交易ID',
  `user_transaction_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户交易ID',
  `status` enum('pending','confirmed','completed','expired','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `callback_url` varchar(500) DEFAULT NULL COMMENT '回调URL',
  `callback_status` enum('pending','success','failed') DEFAULT 'pending' COMMENT '回调状态',
  `callback_count` int(11) DEFAULT 0 COMMENT '回调次数',
  `last_callback_at` datetime DEFAULT NULL COMMENT '最后回调时间',
  `expire_at` datetime DEFAULT NULL COMMENT '订单过期时间',
  `confirmed_at` datetime DEFAULT NULL COMMENT '确认时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `memo` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_auth_key_id` (`auth_key_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_at` (`expire_at`),
  KEY `idx_callback_status` (`callback_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权支付订单表';

-- 添加商户钱包中的新交易类型到枚举（如果需要）
-- ALTER TABLE `merchant_transactions` 
-- MODIFY COLUMN `type` enum('deposit','withdrawal','transfer','red_packet','payment','commission','system_adjust','auth_payment_add','auth_payment_deduct') NOT NULL COMMENT '交易类型';

-- 添加用户交易表的新交易类型（如果存在类似表）
-- ALTER TABLE `transactions` 
-- MODIFY COLUMN `type` enum(...existing types..., 'auth_payment_add','auth_payment_deduct') NOT NULL COMMENT '交易类型';