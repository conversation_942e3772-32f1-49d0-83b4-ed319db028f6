# https://goframe.org/docs/web/server-config-file-template
server:
  address:     ":9000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  # 安全头配置
  serverAgent: "Merchant-Server/1.0"
  logPath: "logs" # Gateway 日志路径 (从规划添加)
  dumpRouterMap: false
# https://goframe.org/docs/core/glog-config
logger:
  level: "info"  # 恢复到生产级别
  stdout: true
  path: "logs"
  file: "server.log"
  rotateSize: "100M"
  rotateExpire: "7d"
  format: "json"  # 使用 JSON 格式便于日志收集

# https://goframe.org/docs/core/gdb-config-file
database:
  logger:
    path: "logs/database"
    level: "error"  # 生产环境只记录错误
    stdout: false
  default: # 使用 MySQL
    link: "mysql:root:root@tcp(127.0.0.1:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4" # 确认或修改为您的 MySQL 连接信息
    debug: false  # 生产环境必须关闭
    maxIdleConnCount: 10
    maxOpenConnCount: 100
    maxConnLifeTime: "30s"


# https://goframe.org/docs/core/gredis-config
redis:
  default:
    address: "127.0.0.1:6379" # 确认或修改为您的 Redis 地址
    db: 0 # 确认或修改 DB 编号
    pass: "valkey_password"
    idleTimeout: "60s"
    maxConnLifetime: "90s"
    waitTimeout: "60s"
    dialTimeout: "30s"
    readTimeout: "30s"
    writeTimeout: "30s"
    maxActive: 100

# 安全配置
security:
  # API 频率限制配置
  rateLimit:
    enabled: true
    defaultLimit: 100  # 每分钟默认限制
    windowSeconds: 60  # 时间窗口
  
  # CORS 配置
  cors:
    allowOrigins: ["http://localhost:3000"]
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowHeaders: ["Content-Type", "Authorization", "X-API-Key", "X-Timestamp", "X-Nonce", "X-Signature"]
    allowCredentials: false
    maxAge: 86400
  
  # IP 白名单配置
  ipWhitelist:
    enabled: true
    trustedProxies: ["10.0.0.0/8", "**********/12", "***********/16"]


# Consul 配置（用于配置同步）
consul:
  address: "127.0.0.1:8500" # Consul 服务器地址
  token: "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874" # ACL Token（生产环境请修改）
  config_prefix: "xpay/config" # 配置存储前缀


s3:
    accessKeyID: "********************" # AWS Access Key ID
    secretAccessKey: "jRlPC3qDWMNvW7IX8MThWEalYSxeLupBcUKZCQHO" # AWS Secret Access Key
    region: "ap-northeast-1" # AWS Region
    bucketName: "yescex1" # AWS Bucket Name
    usePathStyleEndpoint: false # Whether to use path style endpoint
    publicURLPrefix: "" # (可选) 用于构建文件访问 URL 的前缀，如果为空，则使用 S3 默认 URL 格式