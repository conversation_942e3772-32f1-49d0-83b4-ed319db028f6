server:
  address: "${MERCHANT_SERVER_SERVER_ADDRESS}"
  openapiPath: "${MERCHANT_SERVER_SERVER_OPENAPIPATH}"
  swaggerPath: "${MERCHANT_SERVER_SERVER_SWAGGERPATH}"
  serverAgent: "${MERCHANT_SERVER_SERVER_SERVERAGENT}"
  logPath: "${MERCHANT_SERVER_SERVER_LOGPATH}"
  dumpRouterMap: ${MERCHANT_SERVER_SERVER_DUMPROUTERMAP}
logger:
  level: "${MERCHANT_SERVER_LOGGER_LEVEL}"
  stdout: ${MERCHANT_SERVER_LOGGER_STDOUT}
  path: "${MERCHANT_SERVER_LOGGER_PATH}"
  file: "${MERCHANT_SERVER_LOGGER_FILE}"
  rotateSize: "${MERCHANT_SERVER_LOGGER_ROTATESIZE}"
  rotateExpire: "${MERCHANT_SERVER_LOGGER_ROTATEEXPIRE}"
  format: "${MERCHANT_SERVER_LOGGER_FORMAT}"
database:
  logger:
    path: "${MERCHANT_SERVER_DATABASE_LOGGER_PATH}"
    level: "${MERCHANT_SERVER_DATABASE_LOGGER_LEVEL}"
    stdout: ${MERCHANT_SERVER_DATABASE_LOGGER_STDOUT}
  default:
    link: "${MERCHANT_SERVER_DATABASE_DEFAULT_LINK}"
    debug: ${MERCHANT_SERVER_DATABASE_DEFAULT_DEBUG}
    maxIdleConnCount: ${MERCHANT_SERVER_DATABASE_DEFAULT_MAXIDLECONNCOUNT}
    maxOpenConnCount: ${MERCHANT_SERVER_DATABASE_DEFAULT_MAXOPENCONNCOUNT}
    maxConnLifeTime: "${MERCHANT_SERVER_DATABASE_DEFAULT_MAXCONNLIFETIME}"
redis:
  default:
    address: "${MERCHANT_SERVER_REDIS_DEFAULT_ADDRESS}"
    db: ${MERCHANT_SERVER_REDIS_DEFAULT_DB}
    pass: "${MERCHANT_SERVER_REDIS_DEFAULT_PASS}"
    idleTimeout: "${MERCHANT_SERVER_REDIS_DEFAULT_IDLETIMEOUT}"
    maxConnLifetime: "${MERCHANT_SERVER_REDIS_DEFAULT_MAXCONNLIFETIME}"
    waitTimeout: "${MERCHANT_SERVER_REDIS_DEFAULT_WAITTIMEOUT}"
    dialTimeout: "${MERCHANT_SERVER_REDIS_DEFAULT_DIALTIMEOUT}"
    readTimeout: "${MERCHANT_SERVER_REDIS_DEFAULT_READTIMEOUT}"
    writeTimeout: "${MERCHANT_SERVER_REDIS_DEFAULT_WRITETIMEOUT}"
    maxActive: ${MERCHANT_SERVER_REDIS_DEFAULT_MAXACTIVE}
security:
  rateLimit:
    enabled: ${MERCHANT_SERVER_SECURITY_RATELIMIT_ENABLED}
    defaultLimit: ${MERCHANT_SERVER_SECURITY_RATELIMIT_DEFAULTLIMIT}
    windowSeconds: ${MERCHANT_SERVER_SECURITY_RATELIMIT_WINDOWSECONDS}
  cors:
    allowOrigins:
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWORIGINS_0}"
    allowMethods:
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_0}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_1}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_2}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_3}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_4}"
    allowHeaders:
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_0}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_1}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_2}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_3}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_4}"
    - "${MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_5}"
    allowCredentials: ${MERCHANT_SERVER_SECURITY_CORS_ALLOWCREDENTIALS}
    maxAge: ${MERCHANT_SERVER_SECURITY_CORS_MAXAGE}
  ipWhitelist:
    enabled: ${MERCHANT_SERVER_SECURITY_IPWHITELIST_ENABLED}
    trustedProxies:
    - "${MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_0}"
    - "${MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_1}"
    - "${MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_2}"
consul:
  address: "${MERCHANT_SERVER_CONSUL_ADDRESS}"
  token: "${MERCHANT_SERVER_CONSUL_TOKEN}"
  config_prefix: "${MERCHANT_SERVER_CONSUL_CONFIG_PREFIX}"
s3:
  accessKeyID: "${MERCHANT_SERVER_S3_ACCESSKEYID}"
  secretAccessKey: "${MERCHANT_SERVER_S3_SECRETACCESSKEY}"
  region: "${MERCHANT_SERVER_S3_REGION}"
  bucketName: "${MERCHANT_SERVER_S3_BUCKETNAME}"
  usePathStyleEndpoint: ${MERCHANT_SERVER_S3_USEPATHSTYLEENDPOINT}
  publicURLPrefix: ''
