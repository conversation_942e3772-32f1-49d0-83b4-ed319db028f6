#!/bin/bash

echo "测试API日志记录功能..."

# 模拟一个简单的健康检查请求
echo -e "\n测试健康检查接口..."
curl -X GET http://localhost:8002/health -v

# 等待日志写入
sleep 1

# 检查今天的日志目录
TODAY=$(date +%Y-%m-%d)
echo -e "\n\n检查日志目录: logs/$TODAY/"
ls -la logs/$TODAY/ 2>/dev/null || echo "日志目录不存在"

# 如果有health.log，显示内容
if [ -f "logs/$TODAY/health.log" ]; then
    echo -e "\n=== health.log 最新内容 ==="
    tail -n 1 "logs/$TODAY/health.log" | jq .
fi

# 显示所有日志文件
echo -e "\n\n所有日志文件："
find logs -name "*.log" -type f 2>/dev/null || echo "没有找到日志文件"