# 授权支付加款逻辑重构总结

## 重构完成

已成功重构授权支付加款逻辑，确保对用户的资金操作使用 `github.com/yalks/wallet` 库。

## 主要改动

### 1. 用户资金操作库切换
- **原来**：使用项目内部的 wallet 库处理用户资金
- **现在**：使用 `github.com/yalks/wallet` 库处理用户资金

### 2. 具体改动位置

#### 文件：`/home/<USER>/merchant/merchant-server/internal/logic/merchant/auth_payment.go`

**导入部分**（第18-19行）：
```go
externalWallet "github.com/yalks/wallet"
externalWalletConstants "github.com/yalks/wallet/constants"
```

**用户加款处理**（第231-270行）：
```go
// 5.3 执行用户加款（使用external wallet）
// 使用 github.com/yalks/wallet 库处理用户资金
userCreditDescription := descriptor.FormatBasicDescription(constants.FundOpAuthPaymentAdd, req.Amount.String(), req.TokenSymbol)

// 构建用户加款请求
userCreditReq := &externalWalletConstants.FundOperationRequest{
    UserID:      uint64(user.Id),
    TokenSymbol: req.TokenSymbol,
    Amount:      req.Amount,
    BusinessID:  businessID + "_credit",
    FundType:    externalWalletConstants.FundTypeTransferIn,
    Description: userCreditDescription,
    RelatedID:   int64(merchantId),
    Metadata: map[string]string{
        "order_no":      orderNo,
        "order_type":    "auth_payment_credit",
        "merchant_id":   fmt.Sprintf("%d", merchantId),
        "auth_reason":   req.AuthReason,
    },
    RequestSource: "auth_payment",
}

// 使用 external wallet 执行用户加款
userResult, err := externalWallet.Manager().ProcessFundOperationInTx(ctx, tx, userCreditReq)
```

### 3. 关键区别

1. **请求结构**：
   - 使用 `externalWalletConstants.FundOperationRequest` 而不是 `wallet.TransactionRequest`
   - 使用 `FundType` 字段而不是 `Operation` 和 `Direction`
   - `Metadata` 是 `map[string]string` 而不是 `map[string]interface{}`

2. **执行方法**：
   - 使用 `ProcessFundOperationInTx` 而不是 `UpdateBalance`
   - 返回的 TransactionID 是 string 类型，需要转换为 uint64

3. **资金流向**：
   - 商户扣款：使用内部 wallet 库
   - 用户加款：使用 external wallet 库（github.com/yalks/wallet）

### 4. 优势

1. **职责分离**：商户资金和用户资金使用不同的库管理，职责更清晰
2. **一致性**：与 telegram-bot 等其他系统保持一致的用户资金处理方式
3. **可维护性**：遵循现有架构设计，便于后续维护

## 测试

可以使用创建的测试脚本验证重构后的功能：
```bash
./test_auth_payment_add.sh
```

该脚本会：
1. 创建一个加款订单
2. 查询订单状态
3. 验证商户和用户的交易ID
4. 列出最近的加款订单