# 商户服务器部署文档

## 系统要求

### 硬件要求

- **CPU**: 2核心以上
- **内存**: 4GB 以上
- **存储**: 50GB 以上 SSD
- **网络**: 100Mbps 以上带宽

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 7+)
- **Go 版本**: 1.22+
- **数据库**: MySQL 8.0+
- **Redis**: 6.0+ (可选，用于缓存)
- **Nginx**: 1.18+ (反向代理)

## 环境准备

### 1. 安装 Go

```bash
# 下载并安装 Go
wget https://golang.org/dl/go1.22.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.22.0.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

### 2. 安装 MySQL

```bash
# Ubuntu
sudo apt update
sudo apt install mysql-server

# CentOS
sudo yum install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 3. 创建数据库

```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE merchant_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'merchant_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON merchant_server.* TO 'merchant_user'@'localhost';
FLUSH PRIVILEGES;
```

## 项目部署

### 1. 获取源码

```bash
# 克隆项目
git clone https://github.com/your-org/merchant-server.git
cd merchant-server

# 安装依赖
go mod download
```

### 2. 配置文件

创建配置文件 `config/config.yaml`：

```yaml
# 服务器配置
server:
  address: "0.0.0.0:9000"
  serverRoot: "/merchant-server"
  
# 数据库配置
database:
  default:
    link: "mysql:merchant_user:your_password@tcp(127.0.0.1:3306)/merchant_server"
    debug: false
    charset: "utf8mb4"
    dryRun: false
    maxIdle: 10
    maxOpen: 100
    maxLifetime: "30s"

# 日志配置
logger:
  level: "info"
  stdout: true
  file: "logs/merchant-server.log"

# Redis 配置（可选）
redis:
  default:
    address: "127.0.0.1:6379"
    db: 0
    pass: ""
    
# 安全配置
security:
  jwt:
    secret: "your_jwt_secret_key"
    expire: "24h"
  
# 业务配置
business:
  callback:
    timeout: "30s"
    retryTimes: 3
    retryInterval: "5m"
```

### 3. 数据库迁移

```bash
# 运行数据库迁移脚本
mysql -u merchant_user -p merchant_server < scripts/schema.sql

# 或者使用 GoFrame 的数据库工具
gf gen dao
```

### 4. 编译项目

```bash
# 编译生产版本
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o merchant-server .

# 或者使用 Makefile
make build
```

### 5. 创建系统服务

创建 systemd 服务文件 `/etc/systemd/system/merchant-server.service`：

```ini
[Unit]
Description=Merchant Server
After=network.target mysql.service

[Service]
Type=simple
User=merchant
Group=merchant
WorkingDirectory=/opt/merchant-server
ExecStart=/opt/merchant-server/merchant-server
Restart=always
RestartSec=5
Environment=GF_GCFG_PATH=/opt/merchant-server/config

[Install]
WantedBy=multi-user.target
```

### 6. 启动服务

```bash
# 创建用户和目录
sudo useradd -r -s /bin/false merchant
sudo mkdir -p /opt/merchant-server
sudo mkdir -p /opt/merchant-server/logs
sudo chown -R merchant:merchant /opt/merchant-server

# 复制文件
sudo cp merchant-server /opt/merchant-server/
sudo cp -r config /opt/merchant-server/
sudo chown -R merchant:merchant /opt/merchant-server

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable merchant-server
sudo systemctl start merchant-server

# 检查状态
sudo systemctl status merchant-server
```

## Nginx 配置

### 1. 安装 Nginx

```bash
# Ubuntu
sudo apt install nginx

# CentOS
sudo yum install nginx
```

### 2. 配置反向代理

创建配置文件 `/etc/nginx/sites-available/merchant-server`：

```nginx
upstream merchant_backend {
    server 127.0.0.1:9000;
}

server {
    listen 80;
    server_name api.yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 日志配置
    access_log /var/log/nginx/merchant-server.access.log;
    error_log /var/log/nginx/merchant-server.error.log;
    
    # API 路由
    location /merchant-server/ {
        proxy_pass http://merchant_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://merchant_backend/health;
        access_log off;
    }
}
```

### 3. 启用配置

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/merchant-server /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 监控和日志

### 1. 日志配置

```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/merchant-server << EOF
/opt/merchant-server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 merchant merchant
    postrotate
        systemctl reload merchant-server
    endscript
}
EOF
```

### 2. 监控脚本

创建监控脚本 `/opt/merchant-server/scripts/monitor.sh`：

```bash
#!/bin/bash

# 检查服务状态
check_service() {
    if ! systemctl is-active --quiet merchant-server; then
        echo "$(date): Merchant server is down, restarting..." >> /var/log/merchant-monitor.log
        systemctl restart merchant-server
    fi
}

# 检查端口
check_port() {
    if ! netstat -tuln | grep -q ":9000 "; then
        echo "$(date): Port 9000 is not listening" >> /var/log/merchant-monitor.log
        systemctl restart merchant-server
    fi
}

# 检查数据库连接
check_database() {
    if ! mysqladmin ping -h localhost -u merchant_user -p'your_password' &>/dev/null; then
        echo "$(date): Database connection failed" >> /var/log/merchant-monitor.log
    fi
}

check_service
check_port
check_database
```

### 3. 设置定时任务

```bash
# 添加到 crontab
sudo crontab -e

# 每分钟检查一次
* * * * * /opt/merchant-server/scripts/monitor.sh
```

## 安全配置

### 1. 防火墙设置

```bash
# Ubuntu (UFW)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 9000/tcp  # 只允许内部访问
sudo ufw enable

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. 数据库安全

```sql
-- 限制数据库用户权限
REVOKE ALL PRIVILEGES ON *.* FROM 'merchant_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON merchant_server.* TO 'merchant_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 文件权限

```bash
# 设置正确的文件权限
sudo chmod 750 /opt/merchant-server
sudo chmod 640 /opt/merchant-server/config/config.yaml
sudo chmod 755 /opt/merchant-server/merchant-server
sudo chmod 755 /opt/merchant-server/scripts/*.sh
```

## 备份策略

### 1. 数据库备份

创建备份脚本 `/opt/merchant-server/scripts/backup.sh`：

```bash
#!/bin/bash

BACKUP_DIR="/opt/backups/merchant-server"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="merchant_server"
DB_USER="merchant_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# 配置文件备份
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz -C /opt/merchant-server config

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### 2. 设置定时备份

```bash
# 添加到 crontab
sudo crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/merchant-server/scripts/backup.sh
```

## 故障排除

### 1. 常见问题

**服务无法启动**
```bash
# 检查日志
sudo journalctl -u merchant-server -f

# 检查配置文件
sudo -u merchant /opt/merchant-server/merchant-server --help
```

**数据库连接失败**
```bash
# 测试数据库连接
mysql -u merchant_user -p -h localhost merchant_server

# 检查数据库状态
sudo systemctl status mysql
```

**端口被占用**
```bash
# 查看端口占用
sudo netstat -tulpn | grep :9000
sudo lsof -i :9000
```

### 2. 性能优化

**数据库优化**
```sql
-- 添加索引
CREATE INDEX idx_merchant_deposits_merchant_id ON merchant_deposits(merchant_id);
CREATE INDEX idx_merchant_deposits_created_at ON merchant_deposits(created_at);
CREATE INDEX idx_merchant_withdraws_merchant_id ON merchant_withdraws(merchant_id);
CREATE INDEX idx_merchant_withdraws_order_no ON merchant_withdraws(order_no);
```

**应用优化**
```yaml
# config/config.yaml
database:
  default:
    maxIdle: 20
    maxOpen: 200
    maxLifetime: "60s"
```

## 更新部署

### 1. 滚动更新

```bash
# 备份当前版本
sudo cp /opt/merchant-server/merchant-server /opt/merchant-server/merchant-server.backup

# 部署新版本
sudo systemctl stop merchant-server
sudo cp merchant-server /opt/merchant-server/
sudo systemctl start merchant-server

# 检查状态
sudo systemctl status merchant-server

# 如果有问题，回滚
# sudo cp /opt/merchant-server/merchant-server.backup /opt/merchant-server/merchant-server
# sudo systemctl restart merchant-server
```

### 2. 蓝绿部署

```bash
# 准备新环境
sudo cp -r /opt/merchant-server /opt/merchant-server-new
# 更新新环境的配置和二进制文件
# 测试新环境
# 切换 Nginx 配置到新环境
# 停止旧环境
```

## 联系支持

如有部署问题，请联系运维团队：

- **邮箱**: <EMAIL>
- **电话**: +86-400-123-4567
- **工作时间**: 7x24小时
