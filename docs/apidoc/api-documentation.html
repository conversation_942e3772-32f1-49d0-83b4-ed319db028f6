<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-Pay 商户平台 API 接口文档</title>
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --bg-color: #f9fafb;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
            --code-bg: #1e293b;
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Header */
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header .subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
            margin-top: 0.25rem;
        }

        /* Layout */
        .container {
            display: flex;
            margin-top: 60px;
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background-color: white;
            border-right: 1px solid var(--border-color);
            position: fixed;
            top: 60px;
            bottom: 0;
            overflow-y: auto;
            padding: 1.5rem;
        }

        .sidebar h3 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: 0.75rem;
            letter-spacing: 0.05em;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-link {
            display: block;
            padding: 0.5rem 0.75rem;
            color: var(--text-color);
            text-decoration: none;
            border-radius: 0.375rem;
            transition: all 0.2s;
            font-size: 0.875rem;
        }

        .nav-link:hover {
            background-color: var(--bg-color);
            color: var(--secondary-color);
        }

        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }

        .nav-link.sub-link {
            padding-left: 2rem;
            font-size: 0.813rem;
        }

        /* Main Content */
        .content {
            margin-left: var(--sidebar-width);
            padding: 2rem;
            margin-top: 2%;
            max-width: 1200px;
        }

        .section {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        h2 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            color: var(--text-color);
        }

        p {
            margin-bottom: 1rem;
        }

        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--bg-color);
            font-weight: 600;
            color: var(--text-color);
        }

        tbody tr:hover {
            background-color: #f9fafb;
        }

        /* Code blocks */
        pre {
            background-color: var(--code-bg);
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.875rem;
        }

        .inline-code {
            background-color: #e5e7eb;
            color: var(--text-color);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        /* API Endpoint styling */
        .endpoint {
            background-color: #f3f4f6;
            border-left: 4px solid var(--secondary-color);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }

        .endpoint .method {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 0.5rem;
        }

        .method.post {
            background-color: var(--accent-color);
            color: white;
        }

        .method.get {
            background-color: var(--secondary-color);
            color: white;
        }

        .endpoint .url {
            font-family: monospace;
            font-size: 0.875rem;
            color: var(--text-color);
        }

        /* Badges and labels */
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .badge.required {
            background-color: var(--danger-color);
            color: white;
        }

        .badge.optional {
            background-color: #6b7280;
            color: white;
        }

        /* Alert boxes */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }

        .alert.info {
            background-color: #dbeafe;
            border-left: 4px solid var(--secondary-color);
            color: #1e40af;
        }

        .alert.warning {
            background-color: #fef3c7;
            border-left: 4px solid var(--warning-color);
            color: #92400e;
        }

        .alert.danger {
            background-color: #fee2e2;
            border-left: 4px solid var(--danger-color);
            color: #991b1b;
        }

        .alert.success {
            background-color: #d1fae5;
            border-left: 4px solid var(--accent-color);
            color: #065f46;
        }

        /* Collapsible sections */
        .collapsible {
            background-color: #f3f4f6;
            cursor: pointer;
            padding: 1rem;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1rem;
            font-weight: 600;
            transition: 0.3s;
            border-radius: 0.375rem;
            margin-top: 0.5rem;
        }

        .collapsible:hover {
            background-color: #e5e7eb;
        }

        .collapsible:after {
            content: '\25BC';
            float: right;
            transition: transform 0.3s;
        }

        .collapsible.active:after {
            transform: rotate(180deg);
        }

        .collapsible-content {
            padding: 0 1rem;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .content {
                margin-left: 0;
                padding: 1rem;
            }

            .section {
                padding: 1rem;
            }
        }

        /* Scroll to top button */
        .scroll-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--secondary-color);
            color: white;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .scroll-top.visible {
            opacity: 1;
        }

        .scroll-top:hover {
            background-color: var(--primary-color);
        }

        /* Copy button for code blocks */
        .code-wrapper {
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.25rem 0.5rem;
            background-color: #4b5563;
            color: white;
            border: none;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .copy-btn:hover {
            background-color: #6b7280;
        }

        .copy-btn.copied {
            background-color: var(--accent-color);
        }

        /* Tab container */
        .tabs {
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 1rem;
        }

        .tab-button {
            background-color: transparent;
            border: none;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s;
            border-bottom: 2px solid transparent;
            margin-bottom: -2px;
        }

        .tab-button:hover {
            color: var(--text-color);
        }

        .tab-button.active {
            color: var(--secondary-color);
            border-bottom-color: var(--secondary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <h1>X-Pay 商户平台 API 接口文档</h1>
        <div class="subtitle"> API 对接指南</div>
    </header>

    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="nav-section">
            <h3>快速开始</h3>
            <a href="#overview" class="nav-link">项目概述</a>
            <a href="#authentication" class="nav-link">认证机制</a>
            <a href="#common-structures" class="nav-link">通用数据结构</a>
        </div>

        <div class="nav-section">
            <h3>核心功能</h3>
            <a href="#deposits" class="nav-link">充值管理</a>
            <a href="#deposits-address" class="nav-link sub-link">获取充值地址</a>
            <a href="#deposits-query" class="nav-link sub-link">查询充值记录</a>
            <a href="#withdrawals" class="nav-link">提现管理</a>
            <a href="#withdrawals-list" class="nav-link sub-link">查询提现记录</a>
            <a href="#withdrawals-detail" class="nav-link sub-link">获取提现详情</a>
            <a href="#auth-payment" class="nav-link">授权支付</a>
            <a href="#auth-payment-create" class="nav-link sub-link">创建授权订单</a>
            <a href="#auth-payment-query" class="nav-link sub-link">查询授权订单</a>
            <a href="#transactions" class="nav-link">交易查询</a>
        </div>

        <div class="nav-section">
            <h3>开发指南</h3>
            <a href="#error-handling" class="nav-link">错误处理</a>
            <a href="#best-practices" class="nav-link">最佳实践</a>
            <a href="#testing" class="nav-link">测试指南</a>
            <a href="#test-examples" class="nav-link">测试代码示例</a>
            <a href="#faq" class="nav-link">常见问题</a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="content">
        <!-- Overview Section -->
        <section id="overview" class="section">
            <h2>1. 项目概述</h2>
            
            <h3>1.1 平台介绍</h3>
            <p>X-Pay 商户平台是一个专业的区块链支付解决方案，为商户提供完整的数字货币充值、提现、交易查询等服务。平台支持主流区块链网络，提供安全可靠的API接口，帮助商户快速集成数字货币支付功能。</p>

            <h3>1.2 核心功能</h3>
            <ul>
                <li><strong>充值管理</strong>：自动生成充值地址，支持地址复用，实时监控到账状态</li>
                <li><strong>提现管理</strong>：灵活的提现申请流程，支持手续费预估和状态跟踪</li>
                <li><strong>授权支付</strong>：支持商户加款和用户扣款的授权支付模式</li>
                <li><strong>交易查询</strong>：统一的交易记录查询，支持多维度筛选和统计分析</li>
                <li><strong>安全保障</strong>：HMAC-SHA256签名验证，防重放攻击，IP白名单控制</li>
            </ul>

            <h3>1.3 支持的区块链网络</h3>
            <table>
                <thead>
                    <tr>
                        <th>区块链</th>
                        <th>网络代码</th>
                        <th>支持代币类型</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>波场</td>
                        <td>TRX</td>
                        <td>原生TRX</td>
                        <td>波场原生币</td>
                    </tr>
                    <tr>
                        <td>波场</td>
                        <td>TRC20</td>
                        <td>TRC20代币</td>
                        <td>如USDT等TRC20标准代币</td>
                    </tr>
                    <tr>
                        <td>以太坊</td>
                        <td>ETH</td>
                        <td>原生ETH</td>
                        <td>以太坊原生币</td>
                    </tr>
                    <tr>
                        <td>以太坊</td>
                        <td>ERC20</td>
                        <td>ERC20代币</td>
                        <td>如USDT等ERC20标准代币</td>
                    </tr>
                </tbody>
            </table>

            <h3>1.4 服务端点</h3>
            <div class="alert info">
                <p><strong>生产环境</strong>：<code class="inline-code">https://api.xpay.com</code></p>
                <p><strong>测试环境</strong>：<code class="inline-code">http://localhost:9000</code></p>
                <p><strong>Swagger文档</strong>：<code class="inline-code">{base_url}/swagger/</code></p>
                <p><strong>OpenAPI规范</strong>：<code class="inline-code">{base_url}/api.json</code></p>
            </div>
        </section>

        <!-- Authentication Section -->
        <section id="authentication" class="section">
            <h2>2. 认证机制</h2>

            <h3>2.1 认证概述</h3>
            <p>X-Pay API 使用基于 HMAC-SHA256 的签名认证机制，确保请求的完整性和安全性。所有API请求（除健康检查接口外）都需要进行签名验证。</p>

            <h3>2.2 获取API密钥</h3>
            <p>商户需要先在X-Pay管理后台申请API密钥对：</p>
            <ul>
                <li><strong>API Key</strong>：用于标识商户身份的公钥</li>
                <li><strong>Secret Hash</strong>：用于签名计算的私钥（请妥善保管，不要在客户端代码中暴露）</li>
            </ul>

            <h3>2.3 请求头参数</h3>
            <p>每个API请求需要包含以下认证头：</p>
            <table>
                <thead>
                    <tr>
                        <th>头部名称</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Content-Type</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>固定值：<code class="inline-code">application/json</code></td>
                    </tr>
                    <tr>
                        <td>X-API-Key</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>商户API密钥</td>
                    </tr>
                    <tr>
                        <td>X-Timestamp</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>Unix时间戳（秒），用于防重放攻击</td>
                    </tr>
                    <tr>
                        <td>X-Nonce</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>随机字符串，建议使用Base64编码</td>
                    </tr>
                    <tr>
                        <td>X-Signature</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>HMAC-SHA256签名</td>
                    </tr>
                </tbody>
            </table>

            <h3>2.4 签名生成流程</h3>
            
            <h4>2.4.1 签名字符串构建</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>签名字符串 = HTTP方法 + URI路径 + 时间戳 + 随机数 + 请求体</code></pre>
            </div>

            <p><strong>示例：</strong></p>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>POST/api/v1/deposits/address1641234567890random123{"user_label":"test","chain":"TRX","token":"native"}</code></pre>
            </div>

            <h4>2.4.2 HMAC-SHA256签名计算</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>签名 = HEX(HMAC-SHA256(签名字符串, Secret Hash))</code></pre>
            </div>

            <h4>2.4.3 Go语言实现示例</h4>
            <button class="collapsible">查看完整代码示例</button>
            <div class="collapsible-content">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>package main

import (
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
)

// 生成HMAC签名
func generateSignature(method, uri, timestamp, nonce, body, secretHash string) string {
    signString := method + uri + timestamp + nonce + body
    h := hmac.New(sha256.New, []byte(secretHash))
    h.Write([]byte(signString))
    return hex.EncodeToString(h.Sum(nil))
}</code></pre>
                </div>
            </div>

            <div class="alert warning">
                <p><strong>时间戳验证</strong></p>
                <ul>
                    <li>时间戳必须在当前时间前后5分钟内（300秒）</li>
                    <li>用于防止重放攻击</li>
                    <li>建议客户端与服务器时间保持同步</li>
                </ul>
            </div>
        </section>

        <!-- Common Data Structures -->
        <section id="common-structures" class="section">
            <h2>3. 通用数据结构</h2>

            <h3>3.1 统一响应格式</h3>
            <p>所有API接口都使用统一的响应格式：</p>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    // 具体业务数据
  }
}</code></pre>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>字段</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>code</td>
                        <td>int</td>
                        <td>响应码，0表示成功，非0表示错误</td>
                    </tr>
                    <tr>
                        <td>message</td>
                        <td>string</td>
                        <td>响应消息，成功时为"操作成功"，失败时为错误描述</td>
                    </tr>
                    <tr>
                        <td>data</td>
                        <td>object</td>
                        <td>业务数据，成功时包含具体数据，失败时可能为null</td>
                    </tr>
                </tbody>
            </table>

            <h3>3.2 分页参数</h3>
            <div class="tabs">
                <button class="tab-button active" onclick="showTab(event, 'page-request')">请求参数</button>
                <button class="tab-button" onclick="showTab(event, 'page-response')">响应格式</button>
            </div>
            
            <div id="page-request" class="tab-content active">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "page": 1,
  "page_size": 20
}</code></pre>
                </div>
            </div>

            <div id="page-response" class="tab-content">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "total": 100,
  "page": 1,
  "page_size": 20,
  "data": [
    // 数据列表
  ]
}</code></pre>
                </div>
            </div>
        </section>

        <!-- Deposits Section -->
        <section id="deposits" class="section">
            <h2>4. 充值管理模块</h2>

            <h3 id="deposits-address">4.1 获取充值地址</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/deposits/address</span>
            </div>

            <p>为用户生成或获取充值地址，支持地址复用。</p>

            <h4>请求参数</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "user_label": "user_001",
  "chain": "TRX",
  "token": "native"
}</code></pre>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>user_label</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>用户标识，用于地址复用，1-100字符</td>
                    </tr>
                    <tr>
                        <td>chain</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>区块链类型（TRX/TRC20/ETH/ERC20）</td>
                    </tr>
                    <tr>
                        <td>token</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>代币类型，原生币填"native"，代币填合约地址</td>
                    </tr>
                </tbody>
            </table>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "address": "TJ1e7PStAbJnzy2qrATskNJ3RawWn8KaKc",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAA...",
    "user_label": "user_001",
    "chain": "TRX",
    "token": "native",
    "created_at": "2024-01-01 12:00:00",
    "is_reused": false,
    "min_deposit": "1.0"
  }
}</code></pre>
            </div>

            <div class="alert info">
                <p><strong>地址复用逻辑</strong></p>
                <ul>
                    <li>相同商户 + 相同用户标识 + 相同链 + 相同代币 = 复用已有地址</li>
                    <li>如果没有已绑定地址，则从地址池中分配新地址</li>
                    <li>地址一旦分配给用户，将永久绑定</li>
                </ul>
            </div>

            <h3 id="deposits-query">4.2 查询充值记录</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/deposits</span>
            </div>

            <p>分页查询充值记录，支持多种筛选条件。</p>

            <button class="collapsible">查看请求参数</button>
            <div class="collapsible-content">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "page": 1,
  "page_size": 10,
  "start_time": "2024-01-01 00:00:00",
  "end_time": "2024-12-31 23:59:59",
  "user_label": "user_001",
  "address": "TJ1e7PStAbJnzy2qrATskNJ3RawWn8KaKc",
  "chain": "TRX",
  "token": "native",
  "status": "confirmed",
  "tx_hash": "0x1234...",
  "min_amount": "1.0",
  "max_amount": "1000.0"
}</code></pre>
                </div>
            </div>
        </section>

        <!-- Withdrawals Section -->
        <section id="withdrawals" class="section">
            <h2>5. 提现管理模块</h2>

            
            <h3 id="withdrawals-list">5.1 查询提现记录</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/withdraws</span>
            </div>

            <p>分页查询提现记录，支持多种筛选条件。</p>

            <h4>请求参数</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "page": 1,
  "page_size": 10,
  "start_time": "2024-01-01 00:00:00",
  "end_time": "2024-12-31 23:59:59",
  "user_label": "user_001",
  "to_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
  "chain": "TRX",
  "token": "native",
  "status": "completed",
  "order_id": "ORDER_20240101_001",
  "tx_hash": "0x1234...",
  "min_amount": "1.0",
  "max_amount": "1000.0"
}</code></pre>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>page</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>页码，从1开始</td>
                    </tr>
                    <tr>
                        <td>page_size</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>每页数量，最大100</td>
                    </tr>
                    <tr>
                        <td>start_time</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>查询开始时间</td>
                    </tr>
                    <tr>
                        <td>end_time</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>查询结束时间</td>
                    </tr>
                    <tr>
                        <td>user_label</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>用户标识</td>
                    </tr>
                    <tr>
                        <td>to_address</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>提现目标地址</td>
                    </tr>
                    <tr>
                        <td>chain</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>区块链类型</td>
                    </tr>
                    <tr>
                        <td>token</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>代币类型</td>
                    </tr>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>提现状态</td>
                    </tr>
                    <tr>
                        <td>order_id</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>商户订单号</td>
                    </tr>
                    <tr>
                        <td>tx_hash</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>交易哈希</td>
                    </tr>
                    <tr>
                        <td>min_amount</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>最小金额</td>
                    </tr>
                    <tr>
                        <td>max_amount</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>最大金额</td>
                    </tr>
                </tbody>
            </table>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": {
      "total": 100,
      "page": 1,
      "page_size": 10,
      "data": [
        {
          "id": 12345,
          "merchant_id": 1001,
          "order_id": "ORDER_20240101_001",
          "user_label": "user_001",
          "to_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
          "chain": "TRX",
          "token": "native",
          "amount": "100.5",
          "fee": "2.5",
          "net_amount": "98.0",
          "status": "completed",
          "tx_hash": "0x1234567890abcdef...",
          "memo": "提现到钱包",
          "created_at": "2024-01-01 12:00:00",
          "updated_at": "2024-01-01 12:30:00",
          "completed_at": "2024-01-01 12:30:00"
        }
      ]
    }
  }
}</code></pre>
            </div>

            <h4>提现状态说明</h4>
            <table>
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code class="inline-code">pending</code></td>
                        <td>待处理</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">processing</code></td>
                        <td>处理中</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">completed</code></td>
                        <td>已完成</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">failed</code></td>
                        <td>失败</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">cancelled</code></td>
                        <td>已取消</td>
                    </tr>
                </tbody>
            </table>

            <h3 id="withdrawals-detail">5.2 获取提现详情</h3>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/v1/withdraws/detail</span>
            </div>

            <p>获取单个提现记录的详细信息。</p>

            <h4>请求参数</h4>
            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>提现记录ID</td>
                    </tr>
                </tbody>
            </table>

            <h4>请求示例</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>GET /api/v1/withdraws/detail?id=12345</code></pre>
            </div>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": {
      "id": 12345,
      "merchant_id": 1001,
      "order_id": "ORDER_20240101_001",
      "user_label": "user_001",
      "to_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
      "chain": "TRX",
      "token": "native",
      "amount": "100.5",
      "currency": "TRX",
      "fee": "2.5",
      "fee_currency": "TRX",
      "net_amount": "98.0",
      "status": "completed",
      "tx_hash": "0x1234567890abcdef...",
      "block_number": "12345678",
      "block_time": "2024-01-01 12:30:00",
      "confirmations": 30,
      "memo": "提现到钱包",
      "callback_url": "https://yoursite.com/withdraw/callback",
      "callback_status": "success",
      "callback_time": "2024-01-01 12:31:00",
      "created_at": "2024-01-01 12:00:00",
      "updated_at": "2024-01-01 12:30:00",
      "completed_at": "2024-01-01 12:30:00",
      "error_msg": null
    }
  }
}</code></pre>
            </div>

            <div class="alert info">
                <p><strong>提示</strong></p>
                <ul>
                    <li>详情信息包含完整的提现数据，包括区块信息和回调状态</li>
                    <li>只能查询本商户的提现记录</li>
                    <li>如果记录不存在或不属于本商户，将返回错误</li>
                </ul>
            </div>
        </section>

        <!-- Authorization Payment Section -->
        <section id="auth-payment" class="section">
            <h2>6. 授权支付模块</h2>
            <div class="alert info">
                <p><strong>新功能</strong>：授权支付功能支持商户直接向用户账户加款或从用户账户扣款的操作模式。</p>
            </div>

            <h3 id="auth-payment-create">6.1 创建授权支付订单</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/auth-payment/create</span>
            </div>

            <p>创建授权支付订单，支持加款（商户向用户）和扣款（用户向商户）两种类型。</p>

            <div class="tabs">
                <button class="tab-button active" onclick="showTab(event, 'auth-add')">加款示例</button>
                <button class="tab-button" onclick="showTab(event, 'auth-deduct')">扣款示例</button>
            </div>
            
            <div id="auth-add" class="tab-content active">
                <h4>加款请求示例</h4>
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "userAccount": "**********",
  "orderType": "add",
  "tokenSymbol": "USDT",
  "amount": "100.50",
  "authReason": "活动奖励发放",
  "merchantOrderNo": "REWARD_20240101_001"
}</code></pre>
                </div>
            </div>

            <div id="auth-deduct" class="tab-content">
                <h4>扣款请求示例</h4>
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "userAccount": "**********",
  "orderType": "deduct",
  "tokenSymbol": "USDT",
  "amount": "50.00",
  "authReason": "服务费用扣除",
  "merchantOrderNo": "SERVICE_20240101_001",
  "expireMinutes": 60,
  "callbackUrl": "https://merchant.com/payment/callback"
}</code></pre>
                </div>
            </div>

            <h4>请求参数说明</h4>
            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>userAccount</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>用户账户标识</td>
                    </tr>
                    <tr>
                        <td>orderType</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>订单类型：add（加款）、deduct（扣款）</td>
                    </tr>
                    <tr>
                        <td>tokenSymbol</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>代币符号，如 USDT</td>
                    </tr>
                    <tr>
                        <td>amount</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>金额</td>
                    </tr>
                    <tr>
                        <td>authReason</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>授权原因说明</td>
                    </tr>
                    <tr>
                        <td>merchantOrderNo</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>商户订单号，需保证唯一</td>
                    </tr>
                    <tr>
                        <td>expireMinutes</td>
                        <td>int</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>订单过期时间（分钟），仅扣款订单需要</td>
                    </tr>
                    <tr>
                        <td>callbackUrl</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>回调通知URL</td>
                    </tr>
                </tbody>
            </table>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": {
      "orderNo": "**********************",
      "merchantOrderNo": "REWARD_20240101_001",
      "userAccount": "**********",
      "orderType": "add",
      "tokenSymbol": "USDT",
      "amount": "100.50",
      "status": "completed",
      "createdAt": "2025-01-09 12:05:30",
      "completedAt": "2025-01-09 12:05:31"
    }
  }
}</code></pre>
            </div>

            <h3 id="auth-payment-query">6.2 查询授权支付订单</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/auth-payment/query</span>
            </div>

            <p>通过系统订单号或商户订单号查询授权支付订单详情。</p>

            <div class="tabs">
                <button class="tab-button active" onclick="showTab(event, 'query-system')">通过系统订单号</button>
                <button class="tab-button" onclick="showTab(event, 'query-merchant')">通过商户订单号</button>
            </div>
            
            <div id="query-system" class="tab-content active">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "orderNo": "**********************"
}</code></pre>
                </div>
            </div>

            <div id="query-merchant" class="tab-content">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "merchantOrderNo": "REWARD_20240101_001"
}</code></pre>
                </div>
            </div>

            <h3>6.3 授权支付订单列表</h3>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/v1/auth-payment/list</span>
            </div>

            <p>分页查询授权支付订单列表，支持多种筛选条件。</p>

            <button class="collapsible">查看请求示例</button>
            <div class="collapsible-content">
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>{
  "page": 1,
  "page_size": 10,
  "orderType": "add",
  "status": "completed",
  "userAccount": "**********",
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-12-31 23:59:59"
}</code></pre>
                </div>
            </div>

            <h4>订单状态说明</h4>
            <table>
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>说明</th>
                        <th>适用类型</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code class="inline-code">pending</code></td>
                        <td>待处理</td>
                        <td>扣款订单</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">processing</code></td>
                        <td>处理中</td>
                        <td>所有类型</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">completed</code></td>
                        <td>已完成</td>
                        <td>所有类型</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">failed</code></td>
                        <td>失败</td>
                        <td>所有类型</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">expired</code></td>
                        <td>已过期</td>
                        <td>扣款订单</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Transactions Section -->
        <section id="transactions" class="section">
            <h2>7. 交易查询模块</h2>
            
            <p>交易查询模块提供统一的交易记录查询功能，支持查询充值、提现等各类交易记录。</p>

            <h3 id="transactions-list">7.1 查询交易记录</h3>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/v1/transactions</span>
            </div>

            <p>分页查询交易记录，支持多种筛选条件。</p>

            <h4>请求参数</h4>
            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>page</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>页码，从1开始</td>
                    </tr>
                    <tr>
                        <td>page_size</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>每页数量，最大100</td>
                    </tr>
                    <tr>
                        <td>type</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>交易类型：deposit（充值）、withdraw（提现）</td>
                    </tr>
                    <tr>
                        <td>start_time</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>查询开始时间，格式：YYYY-MM-DD HH:mm:ss</td>
                    </tr>
                    <tr>
                        <td>end_time</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>查询结束时间，格式：YYYY-MM-DD HH:mm:ss</td>
                    </tr>
                    <tr>
                        <td>user_label</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>用户标识</td>
                    </tr>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>交易状态：pending、processing、completed、failed</td>
                    </tr>
                    <tr>
                        <td>chain</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>区块链类型</td>
                    </tr>
                    <tr>
                        <td>token</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>代币类型</td>
                    </tr>
                    <tr>
                        <td>tx_hash</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>交易哈希</td>
                    </tr>
                    <tr>
                        <td>min_amount</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>最小金额</td>
                    </tr>
                    <tr>
                        <td>max_amount</td>
                        <td>string</td>
                        <td><span class="badge optional">可选</span></td>
                        <td>最大金额</td>
                    </tr>
                </tbody>
            </table>

            <h4>请求示例</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>GET /api/v1/transactions?page=1&page_size=10&type=deposit&start_time=2024-01-01 00:00:00</code></pre>
            </div>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": {
      "total": 100,
      "page": 1,
      "page_size": 10,
      "data": [
        {
          "id": 12345,
          "merchant_id": 1001,
          "type": "deposit",
          "user_label": "user_001",
          "order_id": "DEP_20240101_001",
          "chain": "TRX",
          "token": "native",
          "currency": "TRX",
          "amount": "100.5",
          "fee": "0",
          "status": "completed",
          "from_address": "TJ1e7PStAbJnzy2qrATskNJ3RawWn8KaKc",
          "to_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
          "tx_hash": "0x1234567890abcdef...",
          "block_number": "12345678",
          "confirmations": 30,
          "memo": "",
          "created_at": "2024-01-01 12:00:00",
          "updated_at": "2024-01-01 12:05:00",
          "completed_at": "2024-01-01 12:05:00"
        }
      ]
    }
  }
}</code></pre>
            </div>

            <h3 id="transactions-detail">7.2 获取交易详情</h3>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/v1/transactions/detail</span>
            </div>

            <p>根据交易ID获取单条交易记录的详细信息。</p>

            <h4>请求参数</h4>
            <table>
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td>int</td>
                        <td><span class="badge required">必填</span></td>
                        <td>交易记录ID</td>
                    </tr>
                    <tr>
                        <td>type</td>
                        <td>string</td>
                        <td><span class="badge required">必填</span></td>
                        <td>交易类型：deposit（充值）、withdraw（提现）</td>
                    </tr>
                </tbody>
            </table>

            <h4>请求示例</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>GET /api/v1/transactions/detail?id=12345&type=deposit</code></pre>
            </div>

            <h4>响应数据</h4>
            <div class="code-wrapper">
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
                <pre><code>{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": {
      "id": 12345,
      "merchant_id": 1001,
      "type": "deposit",
      "user_label": "user_001",
      "order_id": "DEP_20240101_001",
      "chain": "TRX",
      "token": "native",
      "token_contract": "",
      "currency": "TRX",
      "amount": "100.5",
      "fee": "0",
      "net_amount": "100.5",
      "status": "completed",
      "from_address": "TJ1e7PStAbJnzy2qrATskNJ3RawWn8KaKc",
      "to_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
      "tx_hash": "0x1234567890abcdef...",
      "block_number": "12345678",
      "block_time": "2024-01-01 12:05:00",
      "confirmations": 30,
      "required_confirmations": 20,
      "memo": "",
      "callback_url": "https://yoursite.com/deposit/callback",
      "callback_status": "success",
      "callback_time": "2024-01-01 12:06:00",
      "created_at": "2024-01-01 12:00:00",
      "updated_at": "2024-01-01 12:05:00",
      "completed_at": "2024-01-01 12:05:00",
      "extra_data": {}
    }
  }
}</code></pre>
            </div>

            <div class="alert info">
                <p><strong>交易类型说明</strong></p>
                <ul>
                    <li><code class="inline-code">deposit</code>：充值交易，资金从外部地址转入商户地址</li>
                    <li><code class="inline-code">withdraw</code>：提现交易，资金从商户地址转出到外部地址</li>
                </ul>
            </div>

            <h4>交易状态说明</h4>
            <table>
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>说明</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code class="inline-code">pending</code></td>
                        <td>待处理</td>
                        <td>交易已创建，等待区块链确认</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">processing</code></td>
                        <td>处理中</td>
                        <td>交易已广播，等待确认数达标</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">completed</code></td>
                        <td>已完成</td>
                        <td>交易确认数已达标，处理完成</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">failed</code></td>
                        <td>失败</td>
                        <td>交易失败或被拒绝</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Error Handling Section -->
        <section id="error-handling" class="section">
            <h2>9. 错误处理与状态码</h2>

            <h3>9.1 HTTP状态码</h3>
            <table>
                <thead>
                    <tr>
                        <th>状态码</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>200</td>
                        <td>请求成功</td>
                    </tr>
                    <tr>
                        <td>400</td>
                        <td>请求参数错误</td>
                    </tr>
                    <tr>
                        <td>401</td>
                        <td>认证失败</td>
                    </tr>
                    <tr>
                        <td>403</td>
                        <td>权限不足</td>
                    </tr>
                    <tr>
                        <td>404</td>
                        <td>资源不存在</td>
                    </tr>
                    <tr>
                        <td>500</td>
                        <td>服务器内部错误</td>
                    </tr>
                </tbody>
            </table>

            <h3>9.2 业务错误码</h3>
            <table>
                <thead>
                    <tr>
                        <th>错误码</th>
                        <th>错误消息</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0</td>
                        <td>操作成功</td>
                        <td>请求处理成功</td>
                    </tr>
                    <tr>
                        <td>40001</td>
                        <td>参数错误</td>
                        <td>请求参数格式或值错误</td>
                    </tr>
                    <tr>
                        <td>40101</td>
                        <td>未授权访问</td>
                        <td>API密钥无效或签名错误</td>
                    </tr>
                    <tr>
                        <td>40301</td>
                        <td>禁止访问</td>
                        <td>IP不在白名单或权限不足</td>
                    </tr>
                    <tr>
                        <td>40401</td>
                        <td>资源不存在</td>
                        <td>查询的记录不存在</td>
                    </tr>
                    <tr>
                        <td>50001</td>
                        <td>内部服务错误</td>
                        <td>服务器内部错误</td>
                    </tr>
                    <tr>
                        <td>60001</td>
                        <td>余额不足</td>
                        <td>提现金额超过可用余额</td>
                    </tr>
                </tbody>
            </table>
        </section>

 
        <!-- Best Practices Section -->
        <section id="best-practices" class="section">
            <h2>11. 最佳实践指南</h2>

            <h3>11.1 API调用频率限制</h3>
            <ul>
                <li>单个API Key限制：1000次/分钟</li>
                <li>单个IP限制：5000次/分钟</li>
                <li>建议实现客户端限流和重试机制</li>
            </ul>

            <h3>11.2 幂等性设计</h3>
            <ul>
                <li><strong>充值地址获取</strong>：相同参数返回相同地址</li>
                <li><strong>提现申请</strong>：使用唯一订单号防止重复提现</li>
                <li><strong>授权支付</strong>：使用唯一商户订单号防止重复创建</li>
                <li><strong>查询操作</strong>：天然幂等</li>
            </ul>

            <h3>11.3 安全注意事项</h3>
            <div class="alert danger">
                <p><strong>密钥安全</strong></p>
                <ul>
                    <li>Secret Hash只在服务端使用，永远不要在客户端代码中暴露</li>
                    <li>定期轮换API密钥</li>
                    <li>监控异常API调用</li>
                    <li>使用HTTPS协议传输</li>
                </ul>
            </div>
        </section>

        <!-- Testing Section -->
        <section id="testing" class="section">
            <h2>12. 测试指南</h2>

            <h3>12.1 测试环境配置</h3>
            <div class="alert info">
                <p><strong>服务端点：</strong> <code class="inline-code">http://localhost:9000</code></p>
                <p><strong>测试API密钥：</strong></p>
                <ul>
                    <li>API Key: <code class="inline-code">MK5Tn99YdKVTi5_4n1rTfkpAkytul0tMPtOooB_4Ndc=</code></li>
                    <li>Secret Hash: <code class="inline-code">$2a$10$UWtFj3OcABBDYvU008nPRuHZamzFjQUbhLDf1vs3oWAClV6FYLD7S</code></li>
                </ul>
            </div>

            <h3>12.2 测试用例说明</h3>
            <table>
                <thead>
                    <tr>
                        <th>测试文件</th>
                        <th>功能描述</th>
                        <th>主要接口</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code class="inline-code">test_deposit_apis.go</code></td>
                        <td>充值API综合测试</td>
                        <td>充值地址、记录查询、详情、地址列表</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">test_address_reuse.go</code></td>
                        <td>地址复用功能测试</td>
                        <td>充值地址复用逻辑</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">test_withdraw_comprehensive.go</code></td>
                        <td>提现API综合测试</td>
                        <td>手续费、申请、查询、详情、取消</td>
                    </tr>
                    <tr>
                        <td><code class="inline-code">test_auth_payment_apis.go</code></td>
                        <td>授权支付API测试</td>
                        <td>创建订单、查询订单、订单列表</td>
                    </tr>
                </tbody>
            </table>

        </section>

        <!-- Test Examples Section -->
        <section id="test-examples" class="section">
            <h2>13. 测试代码示例</h2>
            
          
            <h3>可用的测试文件</h3>
            <p>以下是完整的 API 测试示例代码，展示了如何调用各个接口：</p>

            <div style="margin-top: 1.5rem;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr>
                            <th style="text-align: left; padding: 0.75rem; border-bottom: 2px solid var(--border-color);">测试文件</th>
                            <th style="text-align: left; padding: 0.75rem; border-bottom: 2px solid var(--border-color);">功能描述</th>
                            <th style="text-align: center; padding: 0.75rem; border-bottom: 2px solid var(--border-color);">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <code class="inline-code">test_auth_payment_apis.go</code>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                授权支付 API 测试 - 包含加款和扣款订单的完整测试流程
                            </td>
                            <td style="text-align: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <a href="test_auth_payment_apis.go" target="_blank" style="color: var(--secondary-color); text-decoration: none;">
                                    📄 查看文件
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <code class="inline-code">test_deposit_apis.go</code>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                充值管理 API 测试 - 地址获取、记录查询等功能测试
                            </td>
                            <td style="text-align: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <a href="test_deposit_apis.go" target="_blank" style="color: var(--secondary-color); text-decoration: none;">
                                    📄 查看文件
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <code class="inline-code">test_transaction_apis.go</code>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                交易查询 API 测试 - 交易记录查询和统计功能测试
                            </td>
                            <td style="text-align: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <a href="test_transaction_apis.go" target="_blank" style="color: var(--secondary-color); text-decoration: none;">
                                    📄 查看文件
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <code class="inline-code">test_withdraw_comprehensive.go</code>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                提现管理 API 测试 - 手续费查询、提现申请等综合测试
                            </td>
                            <td style="text-align: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <a href="test_withdraw_comprehensive.go" target="_blank" style="color: var(--secondary-color); text-decoration: none;">
                                    📄 查看文件
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <code class="inline-code">callback_receiver.go</code>
                            </td>
                            <td style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                回调通知接收服务 - 处理充值和提现完成的回调通知
                            </td>
                            <td style="text-align: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                                <a href="callback_receiver.go" target="_blank" style="color: var(--secondary-color); text-decoration: none;">
                                    📄 查看文件
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="alert info" style="margin-top: 2rem;">
                <p><strong>提示</strong>：</p>
                <ul>
                    <li>测试文件需要通过 Web 服务器访问才能在浏览器中查看</li>
                    <li>您也可以直接在项目目录中打开这些文件</li>
                    <li>每个测试文件都包含完整的 API 调用示例，包括认证签名、请求构建和响应处理</li>
                </ul>
            </div>

            <div style="margin-top: 2rem; padding: 1.5rem; background-color: #f8f9fa; border-radius: 0.5rem;">
                <h4 style="margin-top: 0;">运行测试文件</h4>
                <p>在项目目录中运行以下命令来执行测试：</p>
                <div class="code-wrapper">
                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                    <pre><code>cd /home/<USER>/merchant/merchant-server
go run docs/apidoc/test_auth_payment_apis.go
go run docs/apidoc/test_deposit_apis.go
go run docs/apidoc/test_transaction_apis.go
go run docs/apidoc/test_withdraw_comprehensive.go</code></pre>
                </div>
            </div>

        </section>

        <!-- FAQ Section -->
        <section id="faq" class="section">
            <h2>14. 常见问题 FAQ</h2>

            <button class="collapsible">Q1: 为什么我的API请求返回401错误？</button>
            <div class="collapsible-content">
                <p>401错误通常是认证问题，请检查：</p>
                <ul>
                    <li>API Key是否正确</li>
                    <li>签名算法是否正确实现</li>
                    <li>时间戳是否在有效范围内（±5分钟）</li>
                    <li>随机数是否为空</li>
                </ul>
            </div>

            <button class="collapsible">Q2: 获取充值地址时返回"暂无可用地址"错误？</button>
            <div class="collapsible-content">
                <p>这表示地址池中没有可用地址，请联系管理员补充地址池。</p>
            </div>

            <button class="collapsible">Q3: 为什么相同参数获取到了不同的充值地址？</button>
            <div class="collapsible-content">
                <p>请检查以下参数是否完全一致：</p>
                <ul>
                    <li>user_label（用户标识）</li>
                    <li>chain（区块链类型）</li>
                    <li>token（代币类型）</li>
                </ul>
                <p>只有这三个参数完全相同时，才会复用地址。</p>
            </div>

            <button class="collapsible">Q4: 授权支付订单创建失败？</button>
            <div class="collapsible-content">
                <p>请检查：</p>
                <ul>
                    <li>用户账户是否存在</li>
                    <li>订单类型是否正确（add/deduct）</li>
                    <li>金额格式是否正确（正数）</li>
                    <li>商户订单号是否重复</li>
                    <li>扣款订单是否设置了过期时间</li>
                </ul>
            </div>

            <button class="collapsible">Q5: 回调通知没有收到？</button>
            <div class="collapsible-content">
                <p>请检查：</p>
                <ul>
                    <li>回调地址是否可访问</li>
                    <li>服务器是否返回HTTP 200状态码</li>
                    <li>是否有防火墙阻拦</li>
                    <li>回调地址是否支持POST请求</li>
                </ul>
            </div>
        </section>
    </main>

    <!-- Scroll to top button -->
    <div class="scroll-top" id="scrollTop" onclick="scrollToTop()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 15l-6-6-6 6"/>
        </svg>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);
                
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    
                    // Update active state
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Collapsible sections
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;
                
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        });

        // Code copy functionality
        function copyCode(button) {
            const codeBlock = button.nextElementSibling.querySelector('code');
            const textArea = document.createElement('textarea');
            textArea.value = codeBlock.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            button.textContent = '已复制';
            button.classList.add('copied');
            
            setTimeout(() => {
                button.textContent = '复制';
                button.classList.remove('copied');
            }, 2000);
        }

        // Tab functionality
        function showTab(event, tabId) {
            const tabContents = event.target.parentElement.parentElement.querySelectorAll('.tab-content');
            const tabButtons = event.target.parentElement.querySelectorAll('.tab-button');
            
            tabContents.forEach(content => content.classList.remove('active'));
            tabButtons.forEach(button => button.classList.remove('active'));
            
            document.getElementById(tabId).classList.add('active');
            event.target.classList.add('active');
        }

        // Scroll to top functionality
        window.addEventListener('scroll', function() {
            const scrollTop = document.getElementById('scrollTop');
            if (window.pageYOffset > 300) {
                scrollTop.classList.add('visible');
            } else {
                scrollTop.classList.remove('visible');
            }
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Highlight current section in navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        });

        // Mobile sidebar toggle
        const sidebar = document.getElementById('sidebar');
        let touchStartX = null;
        
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchmove', function(e) {
            if (!touchStartX) return;
            
            const touchEndX = e.touches[0].clientX;
            const diff = touchEndX - touchStartX;
            
            if (diff > 50 && touchStartX < 50) {
                sidebar.classList.add('active');
            } else if (diff < -50) {
                sidebar.classList.remove('active');
            }
        });


        // Initialize syntax highlighting if needed
        if (typeof hljs !== 'undefined') {
            hljs.highlightAll();
        }
    </script>
</body>
</html>