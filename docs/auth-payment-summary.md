# 授权支付逻辑总结

## 一、功能概述

授权支付是商户与用户之间进行资金操作的功能，支持两种操作类型：
- **加款（Add）**：从商户账户扣款，给用户账户加款
- **扣款（Deduct）**：创建待确认订单，等待用户通过Telegram机器人确认后从用户账户扣款

## 二、核心流程

### 2.1 加款流程（Add）

```mermaid
graph TD
    A[商户发起加款请求] --> B[ValidateAuthPayment验证]
    B --> C[ValidateAmountDecimal金额验证]
    C --> D[幂等性检查]
    D --> E[查询用户信息]
    E --> F[验证商户余额是否充足]
    F --> G[生成订单号]
    G --> H[开启事务]
    H --> I[创建订单记录-状态pending]
    I --> J[执行资金转账]
    J --> K[更新订单状态为completed]
    K --> L[创建回调记录]
    L --> M[提交事务]
    M --> N[返回结果]
```

### 2.2 扣款流程（Deduct）

```mermaid
graph TD
    A[商户发起扣款请求] --> B[ValidateAuthPayment验证]
    B --> C[ValidateAmountDecimal金额验证]
    C --> D[幂等性检查]
    D --> E[查询用户信息]
    E --> F[获取用户余额-仅记录日志]
    F --> G[生成订单号]
    G --> H[计算过期时间]
    H --> I[创建订单记录-状态pending]
    I --> J[返回结果含Telegram链接]
    J --> K[用户点击链接确认]
    K --> L[扣款操作-另外的流程]
```

## 三、状态机

订单状态流转：
- `pending` - 待处理（初始状态）
- `completed` - 已完成（加款直接完成/扣款用户确认后完成）
- `expired` - 已过期（扣款订单超时未确认）
- `failed` - 失败（处理过程中出错）
- `cancelled` - 已取消（用户主动取消）

## 四、安全机制

### 4.1 功能开关
- 通过配置项 `auth_pay.state` 控制功能启用/禁用

### 4.2 代币白名单
- 配置项 `auth_pay.token_symbol` 设置允许的代币列表（换行分隔）
- 只有在白名单中的代币才能进行授权支付

### 4.3 幂等性保证
- 使用商户订单号（merchantOrderNo）防止重复提交
- Redis分布式锁（30秒过期）+ 数据库双重检查

### 4.4 金额验证
- 检查金额不为零、不为负数
- 验证小数位数不超过代币配置的decimals
- 金额范围检查（1e-18 到 1e15）
- 代币必须处于激活状态

### 4.5 IP追踪
- 记录每个请求的客户端IP用于审计

## 五、回调机制

### 5.1 回调触发时机
- 加款订单：交易完成后立即触发
- 扣款订单：用户确认后交易完成时触发

### 5.2 回调数据结构
```json
{
  "event_type": "auth_add_completed",
  "order_no": "AP...",
  "merchant_id": 123,
  "amount": "100.00",
  "token_symbol": "USDT",
  "timestamp": 1736404800
}
```

### 5.3 回调状态管理
- `pending` - 待发送
- `success` - 发送成功
- `failed` - 发送失败

### 5.4 重试机制
- 失败后自动重试
- 记录重试次数和下次重试时间

## 六、关键配置项

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| auth_pay.state | 功能开关 | true/false |
| auth_pay.token_symbol | 允许的代币列表 | "USDT\nUSDC" |
| telegram_bot_setting.name | Telegram机器人名称 | "xpay_bot" |

## 七、数据库设计

主表：`auth_payment_orders`
- 唯一索引：order_no, merchant_id+merchant_order_no
- 优化索引：merchant_id, status, created_at等
- 支持回调重试查询的复合索引

## 八、错误处理

### 8.1 常见错误
- 授权支付功能未启用
- 代币不在白名单中
- 用户不存在
- 商户余额不足（加款时）
- 商户订单号已存在（幂等性冲突）

### 8.2 错误恢复
- 事务保证数据一致性
- 失败订单记录错误信息
- 分布式锁自动释放机制

## 九、性能优化

1. **批量操作优化**：支持并发请求处理
2. **缓存策略**：Redis缓存热点数据
3. **索引优化**：针对查询场景建立复合索引
4. **锁粒度**：基于merchantId+merchantOrderNo的细粒度锁

## 十、集成要点

### 10.1 Wallet Service集成
- 商户钱包：使用内部wallet服务
- 用户钱包：使用external wallet服务
- Transfer接口实现原子性转账

### 10.2 Telegram Bot集成
- 生成深度链接供用户确认操作
- 格式：`https://t.me/{botName}?start={orderNo}`

### 10.3 监控与日志
- 详细的操作日志记录
- 关键步骤的Info级别日志
- 错误情况的Error级别日志