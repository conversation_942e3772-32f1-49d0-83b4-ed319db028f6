# X-Pay 商户平台文档中心

## 📚 文档概览

### 主要文档

- **[API接口文档.md](./API接口文档.md)** - 完整的API接口对接文档
- **[项目配置文档](../CLAUDE.md)** - 开发环境配置和项目说明

### 测试文件索引

| 测试文件 | 功能说明 | 路径 |
|----------|----------|------|
| 充值API测试 | 完整的充值功能测试，包含地址获取、记录查询等 | `scripts/test_deposit_apis.go` |
| 地址复用测试 | 验证地址复用逻辑的专项测试 | `scripts/test_address_reuse.go` |
| 提现API测试 | 完整的提现功能测试，包含费用查询、申请、取消等 | `scripts/test_withdraw_comprehensive.go` |
| 交易查询测试 | 交易记录查询和统计功能测试 | `scripts/test_transaction_apis.go` |

## 🚀 快速开始

### 1. 查看API文档
```bash
# 阅读完整的API接口文档
cat docs/API接口文档.md
```

### 2. 运行测试示例
```bash
# 测试充值API
go run scripts/test_deposit_apis.go

# 测试地址复用功能
go run scripts/test_address_reuse.go

# 测试提现API
go run scripts/test_withdraw_comprehensive.go

# 测试交易查询API
go run scripts/test_transaction_apis.go
```

### 3. 启动开发服务器
```bash
# 启动API服务
gf run main.go

# 访问Swagger文档
http://localhost:9000/swagger/

# 访问OpenAPI规范
http://localhost:9000/api.json
```

## 📋 API接口概览

### 充值管理模块
- `POST /api/v1/deposits/address` - 获取充值地址（支持地址复用）
- `POST /api/v1/deposits` - 查询充值记录
- `POST /api/v1/deposits/detail` - 获取充值详情
- `GET /api/v1/deposits/addresses` - 获取地址列表

### 提现管理模块
- `POST /api/v1/withdraws/fee` - 获取提现手续费
- `POST /api/v1/withdraws` - 创建提现申请
- `GET /api/v1/withdraws` - 查询提现记录
- `GET /api/v1/withdraws/detail` - 获取提现详情
- `POST /api/v1/withdraws/cancel` - 取消提现申请

### 交易查询模块
- `GET /api/v1/transactions` - 查询交易记录
- `GET /api/v1/transactions/detail` - 获取交易详情
- `GET /api/v1/transactions/stats` - 获取交易统计

### 系统功能
- `GET /health` - 健康检查（无需认证）

## 🔐 认证说明

所有API（除健康检查外）都需要HMAC-SHA256签名认证，详见[认证机制章节](./API接口文档.md#2-认证机制)。

**测试密钥：**
- API Key: `MK5Tn99YdKVTi5_4n1rTfkpAkytul0tMPtOooB_4Ndc=`
- Secret Hash: `$2a$10$UWtFj3OcABBDYvU008nPRuHZamzFjQUbhLDf1vs3oWAClV6FYLD7S`

## 🌐 支持的区块链

| 区块链 | 网络代码 | 支持代币 |
|--------|----------|----------|
| 波场 | TRX | 原生TRX币 |
| 波场 | TRC20 | TRC20代币（如USDT） |
| 以太坊 | ETH | 原生ETH币 |
| 以太坊 | ERC20 | ERC20代币（如USDT） |

## 📖 使用指南

### 新手入门
1. 阅读[项目概述](./API接口文档.md#1-项目概述)了解平台功能
2. 学习[认证机制](./API接口文档.md#2-认证机制)并配置API密钥
3. 查看[通用数据结构](./API接口文档.md#3-通用数据结构)了解接口规范
4. 运行测试脚本验证环境配置

### 开发集成
1. 根据业务需求选择对应的API模块
2. 参考测试文件实现认证和接口调用
3. 处理错误响应和异常情况
4. 实现回调通知处理逻辑

### 生产部署
1. 申请生产环境API密钥
2. 配置IP白名单和安全策略
3. 实现监控和日志记录
4. 设置回调地址和通知处理

## ❓ 常见问题

详细的FAQ请参考[FAQ章节](./API接口文档.md#12-faq)，包含：
- 认证失败排查
- 地址复用逻辑
- 提现处理时间
- 回调通知问题
- 网络超时处理

## 📞 技术支持

- **完整文档**：[API接口文档.md](./API接口文档.md)
- **测试示例**：`scripts/` 目录下的测试文件
- **配置说明**：[CLAUDE.md](../CLAUDE.md)
- **问题反馈**：通过工单系统或技术支持邮箱

---

**文档维护**：X-Pay 技术团队  
**最后更新**：2024-01-01