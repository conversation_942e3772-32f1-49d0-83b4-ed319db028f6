#!/bin/bash

echo "测试文件日志记录功能..."

# 启动服务器（后台运行）
./main &
SERVER_PID=$!

# 等待服务器启动
sleep 3

# 测试健康检查接口
echo "测试健康检查接口..."
curl -X GET http://localhost:8002/health

# 测试登录接口
echo -e "\n\n测试登录接口..."
curl -X POST http://localhost:8002/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123"}'

# 测试其他接口（假设有商户列表接口）
echo -e "\n\n测试商户列表接口..."
curl -X GET http://localhost:8002/api/v1/merchants \
  -H "Authorization: Bearer test-token"

# 等待一下让日志写入完成
sleep 2

# 显示生成的日志文件
echo -e "\n\n生成的日志文件："
ls -la logs/$(date +%Y-%m-%d)/

# 显示日志内容示例
echo -e "\n\n日志内容示例："
if [ -f "logs/$(date +%Y-%m-%d)/health.log" ]; then
    echo "=== health.log 内容 ==="
    cat "logs/$(date +%Y-%m-%d)/health.log" | jq .
fi

if [ -f "logs/$(date +%Y-%m-%d)/api_v1_login.log" ]; then
    echo -e "\n=== api_v1_login.log 内容 ==="
    cat "logs/$(date +%Y-%m-%d)/api_v1_login.log" | jq .
fi

# 停止服务器
kill $SERVER_PID

echo -e "\n\n测试完成！"