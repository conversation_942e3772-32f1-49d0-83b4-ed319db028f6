
# CLI tool, only in development environment.
# https://goframe.org/docs/cli
gfcli:
  gen:
    dao:
      - link: "mysql:root:root@tcp(127.0.0.1:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4"
        descriptionTag: true
        gJsonSupport: true
        jsonCase: "CamelLower"
        typeMapping:
              decimal:
                type:   decimal.Decimal
                import: github.com/shopspring/decimal
  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app