# 授权支付技术开发文档

## 1. 功能概述

### 1.1 业务背景
平台需要提供授权API让商户支持授权转账和授权提现功能。系统中涉及三方角色：
- **平台（A）**：提供授权支付服务的平台方
- **商户（B）**：使用授权支付服务的商户
- **用户（C）**：在平台和商户都有共同账户（users.account）的用户

### 1.2 功能需求
实现两种授权支付类型：
1. **加款（add）**：从商户余额扣款，增加资金给用户，直接生成完成状态的订单，用户无需确认，但需要通知商户
2. **扣款（deduct）**：从用户向商户生成待确认订单，确保订单号唯一，返回订单信息给商户

## 2. 系统架构设计

### 2.1 整体架构
遵循项目现有的分层架构模式：
```
API Layer (api/merchant/v1/) 
    ↓
Controller Layer (internal/controller/)
    ↓
Service Layer (internal/logic/merchant/)
    ↓
DAO Layer (internal/dao/)
    ↓
Database (MySQL)
```

### 2.2 核心组件
- **AuthPaymentService**：授权支付业务逻辑服务
- **AuthPaymentController**：HTTP请求处理控制器
- **WalletService**：资金操作服务（复用现有）
- **CallbackService**：回调通知服务

## 3. 数据库设计

### 3.1 主要数据表
使用现有的 `auth_payment_orders` 表，字段说明：

```sql
-- auth_payment_orders 授权支付订单表
CREATE TABLE `auth_payment_orders` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` varchar(64) NOT NULL COMMENT '系统订单号',
    `merchant_id` bigint NOT NULL COMMENT '商户ID',
    `user_account` varchar(64) NOT NULL COMMENT '用户账户标识(users.account)',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID(查询到后填充)',
    `order_type` varchar(20) NOT NULL COMMENT '订单类型: deduct-扣款, add-加款',
    `token_symbol` varchar(20) NOT NULL COMMENT '代币符号',
    `token_id` int DEFAULT NULL COMMENT '代币ID',
    `amount` decimal(36,18) NOT NULL COMMENT '订单金额',
    `auth_reason` text COMMENT '授权原因/说明',
    `merchant_order_no` varchar(128) DEFAULT NULL COMMENT '商户订单号',
    `merchant_transaction_id` bigint DEFAULT NULL COMMENT '商户交易记录ID',
    `user_transaction_id` bigint DEFAULT NULL COMMENT '用户交易记录ID',
    `status` varchar(20) NOT NULL COMMENT '订单状态',
    `callback_url` varchar(512) DEFAULT NULL COMMENT '回调URL',
    `callback_status` varchar(20) DEFAULT NULL COMMENT '回调状态',
    `callback_attempts` int DEFAULT 0 COMMENT '回调尝试次数',
    `last_callback_at` timestamp NULL DEFAULT NULL COMMENT '最后回调时间',
    `next_callback_at` timestamp NULL DEFAULT NULL COMMENT '下次回调时间',
    `callback_response` text COMMENT '最后回调响应',
    `expire_at` timestamp NULL DEFAULT NULL COMMENT '订单过期时间(仅扣款订单)',
    `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
    `error_message` text COMMENT '错误信息',
    `request_ip` varchar(45) DEFAULT NULL COMMENT '请求IP',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_user_account` (`user_account`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权支付订单表';
```

### 3.2 订单状态定义
```go
const (
    // 加款订单状态
    AuthPaymentStatusCompleted = "completed"  // 已完成
    AuthPaymentStatusFailed    = "failed"     // 失败
    
    // 扣款订单状态
    AuthPaymentStatusPending   = "pending"    // 待确认
    AuthPaymentStatusConfirmed = "confirmed"  // 已确认
    AuthPaymentStatusCancelled = "cancelled"  // 已取消
    AuthPaymentStatusExpired   = "expired"    // 已过期
)
```

## 4. API接口设计

### 4.1 创建授权支付订单

#### 4.1.1 接口定义
```go
// api/merchant/v1/auth_payment.go
package v1

type CreateAuthPaymentReq struct {
    g.Meta          `path:"/api/v1/auth-payment/create" method:"POST" tags:"授权支付" summary:"创建授权支付订单"`
    UserAccount     string  `json:"user_account" v:"required" dc:"用户账户标识"`
    OrderType       string  `json:"order_type" v:"required|in:add,deduct" dc:"订单类型: add-加款, deduct-扣款"`
    TokenSymbol     string  `json:"token_symbol" v:"required" dc:"代币符号，如：USDT"`
    Amount          string  `json:"amount" v:"required|min:0.000001" dc:"金额"`
    AuthReason      string  `json:"auth_reason" v:"required|max-length:500" dc:"授权原因"`
    ExpireMinutes   int     `json:"expire_minutes" v:"min:1|max:1440" dc:"过期时间(分钟)，仅扣款订单需要，默认30分钟"`
    MerchantOrderNo string  `json:"merchant_order_no" v:"max-length:128" dc:"商户订单号"`
    CallbackUrl     string  `json:"callback_url" v:"url" dc:"回调URL，不填则使用商户默认回调地址"`
}

type CreateAuthPaymentRes struct {
    OrderNo         string `json:"order_no" dc:"系统订单号"`
    MerchantOrderNo string `json:"merchant_order_no" dc:"商户订单号"`
    UserAccount     string `json:"user_account" dc:"用户账户"`
    OrderType       string `json:"order_type" dc:"订单类型"`
    TokenSymbol     string `json:"token_symbol" dc:"代币符号"`
    Amount          string `json:"amount" dc:"金额"`
    Status          string `json:"status" dc:"订单状态"`
    ExpireAt        string `json:"expire_at,omitempty" dc:"过期时间(仅扣款订单)"`
    CreatedAt       string `json:"created_at" dc:"创建时间"`
}
```

### 4.2 查询授权支付订单

#### 4.2.1 接口定义
```go
type QueryAuthPaymentReq struct {
    g.Meta      `path:"/api/v1/auth-payment/query" method:"POST" tags:"授权支付" summary:"查询授权支付订单"`
    OrderNo     string `json:"order_no" dc:"系统订单号"`
    MerchantOrderNo string `json:"merchant_order_no" dc:"商户订单号"`
}

type QueryAuthPaymentRes struct {
    OrderNo              string `json:"order_no" dc:"系统订单号"`
    MerchantOrderNo      string `json:"merchant_order_no" dc:"商户订单号"`
    UserAccount          string `json:"user_account" dc:"用户账户"`
    OrderType            string `json:"order_type" dc:"订单类型"`
    TokenSymbol          string `json:"token_symbol" dc:"代币符号"`
    Amount               string `json:"amount" dc:"金额"`
    AuthReason           string `json:"auth_reason" dc:"授权原因"`
    Status               string `json:"status" dc:"订单状态"`
    MerchantTransactionId string `json:"merchant_transaction_id,omitempty" dc:"商户交易ID"`
    UserTransactionId    string `json:"user_transaction_id,omitempty" dc:"用户交易ID"`
    ExpireAt             string `json:"expire_at,omitempty" dc:"过期时间"`
    CompletedAt          string `json:"completed_at,omitempty" dc:"完成时间"`
    ErrorMessage         string `json:"error_message,omitempty" dc:"错误信息"`
    CreatedAt            string `json:"created_at" dc:"创建时间"`
    UpdatedAt            string `json:"updated_at" dc:"更新时间"`
}
```

## 5. 业务逻辑实现

### 5.1 Service层设计

#### 5.1.1 接口定义
```go
// internal/service/auth_payment.go
package service

import (
    "context"
    v1 "merchant-server/api/merchant/v1"
)

type IAuthPaymentService interface {
    // CreateAuthPaymentOrder 创建授权支付订单
    CreateAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.CreateAuthPaymentReq) (*v1.CreateAuthPaymentRes, error)
    
    // QueryAuthPaymentOrder 查询授权支付订单
    QueryAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.QueryAuthPaymentReq) (*v1.QueryAuthPaymentRes, error)
    
    // ProcessAuthPaymentCallback 处理授权支付回调
    ProcessAuthPaymentCallback(ctx context.Context, orderId int64) error
}

// 全局服务注册函数
var localAuthPaymentService IAuthPaymentService

func AuthPaymentService() IAuthPaymentService {
    return localAuthPaymentService
}

func RegisterAuthPaymentService(s IAuthPaymentService) {
    localAuthPaymentService = s
}
```

#### 5.1.2 Service实现
```go
// internal/logic/merchant/auth_payment.go
package merchant

import (
    "context"
    "fmt"
    "time"
    
    "github.com/gogf/gf/v2/database/gdb"
    "github.com/gogf/gf/v2/errors/gerror"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/os/gtime"
    "github.com/gogf/gf/v2/util/gconv"
    "github.com/shopspring/decimal"
    
    v1 "merchant-server/api/merchant/v1"
    "merchant-server/internal/constants"
    "merchant-server/internal/dao"
    "merchant-server/internal/model/entity"
    "merchant-server/internal/service"
    "merchant-server/internal/utils"
    "merchant-server/internal/wallet"
)

type sAuthPaymentService struct{}

func init() {
    service.RegisterAuthPaymentService(NewAuthPaymentService())
}

func NewAuthPaymentService() *sAuthPaymentService {
    return &sAuthPaymentService{}
}

// CreateAuthPaymentOrder 创建授权支付订单
func (s *sAuthPaymentService) CreateAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.CreateAuthPaymentReq) (*v1.CreateAuthPaymentRes, error) {
    // 1. 参数验证
    amount, err := decimal.NewFromString(req.Amount)
    if err != nil {
        return nil, gerror.New("金额格式错误")
    }
    if amount.LessThanOrEqual(decimal.Zero) {
        return nil, gerror.New("金额必须大于0")
    }
    
    // 2. 获取请求IP
    requestIP := s.getClientIP(ctx)
    
    // 3. 根据订单类型处理
    switch req.OrderType {
    case "add":
        return s.processAddFundsOrder(ctx, merchantId, req, amount, requestIP)
    case "deduct":
        return s.processDeductFundsOrder(ctx, merchantId, req, amount, requestIP)
    default:
        return nil, gerror.New("无效的订单类型")
    }
}

// processAddFundsOrder 处理加款订单
func (s *sAuthPaymentService) processAddFundsOrder(ctx context.Context, merchantId int64, req *v1.CreateAuthPaymentReq, amount decimal.Decimal, requestIP string) (*v1.CreateAuthPaymentRes, error) {
    // 1. 查询用户信息
    var user entity.Users
    err := dao.Users.Ctx(ctx).Where("account", req.UserAccount).Scan(&user)
    if err != nil {
        return nil, gerror.Wrap(err, "查询用户失败")
    }
    if user.Id == 0 {
        return nil, gerror.New("用户不存在")
    }
    
    // 2. 生成订单号
    orderNo := s.generateOrderNo("AP")
    
    var result *v1.CreateAuthPaymentRes
    
    // 3. 事务处理
    err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 3.1 创建订单记录
        orderId, err := s.createAuthPaymentOrder(ctx, tx, &createOrderParams{
            orderNo:         orderNo,
            merchantId:      merchantId,
            userAccount:     req.UserAccount,
            userId:          user.Id,
            orderType:       req.OrderType,
            tokenSymbol:     req.TokenSymbol,
            amount:          amount,
            authReason:      req.AuthReason,
            merchantOrderNo: req.MerchantOrderNo,
            callbackUrl:     req.CallbackUrl,
            status:          "processing",
            requestIP:       requestIP,
        })
        if err != nil {
            return err
        }
        
        // 3.2 执行商户扣款
        merchantWalletResult, err := s.processMerchantDebit(ctx, tx, merchantId, req.TokenSymbol, amount, orderNo)
        if err != nil {
            // 更新订单状态为失败
            s.updateOrderStatus(ctx, tx, orderId, "failed", err.Error(), nil, nil)
            return err
        }
        
        // 3.3 执行用户加款
        userWalletResult, err := s.processUserCredit(ctx, tx, user.Id, req.TokenSymbol, amount, orderNo, req.AuthReason)
        if err != nil {
            // 更新订单状态为失败
            s.updateOrderStatus(ctx, tx, orderId, "failed", err.Error(), 
                &merchantWalletResult.TransactionID, nil)
            return err
        }
        
        // 3.4 更新订单为完成状态
        completedAt := gtime.Now()
        err = s.updateOrderStatus(ctx, tx, orderId, "completed", "", 
            &merchantWalletResult.TransactionID, &userWalletResult.TransactionID)
        if err != nil {
            return err
        }
        
        // 3.5 创建回调记录
        if req.CallbackUrl != "" || s.getMerchantCallbackUrl(ctx, merchantId) != "" {
            err = s.createCallbackRecord(ctx, tx, orderId, merchantId, orderNo, "add_completed", amount, req.TokenSymbol)
            if err != nil {
                g.Log().Errorf(ctx, "创建回调记录失败: %v", err)
                // 回调失败不影响主流程
            }
        }
        
        // 3.6 构建响应
        result = &v1.CreateAuthPaymentRes{
            OrderNo:         orderNo,
            MerchantOrderNo: req.MerchantOrderNo,
            UserAccount:     req.UserAccount,
            OrderType:       req.OrderType,
            TokenSymbol:     req.TokenSymbol,
            Amount:          amount.String(),
            Status:          "completed",
            CreatedAt:       gtime.Now().Format("c"),
        }
        
        return nil
    })
    
    if err != nil {
        g.Log().Errorf(ctx, "加款订单处理失败: %v", err)
        return nil, gerror.Wrap(err, "订单处理失败")
    }
    
    return result, nil
}

// processDeductFundsOrder 处理扣款订单
func (s *sAuthPaymentService) processDeductFundsOrder(ctx context.Context, merchantId int64, req *v1.CreateAuthPaymentReq, amount decimal.Decimal, requestIP string) (*v1.CreateAuthPaymentRes, error) {
    // 1. 查询用户信息
    var user entity.Users
    err := dao.Users.Ctx(ctx).Where("account", req.UserAccount).Scan(&user)
    if err != nil {
        return nil, gerror.Wrap(err, "查询用户失败")
    }
    if user.Id == 0 {
        return nil, gerror.New("用户不存在")
    }
    
    // 2. 生成订单号
    orderNo := s.generateOrderNo("DP")
    
    // 3. 计算过期时间
    expireMinutes := req.ExpireMinutes
    if expireMinutes == 0 {
        expireMinutes = 30 // 默认30分钟
    }
    expireAt := gtime.Now().Add(time.Duration(expireMinutes) * time.Minute)
    
    // 4. 创建待确认订单
    orderId, err := s.createAuthPaymentOrder(ctx, nil, &createOrderParams{
        orderNo:         orderNo,
        merchantId:      merchantId,
        userAccount:     req.UserAccount,
        userId:          user.Id,
        orderType:       req.OrderType,
        tokenSymbol:     req.TokenSymbol,
        amount:          amount,
        authReason:      req.AuthReason,
        merchantOrderNo: req.MerchantOrderNo,
        callbackUrl:     req.CallbackUrl,
        status:          "pending",
        expireAt:        &expireAt,
        requestIP:       requestIP,
    })
    
    if err != nil {
        return nil, gerror.Wrap(err, "创建订单失败")
    }
    
    // 5. 构建响应
    return &v1.CreateAuthPaymentRes{
        OrderNo:         orderNo,
        MerchantOrderNo: req.MerchantOrderNo,
        UserAccount:     req.UserAccount,
        OrderType:       req.OrderType,
        TokenSymbol:     req.TokenSymbol,
        Amount:          amount.String(),
        Status:          "pending",
        ExpireAt:        expireAt.Format("c"),
        CreatedAt:       gtime.Now().Format("c"),
    }, nil
}

// 辅助方法实现...
```

### 5.2 资金操作集成

#### 5.2.1 商户扣款
```go
// processMerchantDebit 处理商户扣款
func (s *sAuthPaymentService) processMerchantDebit(ctx context.Context, tx gdb.TX, merchantId int64, tokenSymbol string, amount decimal.Decimal, orderNo string) (*wallet.FundOperationResult, error) {
    descriptor := utils.NewFundOperationDescriptor("zh")
    
    req := &wallet.FundOperationRequest{
        UserID:      uint64(merchantId),
        TokenSymbol: tokenSymbol,
        Amount:      amount,
        BusinessID:  descriptor.GenerateBusinessID(constants.FundOpAuthPaymentAdd, orderNo, gtime.Now().Unix()),
        FundType:    wallet.FundTypeAuthPaymentAdd,
        Description: descriptor.FormatBasicDescription(constants.FundOpAuthPaymentAdd, amount.String(), tokenSymbol),
        Metadata: map[string]string{
            "type":      "auth_payment",
            "order_no":  orderNo,
            "operation": "merchant_debit",
        },
        RequestSource: "merchant_api",
    }
    
    walletService := GetWalletService()
    return walletService.ProcessFundOperationInTx(ctx, tx, req)
}

// processUserCredit 处理用户加款
func (s *sAuthPaymentService) processUserCredit(ctx context.Context, tx gdb.TX, userId int64, tokenSymbol string, amount decimal.Decimal, orderNo string, reason string) (*wallet.FundOperationResult, error) {
    descriptor := utils.NewFundOperationDescriptor("zh")
    
    req := &wallet.FundOperationRequest{
        UserID:      uint64(userId),
        TokenSymbol: tokenSymbol,
        Amount:      amount,
        BusinessID:  descriptor.GenerateBusinessID(constants.FundOpAuthPaymentAdd, orderNo, gtime.Now().Unix()),
        FundType:    wallet.FundTypeAuthPaymentAdd,
        Description: descriptor.FormatDescriptionWithMemo(constants.FundOpAuthPaymentAdd, amount.String(), tokenSymbol, reason),
        Metadata: map[string]string{
            "type":      "auth_payment",
            "order_no":  orderNo,
            "operation": "user_credit",
            "reason":    reason,
        },
        RequestSource: "merchant_api",
    }
    
    walletService := GetWalletService()
    return walletService.ProcessFundOperationInTx(ctx, tx, req)
}
```

### 5.3 回调机制

#### 5.3.1 回调数据结构
```go
type AuthPaymentCallback struct {
    EventType       string `json:"event_type"`       // 事件类型: add_completed, deduct_pending
    OrderNo         string `json:"order_no"`         // 系统订单号
    MerchantOrderNo string `json:"merchant_order_no"` // 商户订单号
    UserAccount     string `json:"user_account"`     // 用户账户
    OrderType       string `json:"order_type"`       // 订单类型
    TokenSymbol     string `json:"token_symbol"`     // 代币符号
    Amount          string `json:"amount"`           // 金额
    Status          string `json:"status"`           // 订单状态
    CompletedAt     string `json:"completed_at,omitempty"` // 完成时间
    Timestamp       int64  `json:"timestamp"`        // 时间戳
    Sign            string `json:"sign"`             // 签名
}
```

#### 5.3.2 回调实现
```go
// createCallbackRecord 创建回调记录
func (s *sAuthPaymentService) createCallbackRecord(ctx context.Context, tx gdb.TX, orderId int64, merchantId int64, orderNo string, eventType string, amount decimal.Decimal, tokenSymbol string) error {
    // 获取商户信息和回调URL
    callbackUrl := s.getMerchantCallbackUrl(ctx, merchantId)
    if callbackUrl == "" {
        return nil // 无回调URL，跳过
    }
    
    // 构建回调数据
    callbackData := g.Map{
        "event_type":   eventType,
        "order_no":     orderNo,
        "merchant_id":  merchantId,
        "amount":       amount.String(),
        "token_symbol": tokenSymbol,
        "timestamp":    time.Now().Unix(),
    }
    
    payload, err := json.Marshal(callbackData)
    if err != nil {
        return gerror.Wrap(err, "序列化回调数据失败")
    }
    
    // 插入回调记录
    insertData := g.Map{
        "merchant_id":   merchantId,
        "callback_type": "auth_payment",
        "related_id":    orderId,
        "callback_url":  callbackUrl,
        "payload":       string(payload),
        "status":        "pending",
        "retry_count":   0,
        "created_at":    gtime.Now(),
        "updated_at":    gtime.Now(),
    }
    
    if tx != nil {
        _, err = dao.MerchantCallbacks.Ctx(ctx).TX(tx).Insert(insertData)
    } else {
        _, err = dao.MerchantCallbacks.Ctx(ctx).Insert(insertData)
    }
    
    return err
}
```

## 6. Controller层实现

```go
// internal/controller/merchant/auth_payment.go
package merchant

import (
    "context"
    
    "github.com/gogf/gf/v2/errors/gerror"
    v1 "merchant-server/api/merchant/v1"
    "merchant-server/internal/service"
)

type cAuthPaymentController struct{}

var AuthPaymentController = cAuthPaymentController{}

// CreateAuthPaymentOrder 创建授权支付订单
func (c *cAuthPaymentController) CreateAuthPaymentOrder(ctx context.Context, req *v1.CreateAuthPaymentReq) (res *v1.CreateAuthPaymentRes, err error) {
    merchantId := service.MerchantService().GetMerchantId(ctx)
    if merchantId == 0 {
        return nil, gerror.New("获取商户信息失败")
    }
    
    return service.AuthPaymentService().CreateAuthPaymentOrder(ctx, merchantId, req)
}

// QueryAuthPaymentOrder 查询授权支付订单
func (c *cAuthPaymentController) QueryAuthPaymentOrder(ctx context.Context, req *v1.QueryAuthPaymentReq) (res *v1.QueryAuthPaymentRes, err error) {
    merchantId := service.MerchantService().GetMerchantId(ctx)
    if merchantId == 0 {
        return nil, gerror.New("获取商户信息失败")
    }
    
    return service.AuthPaymentService().QueryAuthPaymentOrder(ctx, merchantId, req)
}
```

## 7. 错误处理

### 7.1 错误码定义
```go
const (
    // 通用错误
    ErrInvalidParams      = "INVALID_PARAMS"       // 参数错误
    ErrSystemError        = "SYSTEM_ERROR"         // 系统错误
    
    // 用户相关
    ErrUserNotFound       = "USER_NOT_FOUND"       // 用户不存在
    ErrUserBalanceInsufficient = "USER_BALANCE_INSUFFICIENT" // 用户余额不足
    
    // 商户相关
    ErrMerchantNotFound   = "MERCHANT_NOT_FOUND"   // 商户不存在
    ErrMerchantBalanceInsufficient = "MERCHANT_BALANCE_INSUFFICIENT" // 商户余额不足
    
    // 订单相关
    ErrOrderNotFound      = "ORDER_NOT_FOUND"      // 订单不存在
    ErrOrderDuplicate     = "ORDER_DUPLICATE"      // 订单重复
    ErrOrderExpired       = "ORDER_EXPIRED"        // 订单已过期
    ErrOrderStatusInvalid = "ORDER_STATUS_INVALID" // 订单状态无效
)
```

### 7.2 错误处理流程
1. 参数验证失败：返回 400 错误码
2. 业务逻辑错误：返回对应错误码和错误信息
3. 系统错误：记录日志，返回 500 错误码

## 8. 安全考虑

### 8.1 接口认证
- 使用商户API密钥进行接口认证
- 验证商户IP白名单（如配置）

### 8.2 数据安全
- 所有金额使用 decimal 类型避免精度问题
- 事务保证数据一致性
- 敏感信息加密存储

### 8.3 防重复提交
- 使用唯一订单号防止重复创建
- 幂等性设计确保重复请求安全

## 9. 性能优化

### 9.1 数据库优化
- 合理使用索引（merchant_id, user_account, order_no等）
- 批量操作减少数据库交互

### 9.2 并发控制
- 使用数据库事务保证一致性
- 钱包服务内置并发控制机制

## 10. 测试方案

### 10.1 单元测试
```go
// internal/logic/merchant/auth_payment_test.go
func TestCreateAuthPaymentOrder_AddFunds(t *testing.T) {
    // 测试加款订单创建
}

func TestCreateAuthPaymentOrder_DeductFunds(t *testing.T) {
    // 测试扣款订单创建
}
```

### 10.2 集成测试
- 测试完整的订单创建流程
- 测试资金操作的正确性
- 测试回调机制

### 10.3 性能测试
- 并发创建订单测试
- 大批量订单处理测试

## 11. 部署说明

### 11.1 配置更新
无需特殊配置，使用现有配置即可

### 11.2 数据库迁移
确保 `auth_payment_orders` 表已创建

### 11.3 服务启动
按照现有流程启动服务即可

## 12. 监控与日志

### 12.1 关键指标
- 订单创建成功率
- 资金操作成功率
- 回调成功率
- 接口响应时间

### 12.2 日志记录
- 记录所有订单创建请求
- 记录资金操作详情
- 记录回调执行情况

## 13. 后续优化

### 13.1 功能扩展
- 支持订单撤销功能
- 支持更多订单查询条件

### 13.2 性能优化
- 引入缓存机制
- 异步处理回调
- 数据库读写分离

---

文档版本：v1.0  
更新日期：2025-01-09  
作者：X-Pay技术团队