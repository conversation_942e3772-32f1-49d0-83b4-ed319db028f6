#!/bin/bash

# 授权支付加款测试脚本
# 测试重构后的加款逻辑

# 配置
API_URL="http://localhost:8002"
API_KEY="sk_test_c5aabe86e9e2e4bb4a8c31b5de9bc9f3c07de8b17f0df3d89c7fa0f96cebeaf0"

# 生成测试数据
MERCHANT_ORDER_NO="TEST_ADD_$(date +%s)"
USER_ACCOUNT="test_user_1"
AMOUNT="10.00"
TOKEN_SYMBOL="USDT"
AUTH_REASON="测试授权加款 - 重构验证"

echo "========================================="
echo "授权支付加款测试"
echo "========================================="
echo "商户订单号: $MERCHANT_ORDER_NO"
echo "用户账户: $USER_ACCOUNT"
echo "加款金额: $AMOUNT $TOKEN_SYMBOL"
echo "授权原因: $AUTH_REASON"
echo ""

# 创建加款订单
echo "1. 创建加款订单..."
RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth-payment/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "userAccount": "'$USER_ACCOUNT'",
    "orderType": "add",
    "tokenSymbol": "'$TOKEN_SYMBOL'",
    "amount": "'$AMOUNT'",
    "authReason": "'$AUTH_REASON'",
    "merchantOrderNo": "'$MERCHANT_ORDER_NO'"
  }')

echo "响应:"
echo "$RESPONSE" | jq .

# 提取订单号
ORDER_NO=$(echo "$RESPONSE" | jq -r '.data.orderNo // empty')

if [ -z "$ORDER_NO" ]; then
  echo "错误: 无法获取订单号"
  exit 1
fi

echo ""
echo "2. 查询订单状态..."
sleep 2

# 查询订单
QUERY_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth-payment/query" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "orderNo": "'$ORDER_NO'"
  }')

echo "查询响应:"
echo "$QUERY_RESPONSE" | jq .

# 检查订单状态
STATUS=$(echo "$QUERY_RESPONSE" | jq -r '.data.status // empty')
echo ""
echo "========================================="
echo "测试结果:"
echo "订单状态: $STATUS"

if [ "$STATUS" = "completed" ]; then
  echo "✓ 加款订单处理成功"
  
  # 获取交易ID
  MERCHANT_TX_ID=$(echo "$QUERY_RESPONSE" | jq -r '.data.merchantTransactionId // "N/A"')
  USER_TX_ID=$(echo "$QUERY_RESPONSE" | jq -r '.data.userTransactionId // "N/A"')
  
  echo "商户交易ID: $MERCHANT_TX_ID"
  echo "用户交易ID: $USER_TX_ID"
else
  echo "✗ 加款订单处理失败"
  ERROR_MSG=$(echo "$QUERY_RESPONSE" | jq -r '.data.errorMessage // "未知错误"')
  echo "错误信息: $ERROR_MSG"
fi

echo "========================================="

# 如果需要查看更多详情，可以查询订单列表
echo ""
echo "3. 查询最近的授权支付订单..."
LIST_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth-payment/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "page": 1,
    "pageSize": 5,
    "orderType": "add"
  }')

echo "最近的加款订单:"
echo "$LIST_RESPONSE" | jq '.data.list[] | {orderNo: .orderNo, merchantOrderNo: .merchantOrderNo, amount: .amount, status: .status, createdAt: .createdAt}'