#!/bin/bash

# 测试获取客户端IP接口
# 需要提供有效的API Key和签名

# API配置
API_KEY="your_api_key_here"
SECRET="your_secret_here"
BASE_URL="http://localhost:8081"
ENDPOINT="/api/v1/client-ip"

# 生成时间戳和随机数
TIMESTAMP=$(date +%s)
NONCE=$(openssl rand -hex 16)

# 构建签名字符串
METHOD="GET"
URI="$ENDPOINT"
BODY=""
SIGN_STRING="${METHOD}${URI}${TIMESTAMP}${NONCE}${BODY}"

# 计算HMAC-SHA256签名
SIGNATURE=$(echo -n "$SIGN_STRING" | openssl dgst -sha256 -hmac "$SECRET" -binary | xxd -p -c 256)

echo "测试获取客户端IP接口"
echo "===================="
echo "URL: ${BASE_URL}${ENDPOINT}"
echo "Method: ${METHOD}"
echo "Timestamp: ${TIMESTAMP}"
echo "Nonce: ${NONCE}"
echo "Signature: ${SIGNATURE}"
echo ""

# 发送请求
echo "发送请求..."
response=$(curl -s -X ${METHOD} \
  -H "X-API-Key: ${API_KEY}" \
  -H "X-Timestamp: ${TIMESTAMP}" \
  -H "X-Nonce: ${NONCE}" \
  -H "X-Signature: ${SIGNATURE}" \
  -H "Content-Type: application/json" \
  "${BASE_URL}${ENDPOINT}")

echo "响应:"
echo "$response" | jq .

# 测试通过代理的请求
echo ""
echo "测试带代理头的请求..."
response_proxy=$(curl -s -X ${METHOD} \
  -H "X-API-Key: ${API_KEY}" \
  -H "X-Timestamp: ${TIMESTAMP}" \
  -H "X-Nonce: ${NONCE}" \
  -H "X-Signature: ${SIGNATURE}" \
  -H "X-Forwarded-For: *******, *******" \
  -H "X-Real-IP: **********" \
  -H "Content-Type: application/json" \
  "${BASE_URL}${ENDPOINT}")

echo "响应:"
echo "$response_proxy" | jq .