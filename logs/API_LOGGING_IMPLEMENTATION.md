# API 日志记录实现说明

## 概述
已成功实现 API 请求响应的文件日志记录功能，所有接口的请求和响应信息都会被完整记录到日志文件中。

## 实现方案

### 核心中间件
- **`UltimateHandlerResponse`**：自定义的响应处理中间件，替代了 GoFrame 原生的 `ghttp.MiddlewareHandlerResponse`
- 位置：`/internal/logic/middleware/ultimate_logger.go`

### 功能特性
1. **完整记录请求信息**：
   - 请求方法、URL、Headers
   - 请求体内容
   - 客户端 IP、User-Agent
   
2. **完整记录响应信息**：
   - 响应状态码、Headers
   - 响应体内容（JSON 格式）
   - 请求处理耗时

3. **日志文件组织**：
   - 按日期创建目录：`logs/YYYY-MM-DD/`
   - 按接口路径创建文件：如 `/api/v1/login` → `api_v1_login.log`
   - 每行一条 JSON 格式的日志记录

## 技术要点

### 问题与解决
1. **问题**：GoFrame 的 `Response.Buffer()` 在某些情况下无法捕获响应内容
2. **原因**：原生的 `MiddlewareHandlerResponse` 直接写入响应，绕过了缓冲区
3. **解决**：创建自定义中间件，在写入响应之前就捕获响应数据

### 中间件注册顺序
```go
group.Middleware(middleware.UltimateHandlerResponse)  // 响应处理和日志记录
group.Middleware(middleware.AuthMiddleware)           // 认证中间件
```

## 使用示例

### 日志文件位置
```
logs/
├── 2025-07-10/
│   ├── api_v1_deposits_address.log
│   ├── api_v1_login.log
│   └── health.log
└── README.md
```

### 日志格式
```json
{
  "timestamp": "2025-07-10 22:45:42.628",
  "request_id": "081e479ac189d37005939fc45c70ec43",
  "method": "POST",
  "url": "/api/v1/deposits/address",
  "client_ip": "**************",
  "user_agent": "Go-http-client/1.1",
  "request_header": {
    "Content-Type": "application/json",
    "X-Api-Key": "btH__8gaw93GoiSwqPrfD6aiNrErJg3u0amh-yfKmiw="
  },
  "request_body": "{\"userLabel\":\"**********\",\"chain\":\"TRC20\"}",
  "response_code": 200,
  "response_header": {},
  "response_body": "{\"code\":0,\"message\":\"success\",\"data\":{...}}",
  "duration_ms": 10,
  "merchant_id": 2
}
```

## 维护说明
- 日志文件会持续增长，建议定期清理旧日志
- 可以使用日志轮转工具进行自动管理
- 敏感信息（如密码）已在请求体中自动脱敏