/build  --feature --ultrathink --persona-architect -seq\  \
\
应用场景 a 平台（我们） ，b商户 ，c 用户（在a 和 b 有一个共同的 users account ）

a需要提供 授权api 让商户支持授权转账 以及授权提现 

1.b请求a接口创建一个c->b 的 授权支付 订单,
传递授权信息 （代币 金额,原因,选填授权有效期. 授权类型扣款,加款）
2.加款是从商户余额扣款然后增加资金给用户 直接生成订单 订单为完成状态  c,不需要确认. 但是要对商户进行通知回调.当前系统只需要创建回调通知记录即可 参考代码 
internal/logic/merchant/deposit.go
3.扣款是从用户 c->b 生成一个待确认的订单(确保订单order_no唯一) 返回给商户订单信息即可,后续不需要当前系统处理了

涉及到的 dao 
internal/dao/users.go //用户表
internal/dao/auth_payment_orders.go //订单表
internal/dao/merchants.go //商户表 
商户自己操作参考 
internal/logic/merchant/withdraw.go
用户资金操作参考下面的代码 

package red_packet

  import (
      "context"
      "crypto/md5"
      "encoding/hex"
      "time"

      "github.com/gogf/gf/v2/database/gdb"
      "github.com/gogf/gf/v2/errors/gerror"
      "github.com/gogf/gf/v2/frame/g"
      "github.com/gogf/gf/v2/os/gtime"
      "github.com/gogf/gf/v2/util/gconv"
      "github.com/google/uuid"
      "github.com/shopspring/decimal"

      config "telegram-bot-api/internal/config"
      "telegram-bot-api/internal/constants"
      "telegram-bot-api/internal/dao"
      "telegram-bot-api/internal/model/entity"
      "telegram-bot-api/internal/service"
      "telegram-bot-api/internal/utils"

      "github.com/yalks/wallet"
      walletConstants "github.com/yalks/wallet/constants"

      "github.com/a19ba14d/tg-bot-common/codes"
      "github.com/a19ba14d/tg-bot-common/consts"
  )

  // CreateRedPacketV2 使用统一资金操作服务的红包创建逻辑
  // 这个版本展示了如何简化红包创建流程，确保资金操作和业务逻辑的一致性
  func (s *sRedPacket) CreateRedPacketV2(ctx context.Context, input service.CreateRedPacketInput) (*entity.RedPackets, error) {
      g.Log().Infof(ctx, "CreateRedPacketV2: Starting operation for user %d", input.CreatorUserId)

      // 1. 输入验证
      if err := s.validateRedPacketInput(ctx, input); err != nil {
          return nil, err
      }

      // 2. 获取用户信息
      user, err := service.User().GetUserByTelegramId(ctx, input.CreatorUserId)
      if err != nil || user == nil {
          g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to find user %d: %v", input.CreatorUserId, err)
          return nil, gerror.NewCode(codes.CodeUserNotFound, "Failed to get user information")
      }

      var createdPacket *entity.RedPackets
      var financialResult *wallet.FundOperationResult

      // 3. 在单个事务中处理所有操作
      dbErr := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
          // 3a. 执行资金扣款 - 使用新的钱包模块
          // 生成原始UUID并使用MD5缩短，以避免回调数据超过Telegram的64字节限制
          // 原始UUID: 36字符 (如: a9590b3f-f935-4a8d-bd22-ee57a895b2a5)
          // MD5缩短后: 16字符 (如: a9590b3ff9354a8d)
          originalUUID := uuid.NewString()
          hash := md5.Sum([]byte(originalUUID))
          redPacketUUID := hex.EncodeToString(hash[:])[:16] // 使用MD5的前16个字符

          // 确保UUID唯一性（虽然碰撞概率极低）
          for {
              exists, err := dao.RedPackets.Ctx(ctx).TX(tx).Where("uuid", redPacketUUID).Count()
              if err != nil {
                  return gerror.Wrap(err, "failed to check UUID uniqueness")
              }
              if exists == 0 {
                  break
              }
              // 如果碰撞，生成新的UUID
              originalUUID = uuid.NewString()
              hash = md5.Sum([]byte(originalUUID))
              redPacketUUID = hex.EncodeToString(hash[:])[:16]
          }

          // 使用新的资金操作系统
          descriptor := utils.NewFundOperationDescriptor("zh")
          req := &walletConstants.FundOperationRequest{
              UserID:      uint64(user.Id),
              TokenSymbol: input.TokenSymbol,
              Amount:      input.TotalAmount,
              BusinessID:  descriptor.GenerateBusinessID(constants.FundOpRedPacketCreate, redPacketUUID, gtime.Now().Unix()),
              FundType:    walletConstants.FundTypeRedPacketCreate,
              Description: descriptor.FormatBasicDescription(constants.FundOpRedPacketCreate, input.TotalAmount.String(), input.TokenSymbol),
              Metadata: map[string]string{
                  "type":      "red_packet",
                  "chat_id":   gconv.String(input.ChatId),
                  "quantity":  gconv.String(input.Quantity),
                  "rp_type":   input.Type,
                  "blessing":  input.Blessing,
                  "operation": "create",
              },
              RequestSource: "telegram",
          }

          var err error
          financialResult, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, req)
          if err != nil {
              g.Log().Errorf(ctx, "CreateRedPacketV2: 红包扣款失败 for user %d: %v", input.CreatorUserId, err)
              return err // 新钱包模块已经处理了错误分类和回滚
          }

          // 3b. 创建红包主记录
          expireMinutes := g.Cfg().MustGet(ctx, "timeouts.redPacketExpirationMinutes", 1440).Int()
          expireDuration := time.Duration(expireMinutes) * time.Minute
          now := gtime.Now()

          // 处理默认祝福语：如果祝福语为空，使用默认祝福语
          blessing := input.Blessing
          if blessing == "" {
              blessing = service.I18n().T(ctx, "{#DefaultBlessing}")
              g.Log().Infof(ctx, "CreateRedPacketV2: Using default blessing for red packet UUID %s", redPacketUUID)
          }

          mainPacket := entity.RedPackets{
              Uuid:              redPacketUUID,
              CreatorUserId:     input.CreatorUserId,
              CreatorUsername:   input.CreatorUsername,
              RedPacketImagesId: input.RedPacketImagesId,
              TokenId:           int(input.TokenId),
              Symbol:            input.TokenSymbol,
              Type:              input.Type,
              Quantity:          input.Quantity,
              TotalAmount:       input.TotalAmount,
              RemainingAmount:   input.TotalAmount,
              RemainingQuantity: input.Quantity,
              Memo:              blessing, // 使用处理后的祝福语
              Status:            string(consts.RedPacketStatusActive),
              CreatedAt:         now,
              ExpiresAt:         now.Add(expireDuration),
              CoverFileId:       input.CoverFileID,
              ThumbUrl:          input.ThumbUrl,
              SenderUserId:      uint64(input.UserId),                                // 设置发送方用户ID
              TransactionId:     s.parseTransactionID(financialResult.TransactionID), // 关联交易流水
              MessageId:         gconv.String(input.MessageId),                       // 记录创建红包的消息ID
              IsPremium:         0,                                                   // 默认为非会员红包
          }

          result, err := dao.RedPackets.Ctx(ctx).TX(tx).Data(mainPacket).Insert()
          if err != nil {
              g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to insert main red packet record: %v", err)
              return gerror.Wrap(err, "failed to create main red packet record")
          }

          packetId, _ := result.LastInsertId()
          mainPacket.RedPacketId = packetId
          createdPacket = &mainPacket

          g.Log().Infof(ctx, "CreateRedPacketV2: Created red packet ID %d with UUID %s and transaction ID %s",
              packetId, redPacketUUID, financialResult.TransactionID)

          // 3c. 创建红包分配记录
          if err := s.createRedPacketClaims(ctx, tx, createdPacket, input); err != nil {
              return gerror.Wrap(err, "failed to create red packet claims")
          }

          return nil
      })

      // 4. 处理事务结果
      if dbErr != nil {
          g.Log().Errorf(ctx, "CreateRedPacketV2: Transaction failed for user %d: %v", input.CreatorUserId, dbErr)
          return nil, dbErr
      }

      // 5. 返回成功结果
      g.Log().Infof(ctx, "CreateRedPacketV2: Successfully created red packet ID %d (TxID: %d) for user %d",
          createdPacket.RedPacketId, createdPacket.TransactionId, input.CreatorUserId)
      return createdPacket, nil
  }