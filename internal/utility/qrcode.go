package utility

import (
	"bytes"
	"encoding/base64"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/skip2/go-qrcode"
)

// GenerateQRCode 生成二维码图片
// content: 二维码内容（如地址）
// size: 二维码尺寸（像素）
// 返回PNG格式的图片数据
func GenerateQRCode(content string, size int) ([]byte, error) {
	if content == "" {
		return nil, gerror.New("二维码内容不能为空")
	}

	if size <= 0 {
		size = 256 // 默认尺寸
	}

	// 生成二维码
	qr, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return nil, gerror.Wrap(err, "创建二维码失败")
	}

	// 转换为PNG图片
	qr.DisableBorder = false // 启用边框以提供适当的白色边距
	png, err := qr.PNG(size)
	if err != nil {
		return nil, gerror.Wrap(err, "生成PNG图片失败")
	}

	return png, nil
}

// GenerateQRCodeBase64 生成二维码并返回Base64编码
// content: 二维码内容（如地址）
// size: 二维码尺寸（像素）
// 返回Base64编码的PNG图片
func GenerateQRCodeBase64(content string, size int) (string, error) {
	png, err := GenerateQRCode(content, size)
	if err != nil {
		return "", err
	}

	// 转换为Base64
	encoded := base64.StdEncoding.EncodeToString(png)
	
	// 添加data URI前缀，方便前端直接使用
	return "data:image/png;base64," + encoded, nil
}

// GenerateQRCodeWithLogo 生成带Logo的二维码
// content: 二维码内容
// size: 二维码尺寸
// logoData: Logo图片数据
// 返回PNG格式的图片数据
func GenerateQRCodeWithLogo(content string, size int, logoData []byte) ([]byte, error) {
	// 基础实现，暂不支持Logo
	// 如果需要支持Logo，可以使用image包进行图片合成
	return GenerateQRCode(content, size)
}

// GenerateQRCodeToBuffer 生成二维码到Buffer
// content: 二维码内容
// size: 二维码尺寸
// 返回包含PNG图片的Buffer
func GenerateQRCodeToBuffer(content string, size int) (*bytes.Buffer, error) {
	pngData, err := GenerateQRCode(content, size)
	if err != nil {
		return nil, err
	}

	buffer := bytes.NewBuffer(pngData)
	return buffer, nil
}