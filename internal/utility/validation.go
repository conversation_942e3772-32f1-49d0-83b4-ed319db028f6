package utility

import (
	"context"
	"strings"

	"github.com/shopspring/decimal"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/config"
	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
)

// ValidateAmountDecimal 校验金额是否有效。
// 该方法强制要求传入decimal.Decimal类型的金额和代币符号，
// 从tokens表获取代币的小数位(decimals)进行精确校验。
// 有效的金额必须：
// 1. 不为零
// 2. 不为负数
// 3. 小数位数不超过代币配置的decimals位数
// 4. 在合理的数值范围内
// 5. 不能是无穷大或NaN等特殊值
// 6. 不能超过代币配置的最大/最小金额限制
// 返回 isValid 和一个错误 key（如果无效）。
func ValidateAmountDecimal(ctx context.Context, input decimal.Decimal, symbol string) (isValid bool, errorKey string) {


	// 边缘情况1: 检查context是否有效
	if ctx == nil {
		return false, "上下文无效"
	}

	// 边缘情况2: 检查symbol是否有效
	if strings.TrimSpace(symbol) == "" {
		return false, "代币符号不能为空"
	}

	// 边缘情况3: 检查decimal是否为特殊值
	// decimal库不支持NaN/Inf，但我们检查字符串表示以确保安全
	inputStr := input.String()
	if inputStr == "" || inputStr == "NaN" || inputStr == "+Inf" || inputStr == "-Inf" {
		return false, "金额数值无效"
	}

	// 边缘情况4: 检查是否为零
	if input.IsZero() {
		return false, "金额不能为零"
	}

	// 边缘情况5: 检查是否为负数
	if input.IsNegative() {
		return false, "金额不能为负数"
	}

	// 边缘情况6: 检查是否为极小的正数（接近零但不等于零）
	// 如果小数点后有过多的零，可能是精度问题
	if input.LessThan(decimal.NewFromFloat(1e-18)) {
		return false, "金额过小"
	}

	// 边缘情况7: 检查是否为极大数值（防止溢出）
	// 使用更保守的上限检查
	maxSafeValue := decimal.NewFromFloat(1e15) // 1,000,000,000,000,000
	if input.GreaterThan(maxSafeValue) {
		return false, "金额溢出"
	}

	var token *entity.Tokens
	// 获取代币信息
	err := dao.Tokens.Ctx(ctx).Where(dao.Tokens.Columns().Symbol, symbol).Scan(&token)
	if err != nil {
		return false, "代币不存在"
	}

	// 边缘情况8: 检查代币是否处于活跃状态
	if token.IsActive != 1 || token.Status != 1 {
		return false, "代币未激活"
	}

	// 边缘情况9: 检查decimals配置是否合理
	if token.Decimals > 18 { // 大多数区块链支持的最大精度
		return false, "代币精度配置无效"
	}

	// 边缘情况10: 检查小数位数是否超过代币的decimals配置
	// 使用更精确的方法计算小数位数
	inputScale := int32(0)
	if input.Exponent() < 0 {
		inputScale = int32(input.Exponent() * -1)
	}

	// 对于非常小的数，需要特殊处理
	if inputScale > int32(token.Decimals) {
		// 检查是否是因为精度导致的小数位超出
		truncated := input.Truncate(int32(token.Decimals))
		if !input.Equal(truncated) {
			return false, "金额小数位数过多"
		}
	}

	// 边缘情况11: 检查最小值 - 使用代币的最小精度单位
	minAmount := decimal.NewFromInt(1).Div(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(token.Decimals))))
	if input.LessThan(minAmount) {
		return false, "金额过小"
	}

	// 边缘情况12: 使用默认最大值
	var maxAmount = decimal.NewFromInt(10000000000)

	if input.GreaterThan(maxAmount) {
		return false, "金额过大"
	}

	// 边缘情况13: 最终数值一致性检查
	// 确保转换后的值与原始值一致
	reconverted, err := decimal.NewFromString(input.String())
	if err != nil || !reconverted.Equal(input) {
		return false, "金额数值不一致"
	}

	return true, ""
}

// ValidateAuthPayment provides unified validation for both auth_payment_create and auth_payment_query handlers
// This ensures consistent validation rules across the entire auth payment flow
func ValidateAuthPayment(ctx context.Context, req *v1.AuthPaymentCreateReq) (isValid bool, errorKey string) {
	// 1. 检查授权支付功能是否启用
	state, err := config.GetBool(ctx, "auth_pay.state", false)
	if err != nil {
		return false, "授权支付配置错误"
	}
	if !state {
		return false, "授权支付功能未启用"
	}
	
	// 2. 验证代币是否在允许列表中
	allowedTokensStr, err := config.GetString(ctx, "auth_pay.token_symbol", "")
	if err != nil {
		return false, "授权支付配置错误"
	}
	if allowedTokensStr == "" {
		return false, "未配置允许的代币"
	}
	
	// 按换行符分割代币列表
	allowedTokens := strings.Split(allowedTokensStr, "\n")
	tokenAllowed := false
	for _, token := range allowedTokens {
		token = strings.TrimSpace(token)
		if token != "" && token == req.TokenSymbol {
			tokenAllowed = true
			break
		}
	}
	
	if !tokenAllowed {
		return false, "不支持的代币类型"
	}
	
	// 3. 验证金额范围
	// 获取最小金额配置
	minAmountInterface, err := config.GetMapKey(ctx, "auth_pay.min_single_amount", req.TokenSymbol, "0.01")
	if err != nil {
		return false, "授权支付配置错误"
	}
	
	minAmountStr, ok := minAmountInterface.(string)
	if !ok {
		return false, "最小金额配置无效"
	}
	
	minAmount, err := decimal.NewFromString(minAmountStr)
	if err != nil {
		return false, "最小金额配置无效"
	}
	
	// 获取最大金额配置
	maxAmountInterface, err := config.GetMapKey(ctx, "auth_pay.max_single_amount", req.TokenSymbol, "1000000")
	if err != nil {
		return false, "授权支付配置错误"
	}
	
	maxAmountStr, ok := maxAmountInterface.(string)
	if !ok {
		return false, "最大金额配置无效"
	}
	
	maxAmount, err := decimal.NewFromString(maxAmountStr)
	if err != nil {
		return false, "最大金额配置无效"
	}
	
	// 检查金额是否在允许范围内
	if req.Amount.LessThan(minAmount) {
		return false, "金额低于最小限额"
	}
	
	if req.Amount.GreaterThan(maxAmount) {
		return false, "金额超过最大限额"
	}
	
	return true, ""
}


// ValidateInlineTransferRequest provides unified validation for both inline_query and chosen_inline_result handlers
// This ensures consistent validation rules across the entire inline transfer flow
type InlineTransferValidationResult struct {
	IsValid              bool
	ErrorKey             string
	Amount               decimal.Decimal
	Symbol               string
	TokenInfo            interface{} // Will be populated if validation passes
	NeedsPassword        bool
	UserBalance          decimal.Decimal
	HasSufficientBalance bool
}


// ValidateInlinePaymentRequest provides unified validation for both inline_query and chosen_inline_result handlers for payment requests
// This ensures consistent validation rules across the entire inline payment request flow
type InlinePaymentValidationResult struct {
	IsValid   bool
	ErrorKey  string
	Amount    decimal.Decimal
	Symbol    string
	TokenInfo interface{} // Will be populated if validation passes
}


// TrimTrailingZeros removes trailing zeros from a decimal number's string representation.
// It takes a decimal.Decimal as input and returns a string without trailing zeros.
// Examples:
// - 1.2300 -> "1.23"
// - 1.0000 -> "1"
// - 100.00 -> "100"
// - 0.0000 -> "0"
func TrimTrailingZeros(input decimal.Decimal) string {
	// Get the string representation of the decimal
	str := input.String()

	// If the string contains a decimal point, remove trailing zeros
	if strings.Contains(str, ".") {
		// Remove trailing zeros after decimal point
		str = strings.TrimRight(str, "0")
		// If all decimals were zeros, remove the decimal point too
		str = strings.TrimRight(str, ".")
	}

	return str
}

// TrimTrailingZerosFee formats fee amounts with 6 decimal precision and removes trailing zeros.
// It's specifically designed for fee amounts which can be very small.
// Examples:
// - 0.000100 -> "0.0001"
// - 0.123456789 -> "0.123456"
// - 1.000000 -> "1"
func TrimTrailingZerosFee(input decimal.Decimal) string {
	// Format with 6 decimal places
	str := input.StringFixed(6)

	// If the string contains a decimal point, remove trailing zeros
	if strings.Contains(str, ".") {
		// Remove trailing zeros after decimal point
		str = strings.TrimRight(str, "0")
		// If all decimals were zeros, remove the decimal point too
		str = strings.TrimRight(str, ".")
	}

	return str
}
