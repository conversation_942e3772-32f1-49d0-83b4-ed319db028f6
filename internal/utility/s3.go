package utility

import (
	"bytes"
	"context"
	"fmt"
	"mime"
	"path/filepath"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// S3Config S3配置结构
type S3Config struct {
	AccessKeyID          string
	SecretAccessKey      string
	Region               string
	BucketName           string
	UsePathStyleEndpoint bool
	PublicURLPrefix      string
}

// S3Client S3客户端封装
type S3Client struct {
	client *s3.Client
	config *S3Config
}

// NewS3Client 创建S3客户端
func NewS3Client(ctx context.Context) (*S3Client, error) {
	// 从配置文件读取S3配置
	s3Config := &S3Config{
		AccessKeyID:          g.Cfg().MustGet(ctx, "s3.accessKeyID").String(),
		SecretAccessKey:      g.Cfg().MustGet(ctx, "s3.secretAccessKey").String(),
		Region:               g.Cfg().MustGet(ctx, "s3.region").String(),
		BucketName:           g.Cfg().MustGet(ctx, "s3.bucketName").String(),
		UsePathStyleEndpoint: g.Cfg().MustGet(ctx, "s3.usePathStyleEndpoint").Bool(),
		PublicURLPrefix:      g.Cfg().MustGet(ctx, "s3.publicURLPrefix").String(),
	}

	// 验证配置
	if s3Config.AccessKeyID == "" || s3Config.SecretAccessKey == "" || 
		s3Config.Region == "" || s3Config.BucketName == "" {
		return nil, gerror.New("S3配置不完整")
	}

	// 创建AWS配置
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(s3Config.Region),
		config.WithCredentialsProvider(
			credentials.NewStaticCredentialsProvider(
				s3Config.AccessKeyID,
				s3Config.SecretAccessKey,
				"",
			),
		),
	)
	if err != nil {
		return nil, gerror.Wrap(err, "加载AWS配置失败")
	}

	// 创建S3客户端
	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.UsePathStyle = s3Config.UsePathStyleEndpoint
	})

	return &S3Client{
		client: client,
		config: s3Config,
	}, nil
}

// UploadFile 上传文件到S3
func (c *S3Client) UploadFile(ctx context.Context, key string, data []byte, contentType string) (string, error) {
	// 如果没有指定内容类型，尝试根据文件扩展名推断
	if contentType == "" {
		ext := filepath.Ext(key)
		contentType = mime.TypeByExtension(ext)
		if contentType == "" {
			contentType = "application/octet-stream"
		}
	}

	// 上传文件
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(c.config.BucketName),
		Key:         aws.String(key),
		Body:        bytes.NewReader(data),
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", gerror.Wrap(err, "上传文件到S3失败")
	}

	// 构建访问URL
	url := c.GetFileURL(key)
	
	return url, nil
}

// GetFileURL 获取文件的访问URL
func (c *S3Client) GetFileURL(key string) string {
	// 如果配置了自定义的公共URL前缀，使用它
	if c.config.PublicURLPrefix != "" {
		return fmt.Sprintf("%s/%s", strings.TrimRight(c.config.PublicURLPrefix, "/"), key)
	}

	// 否则使用标准的S3 URL格式
	if c.config.UsePathStyleEndpoint {
		// Path-style URL: https://s3.region.amazonaws.com/bucket/key
		return fmt.Sprintf("https://s3.%s.amazonaws.com/%s/%s", 
			c.config.Region, c.config.BucketName, key)
	}

	// Virtual-hosted-style URL: https://bucket.s3.region.amazonaws.com/key
	return fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", 
		c.config.BucketName, c.config.Region, key)
}

// DeleteFile 删除S3中的文件
func (c *S3Client) DeleteFile(ctx context.Context, key string) error {
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(c.config.BucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return gerror.Wrap(err, "删除S3文件失败")
	}
	
	return nil
}

// FileExists 检查文件是否存在
func (c *S3Client) FileExists(ctx context.Context, key string) (bool, error) {
	_, err := c.client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(c.config.BucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		// 如果是404错误，说明文件不存在
		if strings.Contains(err.Error(), "NotFound") {
			return false, nil
		}
		return false, gerror.Wrap(err, "检查S3文件失败")
	}
	
	return true, nil
}