# Swap 配置管理指南

## 概述
Swap 模块的所有配置都存储在 `admin_config_items` 表中，使用 `category_id = 12`。配置通过 `config.manager.go` 进行统一管理，支持热更新。

## 配置项列表

### 服务配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.enabled | boolean | true | Swap 服务是否启用 |
| swap.maintenance_mode | boolean | false | 是否处于维护模式 |
| swap.maintenance_message | text | "" | 维护模式提示信息 |

### 限额配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.limits_global_daily_limit_usd | number | 1000000 | 全局每日限额（美元） |
| swap.limits_user_daily_limit_usd | number | 10000 | 用户每日限额（美元） |
| swap.limits_min_order_amount_usd | number | 1 | 最小订单金额（美元） |
| swap.limits_max_order_amount_usd | number | 100000 | 最大订单金额（美元） |

### 定价配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.pricing_default_slippage | number | 0.01 | 默认滑点（1%） |
| swap.pricing_max_slippage | number | 0.05 | 最大滑点（5%） |
| swap.pricing_price_validity_seconds | number | 30 | 价格有效期（秒） |
| swap.pricing_quote_expiry_seconds | number | 60 | 报价过期时间（秒） |

### 费用配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.fees_default_rate | number | 0.002 | 默认费率（0.2%） |
| swap.fees_min_amount_usd | number | 0.1 | 最小费用金额（美元） |
| swap.fees_discount_enabled | boolean | false | 是否启用费用折扣 |

### 风控配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.risk_price_deviation_threshold | number | 0.02 | 价格偏差阈值（2%） |
| swap.risk_velocity_check_enabled | boolean | false | 是否启用速率检查 |
| swap.risk_max_orders_per_minute | number | 10 | 每分钟最大订单数 |

### 监控配置
| 配置键 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| swap.monitoring_alert_threshold_usd | number | 10000 | 告警阈值（美元） |
| swap.monitoring_log_all_orders | boolean | false | 是否记录所有订单 |

## 配置读取方式

在代码中通过 SwapConfigManager 读取配置：

```go
// 获取完整配置
config, err := service.SwapConfig().GetConfig(ctx)

// 或直接使用 config manager
enabled := config.GetBool(ctx, "swap.enabled", true)
feeRate := config.GetDecimal(ctx, "swap.fees_default_rate", decimal.NewFromFloat(0.002))
```

## 修改配置

### 通过数据库直接更新
```sql
-- 启用/禁用 Swap 服务
UPDATE admin_config_items SET value = 'false' WHERE key = 'swap.enabled';

-- 设置维护模式
UPDATE admin_config_items SET value = 'true' WHERE key = 'swap.maintenance_mode';
UPDATE admin_config_items SET value = '系统维护中，请稍后再试' WHERE key = 'swap.maintenance_message';

-- 调整费率
UPDATE admin_config_items SET value = '0.003' WHERE key = 'swap.fees_default_rate';

-- 调整限额
UPDATE admin_config_items SET value = '50000' WHERE key = 'swap.limits_user_daily_limit_usd';
```

### 配置生效
配置修改后会通过 Consul Watch 机制自动生效，无需重启服务。缓存会在 60 秒后自动刷新。

## 注意事项

1. **数值类型**：所有数值类型的配置都以字符串形式存储，读取时会自动转换为相应的类型（decimal, int 等）
2. **百分比值**：滑点、费率等百分比值使用小数表示，如 0.01 表示 1%
3. **金额单位**：所有金额配置都以美元为单位
4. **布尔值**：使用字符串 "true" 或 "false" 表示
5. **缓存机制**：配置有 60 秒的缓存时间，频繁读取不会造成性能问题

## 配置初始化 SQL

完整的配置初始化 SQL 请参考项目文档中的 swap 配置部分。