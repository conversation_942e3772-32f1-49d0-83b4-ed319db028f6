package wallet

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// EnsuredWalletManager 确保钱包存在的管理器装饰器
// 该装饰器会在执行某些操作前自动确保钱包存在
type EnsuredWalletManager struct {
	inner   Manager // 被装饰的原始管理器
	dbGroup string  // 数据库组名
}

// NewEnsuredWalletManager 创建确保钱包存在的管理器
func NewEnsuredWalletManager(inner Manager, dbGroup string) Manager {
	if dbGroup == "" {
		dbGroup = "default"
	}
	return &EnsuredWalletManager{
		inner:   inner,
		dbGroup: dbGroup,
	}
}

// GetBalance 获取余额
// 注意：原始的 GetBalance 已经有自动创建逻辑，这里直接委托
// 保持这个方法是为了未来可能需要的统一处理
func (m *EnsuredWalletManager) GetBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	// 直接委托给内部实现，因为它已经包含了 EnsureWallet 逻辑
	return m.inner.GetBalance(ctx, tx, merchantID, tokenSymbol)
}

// LockBalance 锁定余额（确保钱包存在）
func (m *EnsuredWalletManager) LockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error {
	// 先确保钱包存在
	g.Log().Debugf(ctx, "EnsuredWalletManager.LockBalance: 确保钱包存在 - merchantID=%d, tokenSymbol=%s", merchantID, tokenSymbol)
	_, err := m.inner.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return err
	}

	// 执行锁定操作
	return m.inner.LockBalance(ctx, tx, merchantID, tokenSymbol, amount)
}

// UnlockBalance 解锁余额（确保钱包存在）
func (m *EnsuredWalletManager) UnlockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error {
	// 先确保钱包存在
	g.Log().Debugf(ctx, "EnsuredWalletManager.UnlockBalance: 确保钱包存在 - merchantID=%d, tokenSymbol=%s", merchantID, tokenSymbol)
	_, err := m.inner.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return err
	}

	// 执行解锁操作
	return m.inner.UnlockBalance(ctx, tx, merchantID, tokenSymbol, amount)
}

// ValidateBalance 验证余额（确保钱包存在）
func (m *EnsuredWalletManager) ValidateBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal, walletType WalletType) error {
	// 如果没有事务，创建一个只读事务
	if tx == nil {
		return g.DB(m.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 确保钱包存在
			_, err := m.inner.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
			if err != nil {
				return err
			}
			// 验证余额
			return m.inner.ValidateBalance(ctx, tx, merchantID, tokenSymbol, amount, walletType)
		})
	}

	// 先确保钱包存在
	_, err := m.inner.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return err
	}

	// 执行验证
	return m.inner.ValidateBalance(ctx, tx, merchantID, tokenSymbol, amount, walletType)
}

// BatchUpdateBalance 批量更新余额（为每个操作确保钱包存在）
func (m *EnsuredWalletManager) BatchUpdateBalance(ctx context.Context, tx gdb.TX, reqs []*TransactionRequest) ([]*TransactionResult, error) {
	// 先为所有请求确保钱包存在，使用 map 避免重复
	merchantTokenMap := make(map[string]bool)
	for _, req := range reqs {
		key := fmt.Sprintf("%d_%s", req.MerchantID, req.TokenSymbol)
		if !merchantTokenMap[key] {
			g.Log().Debugf(ctx, "EnsuredWalletManager.BatchUpdateBalance: 确保钱包存在 - %s", key)
			_, err := m.inner.EnsureWallet(ctx, tx, req.MerchantID, req.TokenSymbol)
			if err != nil {
				return nil, err
			}
			merchantTokenMap[key] = true
		}
	}

	// 执行批量更新
	return m.inner.BatchUpdateBalance(ctx, tx, reqs)
}

// ========== 直接委托的方法 ==========

// UpdateBalance 更新余额（内部已经调用 EnsureWallet，直接委托）
func (m *EnsuredWalletManager) UpdateBalance(ctx context.Context, tx gdb.TX, req *TransactionRequest) (*TransactionResult, error) {
	return m.inner.UpdateBalance(ctx, tx, req)
}

// CreateWallet 创建钱包（直接委托）
func (m *EnsuredWalletManager) CreateWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	return m.inner.CreateWallet(ctx, tx, merchantID, tokenSymbol)
}

// EnsureWallet 确保钱包存在（直接委托）
func (m *EnsuredWalletManager) EnsureWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	return m.inner.EnsureWallet(ctx, tx, merchantID, tokenSymbol)
}

// GetTokenConfig 获取代币配置（直接委托）
func (m *EnsuredWalletManager) GetTokenConfig(tokenSymbol string) (*TokenConfig, error) {
	return m.inner.GetTokenConfig(tokenSymbol)
}

// RegisterTokenConfig 注册代币配置（直接委托）
func (m *EnsuredWalletManager) RegisterTokenConfig(config *TokenConfig) error {
	return m.inner.RegisterTokenConfig(config)
}

// ConvertToInt 将decimal金额转换为int64存储格式（直接委托）
func (m *EnsuredWalletManager) ConvertToInt(amount decimal.Decimal, tokenSymbol string) (int64, error) {
	return m.inner.ConvertToInt(amount, tokenSymbol)
}

// ConvertToDecimal 将int64金额转换为decimal格式（直接委托）
func (m *EnsuredWalletManager) ConvertToDecimal(amount int64, tokenSymbol string) (decimal.Decimal, error) {
	return m.inner.ConvertToDecimal(amount, tokenSymbol)
}

// ValidateAmount 验证金额是否符合代币配置（直接委托）
func (m *EnsuredWalletManager) ValidateAmount(amount decimal.Decimal, tokenSymbol string) error {
	return m.inner.ValidateAmount(amount, tokenSymbol)
}
