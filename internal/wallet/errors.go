package wallet

import (
	"fmt"

	"github.com/gogf/gf/v2/errors/gerror"
)

// 错误码常量定义
const (
	// 通用错误
	ErrCodeInvalidParam     = "WALLET_001"
	ErrCodeInternalError    = "WALLET_002"
	ErrCodeDatabaseError    = "WALLET_003"
	ErrCodeTransactionError = "WALLET_004"

	// 余额相关错误
	ErrCodeInsufficientBalance = "WALLET_101"
	ErrCodeInsufficientFrozen  = "WALLET_102"
	ErrCodeBalanceNotFound     = "WALLET_103"
	ErrCodeBalanceLocked       = "WALLET_104"

	// 金额相关错误
	ErrCodeInvalidAmount  = "WALLET_201"
	ErrCodeAmountTooSmall = "WALLET_202"
	ErrCodeAmountTooLarge = "WALLET_203"
	ErrCodeNegativeAmount = "WALLET_204"
	ErrCodeZeroAmount     = "WALLET_205"

	// 代币相关错误
	ErrCodeTokenNotSupported = "WALLET_301"
	ErrCodeTokenConfigError  = "WALLET_302"
	ErrCodeInvalidSymbol     = "WALLET_303"

	// 钱包类型错误
	ErrCodeInvalidWalletType = "WALLET_401"
	ErrCodeWalletNotFound    = "WALLET_402"

	// 业务相关错误
	ErrCodeDuplicateBusinessID = "WALLET_501"
	ErrCodeInvalidBusinessID   = "WALLET_502"
	ErrCodeOperationNotAllowed = "WALLET_503"

	// 配置相关错误
	ErrCodeConfigNotFound = "WALLET_601"
	ErrCodeConfigInvalid  = "WALLET_602"

	// 事务相关错误
	ErrCodeTransactionRequired = "WALLET_701"
)

// WalletError 钱包错误结构
type WalletError struct {
	Code               string                 `json:"code"`
	Message            string                 `json:"message"`
	Details            string                 `json:"details,omitempty"`
	Context            map[string]interface{} `json:"context,omitempty"`             // 错误上下文
	RecoverySuggestion string                 `json:"recovery_suggestion,omitempty"` // 恢复建议
	UserFriendlyMsg    string                 `json:"user_friendly_msg,omitempty"`   // 用户友好消息
}

// Error 实现error接口
func (e *WalletError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// GetUserFriendlyMessage 获取用户友好消息
func (e *WalletError) GetUserFriendlyMessage() string {
	if e.UserFriendlyMsg != "" {
		return e.UserFriendlyMsg
	}
	return e.Message
}

// GetRecoverySuggestion 获取恢复建议
func (e *WalletError) GetRecoverySuggestion() string {
	return e.RecoverySuggestion
}

// WithContext 添加上下文信息
func (e *WalletError) WithContext(key string, value interface{}) *WalletError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithRecoverySuggestion 添加恢复建议
func (e *WalletError) WithRecoverySuggestion(suggestion string) *WalletError {
	e.RecoverySuggestion = suggestion
	return e
}

// WithUserFriendlyMessage 添加用户友好消息
func (e *WalletError) WithUserFriendlyMessage(msg string) *WalletError {
	e.UserFriendlyMsg = msg
	return e
}

// NewWalletError 创建钱包错误
func NewWalletError(code, message string, details ...string) *WalletError {
	err := &WalletError{
		Code:    code,
		Message: message,
		Context: make(map[string]interface{}),
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewWalletErrorWithContext 创建带上下文的钱包错误
func NewWalletErrorWithContext(code, message string, context map[string]interface{}, details ...string) *WalletError {
	err := &WalletError{
		Code:    code,
		Message: message,
		Context: context,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// 预定义错误创建函数

// ErrInvalidParam 无效参数错误
func ErrInvalidParam(param string, details ...string) *WalletError {
	message := fmt.Sprintf("无效参数: %s", param)
	return NewWalletError(ErrCodeInvalidParam, message, details...)
}

// ErrInsufficientBalance 余额不足错误
func ErrInsufficientBalance(required, available string, details ...string) *WalletError {
	message := fmt.Sprintf("余额不足，需要: %s, 可用: %s", required, available)
	return NewWalletError(ErrCodeInsufficientBalance, message, details...).
		WithRecoverySuggestion("请充值或减少交易金额").
		WithUserFriendlyMessage("余额不足，请检查你的账户余额").
		WithContext("required_amount", required).
		WithContext("available_amount", available)
}

// ErrInsufficientFrozen 冻结余额不足错误
func ErrInsufficientFrozen(required, frozen string, details ...string) *WalletError {
	message := fmt.Sprintf("冻结余额不足，需要: %s, 冻结: %s", required, frozen)
	return NewWalletError(ErrCodeInsufficientFrozen, message, details...).
		WithRecoverySuggestion("请解冻足够的余额").
		WithUserFriendlyMessage("冻结余额不足").
		WithContext("required_amount", required).
		WithContext("frozen_amount", frozen)
}

// ErrInvalidAmount 无效金额错误
func ErrInvalidAmount(amount string, details ...string) *WalletError {
	message := fmt.Sprintf("无效金额: %s", amount)
	return NewWalletError(ErrCodeInvalidAmount, message, details...).
		WithRecoverySuggestion("请输入正确的数字格式").
		WithUserFriendlyMessage("输入的金额格式不正确").
		WithContext("invalid_amount", amount)
}

// ErrAmountTooSmall 金额过小错误
func ErrAmountTooSmall(amount, minAmount string, details ...string) *WalletError {
	message := fmt.Sprintf("金额过小，金额: %s, 最小限额: %s", amount, minAmount)
	return NewWalletError(ErrCodeAmountTooSmall, message, details...).
		WithRecoverySuggestion("请增加交易金额").
		WithUserFriendlyMessage("交易金额低于最小限额").
		WithContext("amount", amount).
		WithContext("min_amount", minAmount)
}

// ErrAmountTooLarge 金额过大错误
func ErrAmountTooLarge(amount, maxAmount string, details ...string) *WalletError {
	message := fmt.Sprintf("金额过大，金额: %s, 最大限额: %s", amount, maxAmount)
	return NewWalletError(ErrCodeAmountTooLarge, message, details...).
		WithRecoverySuggestion("请减少交易金额").
		WithUserFriendlyMessage("交易金额超过最大限额").
		WithContext("amount", amount).
		WithContext("max_amount", maxAmount)
}

// ErrNegativeAmount 负数金额错误
func ErrNegativeAmount(details ...string) *WalletError {
	message := "金额不能为负数"
	return NewWalletError(ErrCodeNegativeAmount, message, details...)
}

// ErrZeroAmount 零金额错误
func ErrZeroAmount(details ...string) *WalletError {
	message := "金额不能为零"
	return NewWalletError(ErrCodeZeroAmount, message, details...)
}

// ErrTokenNotSupported 代币不支持错误
func ErrTokenNotSupported(symbol string, details ...string) *WalletError {
	message := fmt.Sprintf("不支持的代币: %s", symbol)
	return NewWalletError(ErrCodeTokenNotSupported, message, details...).
		WithRecoverySuggestion("请检查支持的代币列表或联系管理员").
		WithUserFriendlyMessage("当前不支持该代币类型").
		WithContext("token_symbol", symbol)
}

// ErrInvalidWalletType 无效钱包类型错误
func ErrInvalidWalletType(walletType string, details ...string) *WalletError {
	message := fmt.Sprintf("无效的钱包类型: %s", walletType)
	return NewWalletError(ErrCodeInvalidWalletType, message, details...)
}

// ErrWalletNotFound 钱包不存在错误
func ErrWalletNotFound(merchantID uint64, symbol string, details ...string) *WalletError {
	message := fmt.Sprintf("钱包不存在，商户ID: %d, 代币: %s", merchantID, symbol)
	return NewWalletError(ErrCodeWalletNotFound, message, details...).
		WithRecoverySuggestion("请联系管理员创建钱包或检查商户ID").
		WithUserFriendlyMessage("钱包不存在，请检查账户设置").
		WithContext("merchant_id", merchantID).
		WithContext("token_symbol", symbol)
}

// ErrDuplicateBusinessID 重复业务ID错误
func ErrDuplicateBusinessID(businessID string, details ...string) error {
	message := fmt.Sprintf("重复的业务ID: %s", businessID)
	return gerror.Wrap(NewWalletError(ErrCodeDuplicateBusinessID, message, details...), message)
}

// ErrInvalidBusinessID 无效业务ID错误
func ErrInvalidBusinessID(businessID string, details ...string) error {
	message := fmt.Sprintf("无效的业务ID: %s", businessID)
	return gerror.Wrap(NewWalletError(ErrCodeInvalidBusinessID, message, details...), message)
}

// ErrOperationNotAllowed 操作不允许错误
func ErrOperationNotAllowed(operation string, details ...string) *WalletError {
	message := fmt.Sprintf("操作不允许: %s", operation)
	return NewWalletError(ErrCodeOperationNotAllowed, message, details...)
}

// ErrConfigNotFound 配置不存在错误
func ErrConfigNotFound(configType string, details ...string) error {
	message := fmt.Sprintf("配置不存在: %s", configType)
	return gerror.Wrap(NewWalletError(ErrCodeConfigNotFound, message, details...), message)
}

// ErrConfigInvalid 配置无效错误
func ErrConfigInvalid(configType string, details ...string) error {
	message := fmt.Sprintf("配置无效: %s", configType)
	return gerror.Wrap(NewWalletError(ErrCodeConfigInvalid, message, details...), message)
}

// ErrDatabaseError 数据库错误
func ErrDatabaseError(operation string, details ...string) error {
	message := fmt.Sprintf("数据库操作失败: %s", operation)
	return gerror.Wrap(NewWalletError(ErrCodeDatabaseError, message, details...), message)
}

// ErrTransactionError 事务错误
func ErrTransactionError(details ...string) error {
	message := "事务处理失败"
	return gerror.Wrap(NewWalletError(ErrCodeTransactionError, message, details...), message)
}

// ErrInternalError 内部错误
func ErrInternalError(details ...string) error {
	message := "内部错误"
	return gerror.Wrap(NewWalletError(ErrCodeInternalError, message, details...), message)
}

// IsWalletError 检查是否为钱包错误
func IsWalletError(err error) bool {
	if err == nil {
		return false
	}
	_, ok := gerror.Cause(err).(*WalletError)
	return ok
}

// GetWalletError 获取钱包错误
func GetWalletError(err error) *WalletError {
	if err == nil {
		return nil
	}
	if walletErr, ok := gerror.Cause(err).(*WalletError); ok {
		return walletErr
	}
	return nil
}

// GetErrorCode 获取错误码
func GetErrorCode(err error) string {
	if walletErr := GetWalletError(err); walletErr != nil {
		return walletErr.Code
	}
	return ""
}

// GetErrorContext 获取错误上下文
func GetErrorContext(err error) map[string]interface{} {
	if walletErr := GetWalletError(err); walletErr != nil {
		return walletErr.Context
	}
	return nil
}

// GetRecoverySuggestion 获取恢复建议
func GetRecoverySuggestion(err error) string {
	if walletErr := GetWalletError(err); walletErr != nil {
		return walletErr.GetRecoverySuggestion()
	}
	return ""
}

// GetUserFriendlyMessage 获取用户友好消息
func GetUserFriendlyMessage(err error) string {
	if walletErr := GetWalletError(err); walletErr != nil {
		return walletErr.GetUserFriendlyMessage()
	}
	return err.Error()
}

// IsRetryableError 判断是否为可重试错误
func IsRetryableError(err error) bool {
	if walletErr := GetWalletError(err); walletErr != nil {
		// 数据库错误和事务错误可重试
		return walletErr.Code == ErrCodeDatabaseError || walletErr.Code == ErrCodeTransactionError
	}
	return false
}

// IsBadRequestError 判断是否为用户请求错误
func IsBadRequestError(err error) bool {
	if walletErr := GetWalletError(err); walletErr != nil {
		// 参数错误、金额错误等为用户请求错误
		return walletErr.Code == ErrCodeInvalidParam ||
			walletErr.Code == ErrCodeInvalidAmount ||
			walletErr.Code == ErrCodeAmountTooSmall ||
			walletErr.Code == ErrCodeAmountTooLarge ||
			walletErr.Code == ErrCodeNegativeAmount ||
			walletErr.Code == ErrCodeZeroAmount ||
			walletErr.Code == ErrCodeInvalidWalletType ||
			walletErr.Code == ErrCodeInvalidBusinessID
	}
	return false
}

// IsInsufficientBalanceError 判断是否为余额不足错误
func IsInsufficientBalanceError(err error) bool {
	if walletErr := GetWalletError(err); walletErr != nil {
		return walletErr.Code == ErrCodeInsufficientBalance || walletErr.Code == ErrCodeInsufficientFrozen
	}
	return false
}
