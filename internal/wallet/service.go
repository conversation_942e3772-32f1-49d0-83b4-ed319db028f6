package wallet

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/shopspring/decimal"
)

// Service 钱包服务，提供简单的事务调用接口
type Service struct {
	manager   Manager
	recorder  TransactionRecorder
	validator Validator
	dbGroup   string
	txManager *TransactionManager
}

// NewService 创建钱包服务
func NewService(options ...Option) *Service {
	manager := NewManager(options...)
	validator := NewValidator(nil, nil) // 将在初始化后更新
	dbGroup := "default"
	// 从选项中提取数据库组配置
	config := &Config{DBGroup: dbGroup}
	for _, option := range options {
		option(config)
	}
	return &Service{
		manager:   manager,
		recorder:  NewTransactionRecorder(options...),
		validator: validator,
		dbGroup:   config.DBGroup,
		txManager: NewTransactionManager(config.DBGroup),
	}
}

// TransferRequest 转账请求
type TransferRequest struct {
	FromMerchantID uint64          `json:"from_merchant_id"` // 发送方商户ID
	ToMerchantID   uint64          `json:"to_merchant_id"`   // 接收方商户ID
	TokenSymbol    string          `json:"token_symbol"`     // 代币符号
	Amount         decimal.Decimal `json:"amount"`           // 转账金额
	Memo           string          `json:"memo"`             // 转账备注
	RequestSource  string          `json:"request_source"`   // 请求来源
	RequestIP      string          `json:"request_ip"`       // 请求IP
}

// DepositRequest 充值请求
type DepositRequest struct {
	MerchantID    uint64          `json:"merchant_id"`    // 商户ID
	TokenSymbol   string          `json:"token_symbol"`   // 代币符号
	Amount        decimal.Decimal `json:"amount"`         // 充值金额
	TxHash        string          `json:"tx_hash"`        // 交易哈希
	Memo          string          `json:"memo"`           // 备注
	RequestSource string          `json:"request_source"` // 请求来源
}

// WithdrawRequest 提现请求
type WithdrawRequest struct {
	MerchantID    uint64          `json:"merchant_id"`    // 商户ID
	TokenSymbol   string          `json:"token_symbol"`   // 代币符号
	Amount        decimal.Decimal `json:"amount"`         // 提现金额
	Address       string          `json:"address"`        // 提现地址
	OrderID       string          `json:"order_id"`       // 订单ID
	Memo          string          `json:"memo"`           // 备注
	RequestSource string          `json:"request_source"` // 请求来源
	RequestIP     string          `json:"request_ip"`     // 请求IP
}

// FreezeRequest 冻结请求
type FreezeRequest struct {
	MerchantID      uint64          `json:"merchant_id"`       // 商户ID
	TokenSymbol     string          `json:"token_symbol"`      // 代币符号
	Amount          decimal.Decimal `json:"amount"`            // 冻结金额
	RelatedEntityID uint64          `json:"related_entity_id"` // 关联实体ID
	Memo            string          `json:"memo"`              // 备注
}

// AdjustRequest 调账请求
type AdjustRequest struct {
	MerchantID    uint64          `json:"merchant_id"`    // 商户ID
	TokenSymbol   string          `json:"token_symbol"`   // 代币符号
	Amount        decimal.Decimal `json:"amount"`         // 调账金额（正数增加，负数减少）
	Reason        string          `json:"reason"`         // 调账原因
	AdminID       uint64          `json:"admin_id"`       // 管理员ID
	RequestSource string          `json:"request_source"` // 请求来源
}

// ApproveWithdrawRequest 提现审批请求
type ApproveWithdrawRequest struct {
	MerchantID    uint64 `json:"merchant_id"`    // 商户ID
	TokenSymbol   string `json:"token_symbol"`   // 代币符号
	WithdrawID    uint64 `json:"withdraw_id"`    // 提现记录ID
	ApproverID    uint64 `json:"approver_id"`    // 审批人ID
	ApprovalNotes string `json:"approval_notes"` // 审批备注
}

// Transfer 转账操作
func (s *Service) Transfer(ctx context.Context, req *TransferRequest) ([]*TransactionResult, error) {
	if err := s.validateTransferRequest(req); err != nil {
		return nil, err
	}

	var results []*TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 验证发送方余额
		if err := s.manager.ValidateBalance(ctx, tx, req.FromMerchantID, req.TokenSymbol, req.Amount, WalletTypeAvailable); err != nil {
			return err
		}

		// 构建转账交易请求
		businessID := s.generateBusinessID("transfer")

		// 发送方交易（扣款）
		fromTxReq := &TransactionRequest{
			MerchantID:       req.FromMerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationTransfer,
			Direction:        DirectionOut,
			WalletType:       WalletTypeAvailable,
			BusinessID:       businessID + "_from",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			TargetMerchantID: &req.ToMerchantID,
		}

		// 接收方交易（入款）
		toTxReq := &TransactionRequest{
			MerchantID:       req.ToMerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationTransfer,
			Direction:        DirectionIn,
			WalletType:       WalletTypeAvailable,
			BusinessID:       businessID + "_to",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			TargetMerchantID: &req.FromMerchantID,
		}

		// 执行转账
		fromResult, err := s.manager.UpdateBalance(ctx, tx, fromTxReq)
		if err != nil {
			return ErrTransactionError("扣除发送方余额失败: " + err.Error())
		}

		toResult, err := s.manager.UpdateBalance(ctx, tx, toTxReq)
		if err != nil {
			return ErrTransactionError("增加接收方余额失败: " + err.Error())
		}

		results = []*TransactionResult{fromResult, toResult}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return results, nil
}

// Deposit 充值操作
func (s *Service) Deposit(ctx context.Context, req *DepositRequest) (*TransactionResult, error) {
	if err := s.validateDepositRequest(req); err != nil {
		return nil, err
	}

	txReq := &TransactionRequest{
		MerchantID:       req.MerchantID,
		TokenSymbol:      req.TokenSymbol,
		Amount:           req.Amount,
		Operation:        OperationDeposit,
		Direction:        DirectionIn,
		WalletType:       WalletTypeAvailable,
		BusinessID:       s.generateBusinessID("deposit"),
		Memo:             &req.Memo,
		RequestSource:    &req.RequestSource,
		RequestReference: &req.TxHash,
	}

	var result *TransactionResult
	err := s.txManager.ReadWrite(ctx, func(tx gdb.TX) error {
		var err error
		result, err = s.manager.UpdateBalance(ctx, tx, txReq)
		return err
	})
	return result, err
}

// PrepareWithdraw 准备提现（冻结余额）
func (s *Service) PrepareWithdraw(ctx context.Context, req *WithdrawRequest) (*TransactionResult, error) {
	if err := s.validateWithdrawRequest(req); err != nil {
		return nil, err
	}

	var result *TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取冻结前的余额快照
		walletBefore, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceBefore := &BalanceSnapshot{
			Available: walletBefore.Available,
			Frozen:    walletBefore.Frozen,
			Total:     walletBefore.Available.Add(walletBefore.Frozen),
		}

		// 冻结提现金额
		if err := s.manager.LockBalance(ctx, tx, req.MerchantID, req.TokenSymbol, req.Amount); err != nil {
			return err
		}

		// 获取冻结后的余额快照
		walletAfter, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceAfter := &BalanceSnapshot{
			Available: walletAfter.Available,
			Frozen:    walletAfter.Frozen,
			Total:     walletAfter.Available.Add(walletAfter.Frozen),
		}

		// 准备两个交易请求
		businessID := s.generateBusinessID("withdraw_prepare")

		// 可用余额减少的交易请求
		availableReq := &TransactionRequest{
			MerchantID:       req.MerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationFreeze,
			Direction:        DirectionOut,        // 可用余额减少
			WalletType:       WalletTypeAvailable, // 记录可用余额
			BusinessID:       businessID + "_available",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			RequestReference: &req.Address,
		}

		// 冻结余额增加的交易请求
		frozenReq := &TransactionRequest{
			MerchantID:       req.MerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationFreeze,
			Direction:        DirectionIn,      // 冻结余额增加
			WalletType:       WalletTypeFrozen, // 记录冻结余额
			BusinessID:       businessID + "_frozen",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			RequestReference: &req.Address,
		}

		// 记录两条交易记录
		results, err := s.recorder.RecordFreezeTransaction(ctx, tx, availableReq, frozenReq, balanceBefore, balanceAfter)
		if err != nil {
			return err
		}

		// 返回第一条记录作为主记录
		result = results[0]
		return nil
	})

	return result, err
}

// CompleteWithdraw 完成提现（直接从冻结余额扣除）
func (s *Service) CompleteWithdraw(ctx context.Context, req *WithdrawRequest, txHash string) (*TransactionResult, error) {
	if err := s.validateWithdrawRequest(req); err != nil {
		return nil, err
	}

	var result *TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取扣除前的余额快照
		walletBefore, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}

		// 检查冻结余额是否足够
		if walletBefore.Frozen.LessThan(req.Amount) {
			return ErrInsufficientFrozen(req.Amount.String(), walletBefore.Frozen.String())
		}

		// 直接从冻结余额中扣除
		txReq := &TransactionRequest{
			MerchantID:       req.MerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationWithdraw,
			Direction:        DirectionOut,
			WalletType:       WalletTypeFrozen, // 从冻结余额扣除
			BusinessID:       s.generateBusinessID("withdraw_complete"),
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			RequestReference: &txHash,
		}

		result, err = s.manager.UpdateBalance(ctx, tx, txReq)
		if err != nil {
			return err
		}

		g.Log().Infof(ctx, "完成提现 - 从冻结余额扣除: merchantID=%d, amount=%s %s, txHash=%s, transactionID=%d",
			req.MerchantID, req.Amount.String(), req.TokenSymbol, txHash, result.TransactionID)

		return nil
	})

	return result, err
}

// CancelWithdraw 取消提现（解冻余额）- 返回两条交易记录
func (s *Service) CancelWithdraw(ctx context.Context, req *WithdrawRequest) ([]*TransactionResult, error) {
	if err := s.validateWithdrawRequest(req); err != nil {
		return nil, err
	}

	var results []*TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取解冻前的余额快照
		walletBefore, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceBefore := &BalanceSnapshot{
			Available: walletBefore.Available,
			Frozen:    walletBefore.Frozen,
			Total:     walletBefore.Available.Add(walletBefore.Frozen),
		}

		// 解冻余额
		if err := s.manager.UnlockBalance(ctx, tx, req.MerchantID, req.TokenSymbol, req.Amount); err != nil {
			return err
		}

		// 获取解冻后的余额快照
		walletAfter, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceAfter := &BalanceSnapshot{
			Available: walletAfter.Available,
			Frozen:    walletAfter.Frozen,
			Total:     walletAfter.Available.Add(walletAfter.Frozen),
		}

		// 准备两个交易请求
		businessID := s.generateBusinessID("withdraw_cancel")

		// 冻结余额减少的交易请求
		frozenReq := &TransactionRequest{
			MerchantID:       req.MerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationUnfreeze,
			Direction:        DirectionOut,     // 冻结余额减少
			WalletType:       WalletTypeFrozen, // 记录冻结余额
			BusinessID:       businessID + "_frozen",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			RequestReference: &req.Address,
		}

		// 可用余额增加的交易请求
		availableReq := &TransactionRequest{
			MerchantID:       req.MerchantID,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        OperationUnfreeze,
			Direction:        DirectionIn,         // 可用余额增加
			WalletType:       WalletTypeAvailable, // 记录可用余额
			BusinessID:       businessID + "_available",
			Memo:             &req.Memo,
			RequestSource:    &req.RequestSource,
			RequestIP:        &req.RequestIP,
			RequestReference: &req.Address,
		}

		// 记录两条交易记录
		results, err = s.recorder.RecordFreezeTransaction(ctx, tx, availableReq, frozenReq, balanceBefore, balanceAfter)
		return err
	})

	return results, err
}

// FreezeBalance 冻结余额
func (s *Service) FreezeBalance(ctx context.Context, req *FreezeRequest) (*TransactionResult, error) {
	if err := s.validateFreezeRequest(req); err != nil {
		return nil, err
	}

	var result *TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取冻结前的余额快照
		walletBefore, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceBefore := &BalanceSnapshot{
			Available: walletBefore.Available,
			Frozen:    walletBefore.Frozen,
			Total:     walletBefore.Available.Add(walletBefore.Frozen),
		}

		// 冻结余额
		if err := s.manager.LockBalance(ctx, tx, req.MerchantID, req.TokenSymbol, req.Amount); err != nil {
			return err
		}

		// 获取冻结后的余额快照
		walletAfter, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceAfter := &BalanceSnapshot{
			Available: walletAfter.Available,
			Frozen:    walletAfter.Frozen,
			Total:     walletAfter.Available.Add(walletAfter.Frozen),
		}

		// 准备两个交易请求
		businessID := s.generateBusinessID("freeze")

		// 可用余额减少的交易请求
		availableReq := &TransactionRequest{
			MerchantID:      req.MerchantID,
			TokenSymbol:     req.TokenSymbol,
			Amount:          req.Amount,
			Operation:       OperationFreeze,
			Direction:       DirectionOut,        // 可用余额减少
			WalletType:      WalletTypeAvailable, // 记录可用余额
			BusinessID:      businessID + "_available",
			RelatedEntityID: &req.RelatedEntityID,
			Memo:            &req.Memo,
		}

		// 冻结余额增加的交易请求
		frozenReq := &TransactionRequest{
			MerchantID:      req.MerchantID,
			TokenSymbol:     req.TokenSymbol,
			Amount:          req.Amount,
			Operation:       OperationFreeze,
			Direction:       DirectionIn,      // 冻结余额增加
			WalletType:      WalletTypeFrozen, // 记录冻结余额
			BusinessID:      businessID + "_frozen",
			RelatedEntityID: &req.RelatedEntityID,
			Memo:            &req.Memo,
		}

		// 记录两条交易记录
		results, err := s.recorder.RecordFreezeTransaction(ctx, tx, availableReq, frozenReq, balanceBefore, balanceAfter)
		if err != nil {
			return err
		}

		// 返回第一条记录作为主记录
		result = results[0]
		return nil
	})

	return result, err
}

// UnfreezeBalance 解冻余额
func (s *Service) UnfreezeBalance(ctx context.Context, req *FreezeRequest) (*TransactionResult, error) {
	if err := s.validateFreezeRequest(req); err != nil {
		return nil, err
	}

	var result *TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取解冻前的余额快照
		walletBefore, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceBefore := &BalanceSnapshot{
			Available: walletBefore.Available,
			Frozen:    walletBefore.Frozen,
			Total:     walletBefore.Available.Add(walletBefore.Frozen),
		}

		// 解冻余额
		if err := s.manager.UnlockBalance(ctx, tx, req.MerchantID, req.TokenSymbol, req.Amount); err != nil {
			return err
		}

		// 获取解冻后的余额快照
		walletAfter, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}
		balanceAfter := &BalanceSnapshot{
			Available: walletAfter.Available,
			Frozen:    walletAfter.Frozen,
			Total:     walletAfter.Available.Add(walletAfter.Frozen),
		}

		// 准备两个交易请求
		businessID := s.generateBusinessID("unfreeze")

		// 冻结余额减少的交易请求
		frozenReq := &TransactionRequest{
			MerchantID:      req.MerchantID,
			TokenSymbol:     req.TokenSymbol,
			Amount:          req.Amount,
			Operation:       OperationUnfreeze,
			Direction:       DirectionOut,     // 冻结余额减少
			WalletType:      WalletTypeFrozen, // 记录冻结余额
			BusinessID:      businessID + "_frozen",
			RelatedEntityID: &req.RelatedEntityID,
			Memo:            &req.Memo,
		}

		// 可用余额增加的交易请求
		availableReq := &TransactionRequest{
			MerchantID:      req.MerchantID,
			TokenSymbol:     req.TokenSymbol,
			Amount:          req.Amount,
			Operation:       OperationUnfreeze,
			Direction:       DirectionIn,         // 可用余额增加
			WalletType:      WalletTypeAvailable, // 记录可用余额
			BusinessID:      businessID + "_available",
			RelatedEntityID: &req.RelatedEntityID,
			Memo:            &req.Memo,
		}

		// 记录两条交易记录（注意：解冻时，先记录冻结减少，再记录可用增加）
		results, err := s.recorder.RecordFreezeTransaction(ctx, tx, availableReq, frozenReq, balanceBefore, balanceAfter)
		if err != nil {
			return err
		}

		// 返回第一条记录作为主记录
		result = results[0]
		return nil
	})

	return result, err
}

// AdjustBalance 调账操作
func (s *Service) AdjustBalance(ctx context.Context, req *AdjustRequest) (*TransactionResult, error) {
	if err := s.validateAdjustRequest(req); err != nil {
		return nil, err
	}

	var direction Direction
	var amount decimal.Decimal

	if req.Amount.IsPositive() {
		direction = DirectionIn
		amount = req.Amount
	} else {
		direction = DirectionOut
		amount = req.Amount.Abs()
	}

	txReq := &TransactionRequest{
		MerchantID:    req.MerchantID,
		TokenSymbol:   req.TokenSymbol,
		Amount:        amount,
		Operation:     OperationAdjust,
		Direction:     direction,
		WalletType:    WalletTypeAvailable,
		BusinessID:    s.generateBusinessID("adjust"),
		Memo:          &req.Reason,
		RequestSource: &req.RequestSource,
	}

	var result *TransactionResult
	err := s.txManager.ReadWrite(ctx, func(tx gdb.TX) error {
		var err error
		result, err = s.manager.UpdateBalance(ctx, tx, txReq)
		return err
	})
	return result, err
}

// ApproveWithdraw 审批提现（生成两条审计记录）
func (s *Service) ApproveWithdraw(ctx context.Context, req *ApproveWithdrawRequest) ([]*TransactionResult, error) {
	if err := s.validateApproveWithdrawRequest(req); err != nil {
		return nil, err
	}

	var results []*TransactionResult
	err := g.DB(s.dbGroup).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取当前余额快照（用于审计记录）
		walletInfo, err := s.manager.GetBalance(ctx, tx, req.MerchantID, req.TokenSymbol)
		if err != nil {
			return err
		}

		balanceSnapshot := &BalanceSnapshot{
			Available: walletInfo.Available,
			Frozen:    walletInfo.Frozen,
			Total:     walletInfo.Available.Add(walletInfo.Frozen),
		}

		// 准备两个审计交易请求
		businessID := s.generateBusinessID("withdraw_approve")
		zeroAmount := decimal.Zero
		approverIDStr := fmt.Sprintf("%d", req.ApproverID)
		approvalMemo1 := fmt.Sprintf("提现审批通过 - 冻结余额确认 (审批人: %s)", approverIDStr)
		approvalMemo2 := fmt.Sprintf("提现审批通过 - 进入处理中 (审批人: %s)", approverIDStr)

		// 第一条记录：冻结余额确认（审计记录，金额为0）
		confirmReq := &TransactionRequest{
			MerchantID:        req.MerchantID,
			TokenSymbol:       req.TokenSymbol,
			Amount:            zeroAmount,
			Operation:         OperationFreeze,
			Direction:         DirectionIn,
			WalletType:        WalletTypeFrozen,
			BusinessID:        businessID + "_confirm",
			RelatedEntityID:   &req.WithdrawID,
			RelatedEntityType: stringPtr("withdraw_approval"),
			Memo:              &approvalMemo1,
			RequestSource:     stringPtr("admin"),
			RequestMetadata: map[string]interface{}{
				"withdraw_id": req.WithdrawID,
				"approver_id": req.ApproverID,
				"action":      "confirm_frozen",
			},
		}

		// 第二条记录：进入处理状态（审计记录，金额为0）
		processReq := &TransactionRequest{
			MerchantID:        req.MerchantID,
			TokenSymbol:       req.TokenSymbol,
			Amount:            zeroAmount,
			Operation:         OperationFreeze,
			Direction:         DirectionOut,
			WalletType:        WalletTypeFrozen,
			BusinessID:        businessID + "_process",
			RelatedEntityID:   &req.WithdrawID,
			RelatedEntityType: stringPtr("withdraw_approval"),
			Memo:              &approvalMemo2,
			RequestSource:     stringPtr("admin"),
			RequestMetadata: map[string]interface{}{
				"withdraw_id": req.WithdrawID,
				"approver_id": req.ApproverID,
				"action":      "start_processing",
			},
		}

		// 记录两条审计交易记录
		results, err = s.recorder.RecordFreezeTransaction(ctx, tx, confirmReq, processReq, balanceSnapshot, balanceSnapshot)
		if err != nil {
			return err
		}

		g.Log().Infof(ctx, "提现审批通过 - 生成审计记录: withdrawID=%d, merchantID=%d, approverID=%d, 确认记录ID=%d, 处理记录ID=%d",
			req.WithdrawID, req.MerchantID, req.ApproverID, results[0].TransactionID, results[1].TransactionID)

		return nil
	})

	return results, err
}

// GetBalance 获取余额
func (s *Service) GetBalance(ctx context.Context, merchantID uint64, tokenSymbol string) (*WalletInfo, error) {
	return s.manager.GetBalance(ctx, nil, merchantID, tokenSymbol)
}

// GetTransactionHistory 获取交易历史
func (s *Service) GetTransactionHistory(ctx context.Context, merchantID uint64, limit, offset int) ([]*TransactionResult, error) {
	return s.recorder.GetTransactionsByMerchant(ctx, merchantID, limit, offset)
}

// generateBusinessID 生成业务ID
func (s *Service) generateBusinessID(prefix string) string {
	return fmt.Sprintf("%s_%s", prefix, guid.S())
}

// 验证方法
func (s *Service) validateTransferRequest(req *TransferRequest) error {
	return s.validator.ValidateTransferRequest(req)
}

func (s *Service) validateDepositRequest(req *DepositRequest) error {
	return s.validator.ValidateDepositRequest(req)
}

func (s *Service) validateWithdrawRequest(req *WithdrawRequest) error {
	return s.validator.ValidateWithdrawRequest(req)
}

func (s *Service) validateFreezeRequest(req *FreezeRequest) error {
	return s.validator.ValidateFreezeRequest(req)
}

func (s *Service) validateAdjustRequest(req *AdjustRequest) error {
	return s.validator.ValidateAdjustRequest(req)
}

func (s *Service) validateApproveWithdrawRequest(req *ApproveWithdrawRequest) error {
	return s.validator.ValidateApproveWithdrawRequest(req)
}

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}
