package cmd

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	"merchant-server/internal/controller/merchant"
	"merchant-server/internal/logic/middleware"

	"github.com/gogf/gf/v2/os/gtime"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()
			err = gtime.SetTimeZone("Asia/Shanghai")
			if err != nil {
				panic(err)
			}
			// 商户API路由
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(middleware.UltimateHandlerResponse)
				group.Middleware(middleware.AuthMiddleware)
				group.Bind(
					merchant.NewV1(),
				)
			})
			s.Run()
			return nil
		},
	}
)
