// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserBackupAccountsDao is the data access object for the table user_backup_accounts.
type UserBackupAccountsDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  UserBackupAccountsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// UserBackupAccountsColumns defines and stores column names for the table user_backup_accounts.
type UserBackupAccountsColumns struct {
	BackupAccountId  string // 备用账户主键ID
	TelegramId       string //
	ChatId           string //
	TelegramUsername string //
	UserId           string // 关联的主用户ID
	VerifiedAt       string // 验证时间
	CreatedAt        string // 创建时间
	UpdatedAt        string // 最后更新时间
	DeletedAt        string // 软删除的时间戳
	IsMaster         string //
	FirstName        string //
}

// userBackupAccountsColumns holds the columns for the table user_backup_accounts.
var userBackupAccountsColumns = UserBackupAccountsColumns{
	BackupAccountId:  "backup_account_id",
	TelegramId:       "telegram_id",
	ChatId:           "chat_id",
	TelegramUsername: "telegram_username",
	UserId:           "user_id",
	VerifiedAt:       "verified_at",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
	IsMaster:         "is_master",
	FirstName:        "first_name",
}

// NewUserBackupAccountsDao creates and returns a new DAO object for table data access.
func NewUserBackupAccountsDao(handlers ...gdb.ModelHandler) *UserBackupAccountsDao {
	return &UserBackupAccountsDao{
		group:    "default",
		table:    "user_backup_accounts",
		columns:  userBackupAccountsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserBackupAccountsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserBackupAccountsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserBackupAccountsDao) Columns() UserBackupAccountsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserBackupAccountsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserBackupAccountsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserBackupAccountsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
