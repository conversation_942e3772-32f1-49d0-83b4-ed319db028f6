// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RedPacketClaimsDao is the data access object for the table red_packet_claims.
type RedPacketClaimsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  RedPacketClaimsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// RedPacketClaimsColumns defines and stores column names for the table red_packet_claims.
type RedPacketClaimsColumns struct {
	ClaimId            string // 领取记录 ID (主键)
	RedPacketId        string // 红包 ID (外键, 指向 red_packets.red_packet_id)
	ClaimerUserId      string // 领取者用户 ID (外键, 指向 users.user_id)
	Amount             string // 领取金额
	TransactionId      string // 关联的资金入账交易 ID (外键, 指向 transactions.transaction_id)
	ClaimedAt          string // 领取时间
	DeletedAt          string // 软删除的时间戳
	SenderUserId       string // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId     string // 接收方用户 ID (外键, 指向 users.user_id)
	SenderUsername     string // 发送方用户名
	ReceiverUsername   string // 接收方用户名
	Status             string // 红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消)
	Symbol             string // 代币符号 (例如: USDT, BTC, ETH)
	NotificationSent   string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt string // 通知发送时间
}

// redPacketClaimsColumns holds the columns for the table red_packet_claims.
var redPacketClaimsColumns = RedPacketClaimsColumns{
	ClaimId:            "claim_id",
	RedPacketId:        "red_packet_id",
	ClaimerUserId:      "claimer_user_id",
	Amount:             "amount",
	TransactionId:      "transaction_id",
	ClaimedAt:          "claimed_at",
	DeletedAt:          "deleted_at",
	SenderUserId:       "sender_user_id",
	ReceiverUserId:     "receiver_user_id",
	SenderUsername:     "sender_username",
	ReceiverUsername:   "receiver_username",
	Status:             "status",
	Symbol:             "symbol",
	NotificationSent:   "notification_sent",
	NotificationSentAt: "notification_sent_at",
}

// NewRedPacketClaimsDao creates and returns a new DAO object for table data access.
func NewRedPacketClaimsDao(handlers ...gdb.ModelHandler) *RedPacketClaimsDao {
	return &RedPacketClaimsDao{
		group:    "default",
		table:    "red_packet_claims",
		columns:  redPacketClaimsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RedPacketClaimsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RedPacketClaimsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RedPacketClaimsDao) Columns() RedPacketClaimsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RedPacketClaimsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RedPacketClaimsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RedPacketClaimsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
