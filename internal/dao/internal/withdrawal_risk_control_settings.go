// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawalRiskControlSettingsDao is the data access object for the table withdrawal_risk_control_settings.
type WithdrawalRiskControlSettingsDao struct {
	table    string                               // table is the underlying table name of the DAO.
	group    string                               // group is the database configuration group name of the current DAO.
	columns  WithdrawalRiskControlSettingsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                   // handlers for customized model modification.
}

// WithdrawalRiskControlSettingsColumns defines and stores column names for the table withdrawal_risk_control_settings.
type WithdrawalRiskControlSettingsColumns struct {
	Id                string //
	ControlType       string // 风控类型: times_limit(提现次数风控), amount_limit(提现金额风控)
	TimePeriod        string // 时间周期数值
	TimeUnit          string // 时间单位: hour(小时), day(天), week(周), month(月)
	MaxTimes          string // 最大提现次数（仅times_limit类型使用）
	MaxAmount         string // 最大提现金额（仅amount_limit类型使用）
	MaxAmountMultiple string // 最大提现金额倍数（仅amount_limit类型使用）  提现金额/充值金额 <=此倍数
	Status            string // 状态: 1-启用, 0-禁用
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 软删除时间
}

// withdrawalRiskControlSettingsColumns holds the columns for the table withdrawal_risk_control_settings.
var withdrawalRiskControlSettingsColumns = WithdrawalRiskControlSettingsColumns{
	Id:                "id",
	ControlType:       "control_type",
	TimePeriod:        "time_period",
	TimeUnit:          "time_unit",
	MaxTimes:          "max_times",
	MaxAmount:         "max_amount",
	MaxAmountMultiple: "max_amount_multiple",
	Status:            "status",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
}

// NewWithdrawalRiskControlSettingsDao creates and returns a new DAO object for table data access.
func NewWithdrawalRiskControlSettingsDao(handlers ...gdb.ModelHandler) *WithdrawalRiskControlSettingsDao {
	return &WithdrawalRiskControlSettingsDao{
		group:    "default",
		table:    "withdrawal_risk_control_settings",
		columns:  withdrawalRiskControlSettingsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WithdrawalRiskControlSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WithdrawalRiskControlSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WithdrawalRiskControlSettingsDao) Columns() WithdrawalRiskControlSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WithdrawalRiskControlSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WithdrawalRiskControlSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WithdrawalRiskControlSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
