// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantApiKeysDao is the data access object for the table merchant_api_keys.
type MerchantApiKeysDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  MerchantApiKeysColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// MerchantApiKeysColumns defines and stores column names for the table merchant_api_keys.
type MerchantApiKeysColumns struct {
	ApiKeyId    string // API 密钥内部 ID (主键)
	MerchantId  string // 所属商户 ID (外键, 指向 merchants.merchant_id)
	ApiKey      string // API Key (公开标识符, 用于请求时识别商户)
	Secret      string // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	SecretHash  string // Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!)
	Label       string // 密钥标签/名称 (方便商户管理, 例如: "生产环境", "测试服务器")
	Status      string // 密钥状态: active-可用, revoked-已撤销, expired-已过期
	Scopes      string // 授权范围 (例如: payment.create, balance.query, withdrawal.create, 使用逗号分隔或 JSON 格式)
	IpWhitelist string // 允许调用此密钥的 IP 地址列表 (逗号分隔或 CIDR, NULL 表示不限制)
	ExpiresAt   string // 密钥过期时间 (NULL 表示永不过期)
	LastUsedAt  string // 最后使用时间 (用于审计和判断活跃度)
	CreatedAt   string // 创建时间
	UpdatedAt   string // 最后更新时间
	DeletedAt   string // 软删除时间
	RateLimit   string // API调用频率限制(每分钟)
	DailyLimit  string // 每日调用限制
}

// merchantApiKeysColumns holds the columns for the table merchant_api_keys.
var merchantApiKeysColumns = MerchantApiKeysColumns{
	ApiKeyId:    "api_key_id",
	MerchantId:  "merchant_id",
	ApiKey:      "api_key",
	Secret:      "secret",
	SecretHash:  "secret_hash",
	Label:       "label",
	Status:      "status",
	Scopes:      "scopes",
	IpWhitelist: "ip_whitelist",
	ExpiresAt:   "expires_at",
	LastUsedAt:  "last_used_at",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	RateLimit:   "rate_limit",
	DailyLimit:  "daily_limit",
}

// NewMerchantApiKeysDao creates and returns a new DAO object for table data access.
func NewMerchantApiKeysDao(handlers ...gdb.ModelHandler) *MerchantApiKeysDao {
	return &MerchantApiKeysDao{
		group:    "default",
		table:    "merchant_api_keys",
		columns:  merchantApiKeysColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantApiKeysDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantApiKeysDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantApiKeysDao) Columns() MerchantApiKeysColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantApiKeysDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantApiKeysDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantApiKeysDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
