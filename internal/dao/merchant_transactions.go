// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantTransactionsDao is the data access object for the table merchant_transactions.
// You can define custom methods on it to extend its functionality as needed.
type merchantTransactionsDao struct {
	*internal.MerchantTransactionsDao
}

var (
	// MerchantTransactions is a globally accessible object for table merchant_transactions operations.
	MerchantTransactions = merchantTransactionsDao{internal.NewMerchantTransactionsDao()}
)

// Add your custom methods and functionality below.
