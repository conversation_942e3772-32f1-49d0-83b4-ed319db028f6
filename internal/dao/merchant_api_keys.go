// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantApiKeysDao is the data access object for the table merchant_api_keys.
// You can define custom methods on it to extend its functionality as needed.
type merchantApiKeysDao struct {
	*internal.MerchantApiKeysDao
}

var (
	// MerchantApiKeys is a globally accessible object for table merchant_api_keys operations.
	MerchantApiKeys = merchantApiKeysDao{internal.NewMerchantApiKeysDao()}
)

// Add your custom methods and functionality below.
