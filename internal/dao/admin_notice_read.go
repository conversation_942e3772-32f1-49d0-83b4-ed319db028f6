// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// adminNoticeReadDao is the data access object for the table admin_notice_read.
// You can define custom methods on it to extend its functionality as needed.
type adminNoticeReadDao struct {
	*internal.AdminNoticeReadDao
}

var (
	// AdminNoticeRead is a globally accessible object for table admin_notice_read operations.
	AdminNoticeRead = adminNoticeReadDao{internal.NewAdminNoticeReadDao()}
)

// Add your custom methods and functionality below.
