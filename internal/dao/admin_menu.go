// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// adminMenuDao is the data access object for the table admin_menu.
// You can define custom methods on it to extend its functionality as needed.
type adminMenuDao struct {
	*internal.AdminMenuDao
}

var (
	// AdminMenu is a globally accessible object for table admin_menu operations.
	AdminMenu = adminMenuDao{internal.NewAdminMenuDao()}
)

// Add your custom methods and functionality below.
