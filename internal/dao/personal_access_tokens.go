// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// personalAccessTokensDao is the data access object for the table personal_access_tokens.
// You can define custom methods on it to extend its functionality as needed.
type personalAccessTokensDao struct {
	*internal.PersonalAccessTokensDao
}

var (
	// PersonalAccessTokens is a globally accessible object for table personal_access_tokens operations.
	PersonalAccessTokens = personalAccessTokensDao{internal.NewPersonalAccessTokensDao()}
)

// Add your custom methods and functionality below.
