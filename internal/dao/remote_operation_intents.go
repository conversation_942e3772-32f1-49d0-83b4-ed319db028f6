// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// remoteOperationIntentsDao is the data access object for the table remote_operation_intents.
// You can define custom methods on it to extend its functionality as needed.
type remoteOperationIntentsDao struct {
	*internal.RemoteOperationIntentsDao
}

var (
	// RemoteOperationIntents is a globally accessible object for table remote_operation_intents operations.
	RemoteOperationIntents = remoteOperationIntentsDao{internal.NewRemoteOperationIntentsDao()}
)

// Add your custom methods and functionality below.
