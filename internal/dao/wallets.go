// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// walletsDao is the data access object for the table wallets.
// You can define custom methods on it to extend its functionality as needed.
type walletsDao struct {
	*internal.WalletsDao
}

var (
	// Wallets is a globally accessible object for table wallets operations.
	Wallets = walletsDao{internal.NewWalletsDao()}
)

// Add your custom methods and functionality below.
