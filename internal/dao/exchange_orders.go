// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// exchangeOrdersDao is the data access object for the table exchange_orders.
// You can define custom methods on it to extend its functionality as needed.
type exchangeOrdersDao struct {
	*internal.ExchangeOrdersDao
}

var (
	// ExchangeOrders is a globally accessible object for table exchange_orders operations.
	ExchangeOrders = exchangeOrdersDao{internal.NewExchangeOrdersDao()}
)

// Add your custom methods and functionality below.
