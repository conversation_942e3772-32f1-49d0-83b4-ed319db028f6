// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantsDao is the data access object for the table merchants.
// You can define custom methods on it to extend its functionality as needed.
type merchantsDao struct {
	*internal.MerchantsDao
}

var (
	// Merchants is a globally accessible object for table merchants operations.
	Merchants = merchantsDao{internal.NewMerchantsDao()}
)

// Add your custom methods and functionality below.
