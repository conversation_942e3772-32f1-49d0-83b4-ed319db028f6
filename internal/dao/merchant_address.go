// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantAddressDao is the data access object for the table merchant_address.
// You can define custom methods on it to extend its functionality as needed.
type merchantAddressDao struct {
	*internal.MerchantAddressDao
}

var (
	// MerchantAddress is a globally accessible object for table merchant_address operations.
	MerchantAddress = merchantAddressDao{internal.NewMerchantAddressDao()}
)

// Add your custom methods and functionality below.
