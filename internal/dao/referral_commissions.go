// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// referralCommissionsDao is the data access object for the table referral_commissions.
// You can define custom methods on it to extend its functionality as needed.
type referralCommissionsDao struct {
	*internal.ReferralCommissionsDao
}

var (
	// ReferralCommissions is a globally accessible object for table referral_commissions operations.
	ReferralCommissions = referralCommissionsDao{internal.NewReferralCommissionsDao()}
)

// Add your custom methods and functionality below.
