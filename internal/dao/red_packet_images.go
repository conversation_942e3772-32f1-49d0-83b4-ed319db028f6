// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// redPacketImagesDao is the data access object for the table red_packet_images.
// You can define custom methods on it to extend its functionality as needed.
type redPacketImagesDao struct {
	*internal.RedPacketImagesDao
}

var (
	// RedPacketImages is a globally accessible object for table red_packet_images operations.
	RedPacketImages = redPacketImagesDao{internal.NewRedPacketImagesDao()}
)

// Add your custom methods and functionality below.
