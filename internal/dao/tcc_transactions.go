// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// tccTransactionsDao is the data access object for the table tcc_transactions.
// You can define custom methods on it to extend its functionality as needed.
type tccTransactionsDao struct {
	*internal.TccTransactionsDao
}

var (
	// TccTransactions is a globally accessible object for table tcc_transactions operations.
	TccTransactions = tccTransactionsDao{internal.NewTccTransactionsDao()}
)

// Add your custom methods and functionality below.
