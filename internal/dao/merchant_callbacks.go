// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantCallbacksDao is the data access object for the table merchant_callbacks.
// You can define custom methods on it to extend its functionality as needed.
type merchantCallbacksDao struct {
	*internal.MerchantCallbacksDao
}

var (
	// MerchantCallbacks is a globally accessible object for table merchant_callbacks operations.
	MerchantCallbacks = merchantCallbacksDao{internal.NewMerchantCallbacksDao()}
)

// Add your custom methods and functionality below.
