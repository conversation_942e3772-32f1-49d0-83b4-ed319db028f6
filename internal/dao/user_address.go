// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// userAddressDao is the data access object for the table user_address.
// You can define custom methods on it to extend its functionality as needed.
type userAddressDao struct {
	*internal.UserAddressDao
}

var (
	// UserAddress is a globally accessible object for table user_address operations.
	UserAddress = userAddressDao{internal.NewUserAddressDao()}
)

// Add your custom methods and functionality below.
