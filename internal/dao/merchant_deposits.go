// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// merchantDepositsDao is the data access object for the table merchant_deposits.
// You can define custom methods on it to extend its functionality as needed.
type merchantDepositsDao struct {
	*internal.MerchantDepositsDao
}

var (
	// MerchantDeposits is a globally accessible object for table merchant_deposits operations.
	MerchantDeposits = merchantDepositsDao{internal.NewMerchantDepositsDao()}
)

// Add your custom methods and functionality below.
