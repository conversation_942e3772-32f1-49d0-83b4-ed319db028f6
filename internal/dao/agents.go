// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"merchant-server/internal/dao/internal"
)

// agentsDao is the data access object for the table agents.
// You can define custom methods on it to extend its functionality as needed.
type agentsDao struct {
	*internal.AgentsDao
}

var (
	// Agents is a globally accessible object for table agents operations.
	Agents = agentsDao{internal.NewAgentsDao()}
)

// Add your custom methods and functionality below.
