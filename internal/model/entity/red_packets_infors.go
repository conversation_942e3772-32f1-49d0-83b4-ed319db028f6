// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketsInfors is the golang structure for table red_packets_infors.
type RedPacketsInfors struct {
	RedPacketId     int64       `json:"redPacketId"     orm:"red_packet_id"     description:"红包 ID (主键)"` // 红包 ID (主键)
	NforId          string      `json:"nforId"          orm:"nfor_id"           description:"红包唯一id"`     // 红包唯一id
	InlineMessageId string      `json:"inlineMessageId" orm:"inline_message_id" description:"红包唯一id"`     // 红包唯一id
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"        description:"创建时间"`       // 创建时间
}
