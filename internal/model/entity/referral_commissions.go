// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// ReferralCommissions is the golang structure for table referral_commissions.
type ReferralCommissions struct {
	CommissionId     int64           `json:"commissionId"     orm:"commission_id"     description:"佣金记录 ID (主键)"`                                                  // 佣金记录 ID (主键)
	TransactionId    int64           `json:"transactionId"    orm:"transaction_id"    description:"关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id)"` // 关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id)
	ReferrerId       int64           `json:"referrerId"       orm:"referrer_id"       description:"获得佣金的用户 ID (外键, 指向 users.user_id)"`                             // 获得佣金的用户 ID (外键, 指向 users.user_id)
	InviteeId        int64           `json:"inviteeId"        orm:"invitee_id"        description:"产生佣金的被推荐人用户 ID (外键, 指向 users.user_id)"`                         // 产生佣金的被推荐人用户 ID (外键, 指向 users.user_id)
	Level            int             `json:"level"            orm:"level"             description:"佣金产生的推荐层级"`                                                     // 佣金产生的推荐层级
	CommissionAmount decimal.Decimal `json:"commissionAmount" orm:"commission_amount" description:"佣金金额"`                                                          // 佣金金额
	CommissionRate   decimal.Decimal `json:"commissionRate"   orm:"commission_rate"   description:"佣金比率 (例如: 0.01 表示 1%)"`                                         // 佣金比率 (例如: 0.01 表示 1%)
	TokenId          int             `json:"tokenId"          orm:"token_id"          description:"佣金代币 ID (外键, 指向 tokens.token_id)"`                              // 佣金代币 ID (外键, 指向 tokens.token_id)
	Status           string          `json:"status"           orm:"status"            description:"佣金状态: pending-待发放, paid-已发放, cancelled-已取消"`                    // 佣金状态: pending-待发放, paid-已发放, cancelled-已取消
	CreatedAt        *gtime.Time     `json:"createdAt"        orm:"created_at"        description:"创建时间"`                                                          // 创建时间
	UpdatedAt        *gtime.Time     `json:"updatedAt"        orm:"updated_at"        description:"最后更新时间"`                                                        // 最后更新时间
	DeletedAt        *gtime.Time     `json:"deletedAt"        orm:"deleted_at"        description:"软删除时间"`                                                         // 软删除时间
}
