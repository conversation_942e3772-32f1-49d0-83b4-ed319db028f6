// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// UserPassFrees is the golang structure for table user_pass_frees.
type UserPassFrees struct {
	UserPassFreesId uint            `json:"userPassFreesId" orm:"user_pass_frees_id" description:"主键ID"`                                          // 主键ID
	UserId          uint64          `json:"userId"          orm:"user_id"            description:"用户ID (Foreign key to users table recommended)"` // 用户ID (Foreign key to users table recommended)
	Symbol          string          `json:"symbol"          orm:"symbol"             description:"币种名称"`                                          // 币种名称
	Amount          decimal.Decimal `json:"amount"          orm:"amount"             description:"免密金额"`                                          // 免密金额
	CreatedAt       *gtime.Time     `json:"createdAt"       orm:"created_at"         description:"创建时间"`                                          // 创建时间
	UpdatedAt       *gtime.Time     `json:"updatedAt"       orm:"updated_at"         description:"最后更新时间"`                                        // 最后更新时间
}
