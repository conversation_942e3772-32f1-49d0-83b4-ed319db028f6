// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// AuthPaymentOrders is the golang structure for table auth_payment_orders.
type AuthPaymentOrders struct {
	Id                    uint64          `json:"id"                    orm:"id"                      description:"主键ID"`                    // 主键ID
	OrderNo               string          `json:"orderNo"               orm:"order_no"                description:"系统订单号"`                   // 系统订单号
	MerchantId            uint64          `json:"merchantId"            orm:"merchant_id"             description:"商户ID"`                    // 商户ID
	UserAccount           string          `json:"userAccount"           orm:"user_account"            description:"用户账户标识(users.account)"`   // 用户账户标识(users.account)
	UserId                uint64          `json:"userId"                orm:"user_id"                 description:"用户ID(查询到后填充)"`            // 用户ID(查询到后填充)
	OrderType             string          `json:"orderType"             orm:"order_type"              description:"订单类型: deduct-扣款, add-加款"` // 订单类型: deduct-扣款, add-加款
	TokenSymbol           string          `json:"tokenSymbol"           orm:"token_symbol"            description:"代币符号"`                    // 代币符号
	Amount                decimal.Decimal `json:"amount"                orm:"amount"                  description:"订单金额"`                    // 订单金额
	AuthReason            string          `json:"authReason"            orm:"auth_reason"             description:"授权原因/说明"`                 // 授权原因/说明
	MerchantOrderNo       string          `json:"merchantOrderNo"       orm:"merchant_order_no"       description:"商户订单号"`                   // 商户订单号
	MerchantTransactionId uint64          `json:"merchantTransactionId" orm:"merchant_transaction_id" description:"商户交易记录ID"`                // 商户交易记录ID
	UserTransactionId     uint64          `json:"userTransactionId"     orm:"user_transaction_id"     description:"用户交易记录ID"`                // 用户交易记录ID
	Status                string          `json:"status"                orm:"status"                  description:"订单状态"`                    // 订单状态
	CallbackUrl           string          `json:"callbackUrl"           orm:"callback_url"            description:"回调URL(使用商户默认或指定)"`        // 回调URL(使用商户默认或指定)
	CallbackStatus        string          `json:"callbackStatus"        orm:"callback_status"         description:"回调状态"`                    // 回调状态
	CallbackAttempts      int             `json:"callbackAttempts"      orm:"callback_attempts"       description:"回调尝试次数"`                  // 回调尝试次数
	LastCallbackAt        *gtime.Time     `json:"lastCallbackAt"        orm:"last_callback_at"        description:"最后回调时间"`                  // 最后回调时间
	NextCallbackAt        *gtime.Time     `json:"nextCallbackAt"        orm:"next_callback_at"        description:"下次回调时间"`                  // 下次回调时间
	CallbackResponse      string          `json:"callbackResponse"      orm:"callback_response"       description:"最后回调响应"`                  // 最后回调响应
	ExpireAt              *gtime.Time     `json:"expireAt"              orm:"expire_at"               description:"订单过期时间(仅扣款订单)"`           // 订单过期时间(仅扣款订单)
	CompletedAt           *gtime.Time     `json:"completedAt"           orm:"completed_at"            description:"完成时间"`                    // 完成时间
	ErrorMessage          string          `json:"errorMessage"          orm:"error_message"           description:"错误信息"`                    // 错误信息
	RequestIp             string          `json:"requestIp"             orm:"request_ip"              description:"请求IP"`                    // 请求IP
	CreatedAt             *gtime.Time     `json:"createdAt"             orm:"created_at"              description:"创建时间"`                    // 创建时间
	UpdatedAt             *gtime.Time     `json:"updatedAt"             orm:"updated_at"              description:"更新时间"`                    // 更新时间
	CallbackBot           string          `json:"callbackBot"           orm:"callback_bot"            description:"回调telegram 机器人"`          // 回调telegram 机器人
}
