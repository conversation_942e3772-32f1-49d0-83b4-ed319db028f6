// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// WithdrawalApprovalSettings is the golang structure for table withdrawal_approval_settings.
type WithdrawalApprovalSettings struct {
	Id                uint64          `json:"id"                orm:"id"                  description:""`                      //
	Currency          string          `json:"currency"          orm:"currency"            description:"币种符号 (如 USDT, BTC)"`    // 币种符号 (如 USDT, BTC)
	Network           string          `json:"network"           orm:"network"             description:"网络类型 (如 TRC20, ERC20)"` // 网络类型 (如 TRC20, ERC20)
	AutoReleaseMin    decimal.Decimal `json:"autoReleaseMin"    orm:"auto_release_min"    description:"无需审核自动放币最小金额"`          // 无需审核自动放币最小金额
	AutoReleaseMax    decimal.Decimal `json:"autoReleaseMax"    orm:"auto_release_max"    description:"无需审核自动放币最大金额"`          // 无需审核自动放币最大金额
	ApprovalAutoMin   decimal.Decimal `json:"approvalAutoMin"   orm:"approval_auto_min"   description:"审核确定后自动放币最小金额"`         // 审核确定后自动放币最小金额
	ApprovalAutoMax   decimal.Decimal `json:"approvalAutoMax"   orm:"approval_auto_max"   description:"审核确定后自动放币最大金额"`         // 审核确定后自动放币最大金额
	ApprovalManualMin decimal.Decimal `json:"approvalManualMin" orm:"approval_manual_min" description:"审核确定后手动放币最小金额"`         // 审核确定后手动放币最小金额
	ApprovalManualMax decimal.Decimal `json:"approvalManualMax" orm:"approval_manual_max" description:"审核确定后手动放币最大金额"`         // 审核确定后手动放币最大金额
	Status            int             `json:"status"            orm:"status"              description:"状态: 1-启用, 0-禁用"`        // 状态: 1-启用, 0-禁用
	CreatedAt         *gtime.Time     `json:"createdAt"         orm:"created_at"          description:"创建时间"`                  // 创建时间
	UpdatedAt         *gtime.Time     `json:"updatedAt"         orm:"updated_at"          description:"更新时间"`                  // 更新时间
	DeletedAt         *gtime.Time     `json:"deletedAt"         orm:"deleted_at"          description:"软删除时间"`                 // 软删除时间
}
