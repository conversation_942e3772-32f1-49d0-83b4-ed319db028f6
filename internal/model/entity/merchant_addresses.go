// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantAddresses is the golang structure for table merchant_addresses.
type MerchantAddresses struct {
	Id         uint64      `json:"id"         orm:"id"          description:""`      //
	MerchantId uint64      `json:"merchantId" orm:"merchant_id" description:"商户ID"`  // 商户ID
	UserLabel  string      `json:"userLabel"  orm:"user_label"  description:"用户标识"`  // 用户标识
	ChainType  string      `json:"chainType"  orm:"chain_type"  description:"链类型"`   // 链类型
	TokenType  string      `json:"tokenType"  orm:"token_type"  description:"代币类型"`  // 代币类型
	Address    string      `json:"address"    orm:"address"     description:"地址"`    // 地址
	AddressId  uint64      `json:"addressId"  orm:"address_id"  description:"地址表ID"` // 地址表ID
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`      //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`      //
}
