// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Merchants is the golang structure for table merchants.
type Merchants struct {
	MerchantId         uint64      `json:"merchantId"         orm:"merchant_id"          description:"商户内部ID (主键)"`               // 商户内部ID (主键)
	UserId             int64       `json:"userId"             orm:"user_id"              description:"用户id"`                      // 用户id
	MerchantName       string      `json:"merchantName"       orm:"merchant_name"        description:"商户名称 (对外展示/内部识别)"`          // 商户名称 (对外展示/内部识别)
	BusinessName       string      `json:"businessName"       orm:"business_name"        description:"公司/业务注册名称 (可选)"`            // 公司/业务注册名称 (可选)
	Email              string      `json:"email"              orm:"email"                description:"商户邮箱 (用于登录和通知)"`            // 商户邮箱 (用于登录和通知)
	EmailVerifiedAt    *gtime.Time `json:"emailVerifiedAt"    orm:"email_verified_at"    description:"邮箱验证时间"`                    // 邮箱验证时间
	PaymentPassword    string      `json:"paymentPassword"    orm:"payment_password"     description:"支付密码 (经过哈希处理)"`             // 支付密码 (经过哈希处理)
	PaymentPasswordSet int         `json:"paymentPasswordSet" orm:"payment_password_set" description:"是否设置支付密码"`                  // 是否设置支付密码
	AreaCode           string      `json:"areaCode"           orm:"area_code"            description:"电话区号"`                      // 电话区号
	Phone              string      `json:"phone"              orm:"phone"                description:"联系电话"`                      // 联系电话
	ContactEmail       string      `json:"contactEmail"       orm:"contact_email"        description:"备用联系邮箱"`                    // 备用联系邮箱
	WebsiteUrl         string      `json:"websiteUrl"         orm:"website_url"          description:"商户网站URL"`                   // 商户网站URL
	Google2FaSecret    string      `json:"google2FaSecret"    orm:"google2fa_secret"     description:"Google 2FA密钥"`              // Google 2FA密钥
	Google2FaEnabled   int         `json:"google2FaEnabled"   orm:"google2fa_enabled"    description:"Google 2FA是否启用"`            // Google 2FA是否启用
	RecoveryCodes      string      `json:"recoveryCodes"      orm:"recovery_codes"       description:"恢复代码"`                      // 恢复代码
	WithdrawPermission int         `json:"withdrawPermission" orm:"withdraw_permission"  description:"提现权限"`                      // 提现权限
	RechargePermission int         `json:"rechargePermission" orm:"recharge_permission"  description:"充值权限"`                      // 充值权限
	ApiPermission      int         `json:"apiPermission"      orm:"api_permission"       description:"API调用权限"`                   // API调用权限
	Status             int         `json:"status"             orm:"status"               description:"商户状态 (-1:待审核, 0:禁用, 1:启用)"` // 商户状态 (-1:待审核, 0:禁用, 1:启用)
	IsStop             int         `json:"isStop"             orm:"is_stop"              description:"账户暂停状态 (0:活跃, 1:已暂停)"`      // 账户暂停状态 (0:活跃, 1:已暂停)
	Reason             string      `json:"reason"             orm:"reason"               description:"暂停或状态变更原因"`                 // 暂停或状态变更原因
	CallbackUrl        string      `json:"callbackUrl"        orm:"callback_url"         description:"回调URL"`                     // 回调URL
	WebhookSecret      string      `json:"webhookSecret"      orm:"webhook_secret"       description:"回调密钥"`                      // 回调密钥
	Language           string      `json:"language"           orm:"language"             description:"语言偏好"`                      // 语言偏好
	Timezone           string      `json:"timezone"           orm:"timezone"             description:"时区设置"`                      // 时区设置
	Avatar             string      `json:"avatar"             orm:"avatar"               description:"头像URL"`                     // 头像URL
	CurrentToken       string      `json:"currentToken"       orm:"current_token"        description:"当前认证令牌"`                    // 当前认证令牌
	LastLoginTime      *gtime.Time `json:"lastLoginTime"      orm:"last_login_time"      description:"最后登录时间"`                    // 最后登录时间
	LastLoginIp        string      `json:"lastLoginIp"        orm:"last_login_ip"        description:"最后登录IP"`                    // 最后登录IP
	LoginAttempts      int         `json:"loginAttempts"      orm:"login_attempts"       description:"登录尝试次数"`                    // 登录尝试次数
	LockedUntil        *gtime.Time `json:"lockedUntil"        orm:"locked_until"         description:"账户锁定到期时间"`                  // 账户锁定到期时间
	Notes              string      `json:"notes"              orm:"notes"                description:"内部备注/审核意见"`                 // 内部备注/审核意见
	ApprovedAt         *gtime.Time `json:"approvedAt"         orm:"approved_at"          description:"审核通过时间"`                    // 审核通过时间
	ApprovedBy         uint64      `json:"approvedBy"         orm:"approved_by"          description:"审核人ID"`                     // 审核人ID
	CreatedAt          *gtime.Time `json:"createdAt"          orm:"created_at"           description:"创建时间"`                      // 创建时间
	UpdatedAt          *gtime.Time `json:"updatedAt"          orm:"updated_at"           description:"更新时间"`                      // 更新时间
	DeletedAt          *gtime.Time `json:"deletedAt"          orm:"deleted_at"           description:"软删除时间"`                     // 软删除时间
}
