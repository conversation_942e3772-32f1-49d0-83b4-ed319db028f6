// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// IpAccessList is the golang structure of table ip_access_list for DAO operations like Where/Data.
type IpAccessList struct {
	g.Meta      `orm:"table:ip_access_list, do:true"`
	Id          interface{} // 记录ID
	UserId      interface{} // 用户ID (关联用户表)
	AgentId     interface{} // 代理ID (关联代理表)
	AdminId     interface{} // 管理员ID (关联管理员表)
	MerchantId  interface{} // 管理员ID (关联管理员表)
	ListType    interface{} // 列表类型 (blacklist-黑名单, whitelist-白名单)
	UseType     interface{} // 适用类型
	Description interface{} // 规则描述 (可选, 如添加原因或来源)
	IpAddress   interface{} // IP地址 (支持IPv4和IPv6)
	Reason      interface{} // 原因 (拉黑或加白)
	AddedBy     interface{} // 操作者 (用户名或系统标识)
	ExpiresAt   *gtime.Time // 过期时间 (NULL表示永久)
	IsEnabled   interface{} // 是否启用 (1-启用, 0-禁用)
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	DeletedAt   *gtime.Time // 软删除的时间戳
}
