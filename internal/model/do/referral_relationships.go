// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ReferralRelationships is the golang structure of table referral_relationships for DAO operations like Where/Data.
type ReferralRelationships struct {
	g.Meta         `orm:"table:referral_relationships, do:true"`
	Type           interface{} // 上级类型 代理，用户
	RelationshipId interface{} // 关系 ID (主键)
	UserId         interface{} // 被推荐人用户 ID (外键, 指向 users.user_id)
	ReferrerId     interface{} // 推荐人用户 ID (外键, 指向 users.user_id)
	Level          interface{} // 推荐层级 (1 表示直接推荐, 2 表示间接推荐, 以此类推)
	CreatedAt      *gtime.Time // 关系创建时间
	DeletedAt      *gtime.Time // 软删除的时间戳
}
