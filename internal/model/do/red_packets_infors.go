// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketsInfors is the golang structure of table red_packets_infors for DAO operations like Where/Data.
type RedPacketsInfors struct {
	g.Meta          `orm:"table:red_packets_infors, do:true"`
	RedPacketId     interface{} // 红包 ID (主键)
	NforId          interface{} // 红包唯一id
	InlineMessageId interface{} // 红包唯一id
	CreatedAt       *gtime.Time // 创建时间
}
