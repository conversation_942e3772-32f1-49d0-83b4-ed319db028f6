// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserPassFrees is the golang structure of table user_pass_frees for DAO operations like Where/Data.
type UserPassFrees struct {
	g.Meta          `orm:"table:user_pass_frees, do:true"`
	UserPassFreesId interface{} // 主键ID
	UserId          interface{} // 用户ID (Foreign key to users table recommended)
	Symbol          interface{} // 币种名称
	Amount          interface{} // 免密金额
	CreatedAt       *gtime.Time // 创建时间
	UpdatedAt       *gtime.Time // 最后更新时间
}
