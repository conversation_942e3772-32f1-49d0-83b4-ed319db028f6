// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantAddresses is the golang structure of table merchant_addresses for DAO operations like Where/Data.
type MerchantAddresses struct {
	g.Meta     `orm:"table:merchant_addresses, do:true"`
	Id         interface{} //
	MerchantId interface{} // 商户ID
	UserLabel  interface{} // 用户标识
	ChainType  interface{} // 链类型
	TokenType  interface{} // 代币类型
	Address    interface{} // 地址
	AddressId  interface{} // 地址表ID
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
}
