// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawalAmountSettings is the golang structure of table withdrawal_amount_settings for DAO operations like Where/Data.
type WithdrawalAmountSettings struct {
	g.Meta    `orm:"table:withdrawal_amount_settings, do:true"`
	Id        interface{} //
	Currency  interface{} // 币种符号 (如 USDT, BTC)
	Network   interface{} // 网络类型 (如 TRC20, ERC20)
	MinAmount interface{} // 单笔最小提现金额
	MaxAmount interface{} // 单笔最大提现金额
	Status    interface{} // 状态: 1-启用, 0-禁用
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 软删除时间
}
