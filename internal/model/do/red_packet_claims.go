// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketClaims is the golang structure of table red_packet_claims for DAO operations like Where/Data.
type RedPacketClaims struct {
	g.Meta             `orm:"table:red_packet_claims, do:true"`
	ClaimId            interface{} // 领取记录 ID (主键)
	RedPacketId        interface{} // 红包 ID (外键, 指向 red_packets.red_packet_id)
	ClaimerUserId      interface{} // 领取者用户 ID (外键, 指向 users.user_id)
	Amount             interface{} // 领取金额
	TransactionId      interface{} // 关联的资金入账交易 ID (外键, 指向 transactions.transaction_id)
	ClaimedAt          *gtime.Time // 领取时间
	DeletedAt          *gtime.Time // 软删除的时间戳
	SenderUserId       interface{} // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId     interface{} // 接收方用户 ID (外键, 指向 users.user_id)
	SenderUsername     interface{} // 发送方用户名
	ReceiverUsername   interface{} // 接收方用户名
	Status             interface{} // 红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消)
	Symbol             interface{} // 代币符号 (例如: USDT, BTC, ETH)
	NotificationSent   interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt *gtime.Time // 通知发送时间
}
