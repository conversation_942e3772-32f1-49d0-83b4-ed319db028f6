// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AuthPaymentOrders is the golang structure of table auth_payment_orders for DAO operations like Where/Data.
type AuthPaymentOrders struct {
	g.Meta                `orm:"table:auth_payment_orders, do:true"`
	Id                    interface{} // 主键ID
	OrderNo               interface{} // 系统订单号
	MerchantId            interface{} // 商户ID
	UserAccount           interface{} // 用户账户标识(users.account)
	UserId                interface{} // 用户ID(查询到后填充)
	OrderType             interface{} // 订单类型: deduct-扣款, add-加款
	TokenSymbol           interface{} // 代币符号
	Amount                interface{} // 订单金额
	AuthReason            interface{} // 授权原因/说明
	MerchantOrderNo       interface{} // 商户订单号
	MerchantTransactionId interface{} // 商户交易记录ID
	UserTransactionId     interface{} // 用户交易记录ID
	Status                interface{} // 订单状态
	CallbackUrl           interface{} // 回调URL(使用商户默认或指定)
	CallbackStatus        interface{} // 回调状态
	CallbackAttempts      interface{} // 回调尝试次数
	LastCallbackAt        *gtime.Time // 最后回调时间
	NextCallbackAt        *gtime.Time // 下次回调时间
	CallbackResponse      interface{} // 最后回调响应
	ExpireAt              *gtime.Time // 订单过期时间(仅扣款订单)
	CompletedAt           *gtime.Time // 完成时间
	ErrorMessage          interface{} // 错误信息
	RequestIp             interface{} // 请求IP
	CreatedAt             *gtime.Time // 创建时间
	UpdatedAt             *gtime.Time // 更新时间
	CallbackBot           interface{} // 回调telegram 机器人
	NotificationSent      interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    *gtime.Time // 通知发送时间
}
