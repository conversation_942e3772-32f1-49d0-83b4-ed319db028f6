// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Withdraws is the golang structure of table withdraws for DAO operations like Where/Data.
type Withdraws struct {
	g.Meta             `orm:"table:withdraws, do:true"`
	WithdrawsId        interface{} // 主键ID
	MerchantId         interface{} // 用户ID (Foreign key to users table recommended)
	TokenId            interface{} // 币种ID
	WalletId           interface{} // 用户钱包ID
	Name               interface{} // 币种ID
	Chan               interface{} //
	OrderNo            interface{} // 提现订单号 (Should be unique)
	Address            interface{} // 提币目标地址
	RecipientName      interface{} // 法币收款人姓名
	RecipientAccount   interface{} // 法币收款账户
	RecipientQrcode    interface{} // 法币收款账户
	Amount             interface{} // 申请提现金额
	HandlingFee        interface{} // 提现手续费
	ActualAmount       interface{} // 实际到账金额
	State              interface{} // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
	RefuseReasonZh     interface{} // 拒绝原因 (中文)
	RefuseReasonEn     interface{} // 拒绝原因 (英文)
	TxHash             interface{} // 链上交易哈希/ID
	ErrorMessage       *gjson.Json // 失败或错误信息
	UserRemark         interface{} // 用户提现备注
	AdminRemark        interface{} // 管理员审核备注
	CreatedAt          *gtime.Time // 创建时间
	CheckedAt          *gtime.Time // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt       *gtime.Time // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt        *gtime.Time // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt          *gtime.Time // 最后更新时间
	Retries            interface{} //
	NergyState         interface{} // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	NotificationSent   interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt *gtime.Time // 通知发送时间
	FiatType           interface{} // 法币提现类型 alipay_account 支付宝账号  alipay_qr 支付宝二维码  wechat_qr 微信二维码
}
