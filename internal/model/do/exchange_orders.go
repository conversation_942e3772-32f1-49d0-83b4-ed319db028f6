// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ExchangeOrders is the golang structure of table exchange_orders for DAO operations like Where/Data.
type ExchangeOrders struct {
	g.Meta                `orm:"table:exchange_orders, do:true"`
	OrderId               interface{} // 订单内部 ID (主键)
	OrderSn               interface{} // 订单唯一编号 (业务层生成, 便于跟踪和对外展示)
	UserId                interface{} // 执行兑换的用户 ID (外键关联 users.user_id)
	ProductId             interface{} // 关联的兑换产品 ID (外键关联 exchange_products.product_id)
	BaseToken             interface{} // 基础代币 ID (来自 exchange_products)
	QuoteToken            interface{} // 计价代币 ID (来自 exchange_products)
	Symbol                interface{} // 交易对符号 (来自 exchange_products)
	TradeType             interface{} // 交易类型: buy-用户买入基础代币(花费计价代币), sell-用户卖出基础代币(获得计价代币)
	AmountBase            interface{} // 涉及的基础代币数量（截断后）
	AmountQuote           interface{} // 涉及的计价代币数量（截断后）
	OriginalFromAmount    interface{} // 原始兑换金额（未截断）
	OriginalToAmount      interface{} // 原始收到金额（未截断）
	Price                 interface{} // 本次交易实际成交价格 (计价代币数量 / 基础代币数量)
	SpreadAmount          interface{} // 点差金额
	SpreadRate            interface{} // 点差率
	FeeAmount             interface{} // 收取的手续费金额
	FeeTokenId            interface{} // 手续费收取的币种 ID (通常是 base_token_id 或 quote_token_id, 根据产品配置决定)
	TransactionHash       interface{} // 交易哈希（如果有）
	SenderTransactionId   interface{} // Wallet transaction ID for debit operation
	ReceiverTransactionId interface{} // Wallet transaction ID for credit operation
	QuoteId               interface{} // 关联的报价ID
	Status                interface{} // 订单状态
	ErrorMessage          interface{} // 订单失败或取消时的错误信息
	ClientOrderId         interface{} // 客户端提供的订单 ID (用于幂等性检查或客户端关联)
	CreatedAt             *gtime.Time // 订单创建时间
	ExecutedAt            *gtime.Time // 实际执行时间
	CompletedAt           *gtime.Time // 完成时间
	UpdatedAt             *gtime.Time // 订单最后更新时间
	DeletedAt             *gtime.Time // 软删除时间
	OutputAmountBeforeFee interface{} // 扣费前输出金额
	OutputAmountAfterFee  interface{} // 扣费后输出金额
	FeeCalculationMethod  interface{} // 手续费计算方法
}
