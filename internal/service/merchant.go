package service

import (
	"context"
	v1 "merchant-server/api/merchant/v1"
)

type (
	IDepositService interface {
		GetDepositAddress(ctx context.Context, merchantId int64, req *v1.DepositAddressReq) (*v1.DepositAddressData, error)
		QueryDeposits(ctx context.Context, merchantId int64, req *v1.DepositQueryReq) (*v1.PageResponse, error)
		GetDepositDetail(ctx context.Context, merchantId int64, req *v1.DepositDetailReq) (*v1.DepositRecord, error)
		GetAddressList(ctx context.Context, merchantId int64, req *v1.DepositAddressListReq) (*v1.PageResponse, error)
		// 新增：处理充值确认（区块链回调时调用）
		ProcessDeposit(ctx context.Context, merchantId int64, txHash, toAddress, chain, token, amount string) error
	}

	IWithdrawService interface {
		CreateWithdraw(ctx context.Context, merchantId int64, req *v1.WithdrawCreateReq) (*v1.WithdrawCreateData, error)
		QueryWithdraws(ctx context.Context, merchantId int64, req *v1.WithdrawQueryReq) (*v1.PageResponse, error)
		GetWithdrawDetail(ctx context.Context, merchantId int64, req *v1.WithdrawDetailReq) (*v1.WithdrawRecord, error)
		CancelWithdraw(ctx context.Context, merchantId int64, req *v1.WithdrawCancelReq) (*v1.WithdrawCancelData, error)
		GetWithdrawFee(ctx context.Context, merchantId int64, req *v1.WithdrawFeeReq) (*v1.WithdrawFeeData, error)
	}

	ITransactionService interface {
		QueryTransactions(ctx context.Context, merchantId int64, req *v1.TransactionQueryReq) (*v1.PageResponse, error)
		GetTransactionDetail(ctx context.Context, merchantId int64, req *v1.TransactionDetailReq) (*v1.TransactionDetailData, error)
		GetTransactionStats(ctx context.Context, merchantId int64, req *v1.TransactionStatsReq) (*v1.TransactionStatsData, error)
	}

	IAuthPaymentService interface {
		CreateAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentCreateReq) (*v1.AuthPaymentCreateData, error)
		QueryAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentQueryReq) (*v1.AuthPaymentQueryData, error)
		ListAuthPaymentOrders(ctx context.Context, merchantId int64, req *v1.AuthPaymentListReq) (*v1.AuthPaymentListData, error)
		ProcessAuthPaymentCallback(ctx context.Context, orderId int64) error
	}
)

var (
	localDepositService     IDepositService
	localWithdrawService    IWithdrawService
	localTransactionService ITransactionService
	localAuthPaymentService IAuthPaymentService
)

func DepositService() IDepositService {
	if localDepositService == nil {
		panic("implement not found for interface IDepositService, forgot register?")
	}
	return localDepositService
}

func WithdrawService() IWithdrawService {
	if localWithdrawService == nil {
		panic("implement not found for interface IWithdrawService, forgot register?")
	}
	return localWithdrawService
}

func TransactionService() ITransactionService {
	if localTransactionService == nil {
		panic("implement not found for interface ITransactionService, forgot register?")
	}
	return localTransactionService
}

func RegisterDepositService(i IDepositService) {
	localDepositService = i
}

func RegisterWithdrawService(i IWithdrawService) {
	localWithdrawService = i
}

func RegisterTransactionService(i ITransactionService) {
	localTransactionService = i
}

func AuthPaymentService() IAuthPaymentService {
	if localAuthPaymentService == nil {
		panic("implement not found for interface IAuthPaymentService, forgot register?")
	}
	return localAuthPaymentService
}

func RegisterAuthPaymentService(i IAuthPaymentService) {
	localAuthPaymentService = i
}
