package service

import (
	"context"

	"github.com/shopspring/decimal"
)

// IWithdrawalConfig defines the interface for withdrawal configuration service operations.
type IWithdrawalConfig interface {
	// GetAmountLimits retrieves withdrawal amount limits from the database
	GetAmountLimits(ctx context.Context, currency, network string) (*WithdrawalAmountLimits, error)

	// GetFeeSettings retrieves withdrawal fee settings from the database
	GetFeeSettings(ctx context.Context, currency, network string) (*WithdrawalFeeSettings, error)

	// GetFeeSettingsByAmount retrieves withdrawal fee settings based on amount range
	GetFeeSettingsByAmount(ctx context.Context, currency, network string, amount decimal.Decimal) (*WithdrawalFeeSettings, error)

	// CalculateWithdrawalFee calculates the withdrawal fee based on amount and settings
	CalculateWithdrawalFee(ctx context.Context, currency, network string, amount decimal.Decimal) (decimal.Decimal, error)

	// ValidateWithdrawalAmount validates if the amount is within allowed limits
	ValidateWithdrawalAmount(ctx context.Context, currency, network string, amount decimal.Decimal) error

	// IsWithdrawalEnabled checks if withdrawal is globally enabled
	IsWithdrawalEnabled(ctx context.Context) (bool, error)

	// GetApprovalSettings retrieves withdrawal approval settings from the database
	GetApprovalSettings(ctx context.Context, currency, network string) (*WithdrawalApprovalSettings, error)

	// DetermineWithdrawalStatus determines audit and processing status based on amount
	DetermineWithdrawalStatus(ctx context.Context, currency, network string, amount decimal.Decimal) (auditStatus uint, processingStatus uint, error error)

	// GetAllFeeSettings retrieves all fee settings for a currency and network, ordered by ID
	GetAllFeeSettings(ctx context.Context, currency, network string) ([]*WithdrawalFeeDetail, error)

	// FormatFeeRangeDisplay formats tiered fee settings into a numbered list for display
	FormatFeeRangeDisplay(ctx context.Context, currency, network string) (string, error)
}

// WithdrawalAmountLimits represents the min/max withdrawal amounts
type WithdrawalAmountLimits struct {
	MinAmount decimal.Decimal
	MaxAmount decimal.Decimal
	Currency  string
	Network   string
}

// WithdrawalFeeSettings represents the fee configuration
type WithdrawalFeeSettings struct {
	FeeType  string          // "fixed" or "percent"
	FeeValue decimal.Decimal
	Currency string
	Network  string
}

// WithdrawalApprovalSettings represents the approval settings for withdrawals
type WithdrawalApprovalSettings struct {
	AutoReleaseMin    decimal.Decimal // Min amount for auto release without approval
	AutoReleaseMax    decimal.Decimal // Max amount for auto release without approval
	ApprovalAutoMin   decimal.Decimal // Min amount for auto release after approval
	ApprovalAutoMax   decimal.Decimal // Max amount for auto release after approval
	ApprovalManualMin decimal.Decimal // Min amount for manual release after approval
	ApprovalManualMax decimal.Decimal // Max amount for manual release after approval (0 means no limit)
}

// WithdrawalFeeDetail represents a single fee tier with amount range
type WithdrawalFeeDetail struct {
	ID        uint
	FeeType   string          // "fixed" or "percent"
	FeeValue  decimal.Decimal
	AmountMin decimal.Decimal
	AmountMax decimal.Decimal
	Currency  string
	Network   string
}

type sWithdrawalConfig struct{}

// NewWithdrawalConfig creates and returns a new instance of the withdrawal config service.
func NewWithdrawalConfig() *sWithdrawalConfig {
	return &sWithdrawalConfig{}
}

func init() {
	// RegisterWithdrawalConfig(NewWithdrawalConfig()) // Will be registered by logic layer
}

var localWithdrawalConfig IWithdrawalConfig

// RegisterWithdrawalConfig registers the withdrawal config service implementation.
func RegisterWithdrawalConfig(i IWithdrawalConfig) {
	localWithdrawalConfig = i
}

// WithdrawalConfig returns the registered withdrawal config service instance.
func WithdrawalConfig() IWithdrawalConfig {
	if localWithdrawalConfig == nil {
		panic("implement not found for interface IWithdrawalConfig, forgot register?")
	}
	return localWithdrawalConfig
}