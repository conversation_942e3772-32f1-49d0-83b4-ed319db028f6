package middleware

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
)

// AuthMiddleware 商户API认证中间件
func AuthMiddleware(r *ghttp.Request) {
	// 健康检查接口跳过认证
	if r.URL.Path == "/health" {
		r.Middleware.Next()
		return
	}

	// 获取客户端IP接口跳过认证
	if r.URL.Path == "/api/v1/client-ip" {
		r.Middleware.Next()
		return
	}

	ctx := r.Context()

	// 获取认证头信息
	apiKey := r.Header.Get("X-API-Key")
	timestamp := r.Header.Get("X-Timestamp")
	nonce := r.Header.Get("X-Nonce")
	signature := r.Header.Get("X-Signature")

	// 验证必需的认证头
	if apiKey == "" || timestamp == "" || nonce == "" || signature == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeUnauthorized,
			"message": "Missing authentication headers",
		})
		return
	}

	// 验证时间戳
	if !validateTimestamp(timestamp) {
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeUnauthorized,
			"message": "Invalid or expired timestamp",
		})
		return
	}

	// 验证 API Key 并获取商户信息
	apiKeyInfo, err := validateAPIKey(ctx, apiKey)
	if err != nil {
		g.Log().Error(ctx, "API Key validation failed:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeUnauthorized,
			"message": "Invalid API key",
		})
		return
	}

	// 验证商户状态
	merchant, err := validateMerchant(ctx, apiKeyInfo.MerchantId)
	if err != nil {
		g.Log().Error(ctx, "Merchant validation failed:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeForbidden,
			"message": "Merchant account is not available",
		})
		return
	}

	// 验证IP白名单
	clientIP := GetClientIP(r)
	if !validateIPWhitelist(ctx, apiKeyInfo, clientIP) {
		g.Log().Warning(ctx, "IP not in whitelist:", clientIP, "for API key:", apiKey)
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeForbidden,
			"message": "IP address not allowed",
		})
		return
	}

	// 验证签名
	if !validateSignature(r, apiKeyInfo.SecretHash, timestamp, nonce, signature) {
		g.Log().Warning(ctx, "Invalid signature for API key:", apiKey)
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeUnauthorized,
			"message": "Invalid signature",
		})
		return
	}

	// 验证频率限制
	if !validateRateLimit(ctx, apiKeyInfo, clientIP) {
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeForbidden,
			"message": "Rate limit exceeded",
		})
		return
	}

	// 验证权限范围
	if !validateScopes(r, apiKeyInfo.Scopes) {
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeForbidden,
			"message": "Insufficient permissions",
		})
		return
	}

	// 更新最后使用时间
	updateLastUsedTime(ctx, apiKeyInfo.ApiKeyId)

	// 设置上下文信息
	r.SetCtxVar("merchant_id", merchant.MerchantId)
	r.SetCtxVar("api_key_id", apiKeyInfo.ApiKeyId)
	r.SetCtxVar("merchant_info", merchant)
	r.SetCtxVar("api_key_info", apiKeyInfo)

	// 继续处理请求
	r.Middleware.Next()
}

// validateTimestamp 验证时间戳（防重放攻击）
func validateTimestamp(timestampStr string) bool {
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return false
	}

	now := time.Now().Unix()
	// 允许5分钟的时间偏差
	if abs(now-timestamp) > 300 {
		return false
	}

	return true
}

// validateAPIKey 验证API Key并返回密钥信息
func validateAPIKey(ctx context.Context, apiKey string) (*entity.MerchantApiKeys, error) {
	var apiKeyInfo *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKey, apiKey).
		Where(dao.MerchantApiKeys.Columns().Status, "active").
		Where(dao.MerchantApiKeys.Columns().DeletedAt + " IS NULL").
		Scan(&apiKeyInfo)

	if err != nil {
		return nil, err
	}

	if apiKeyInfo == nil {
		return nil, fmt.Errorf("API key not found or inactive")
	}

	// 检查过期时间
	if apiKeyInfo.ExpiresAt != nil && apiKeyInfo.ExpiresAt.Before(gtime.Now()) {
		return nil, fmt.Errorf("API key expired")
	}

	return apiKeyInfo, nil
}

// validateMerchant 验证商户状态
func validateMerchant(ctx context.Context, merchantId uint) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().MerchantId, merchantId).
		Where(dao.Merchants.Columns().Status, 1).        // 1: 启用
		Where(dao.Merchants.Columns().IsStop, 0).        // 0: 活跃
		Where(dao.Merchants.Columns().ApiPermission, 1). // 1: 有API权限
		Where(dao.Merchants.Columns().DeletedAt + " IS NULL").
		Scan(&merchant)

	if err != nil {
		return nil, err
	}

	if merchant == nil {
		return nil, fmt.Errorf("merchant not found or not active")
	}

	return merchant, nil
}

// validateIPWhitelist 验证IP白名单
func validateIPWhitelist(ctx context.Context, apiKeyInfo *entity.MerchantApiKeys, clientIP string) bool {
	// 如果没有设置IP白名单，则允许所有IP
	if apiKeyInfo.IpWhitelist == "" {
		return true
	}

	// 解析IP白名单
	allowedIPs := strings.Split(apiKeyInfo.IpWhitelist, ",")
	for _, allowedIP := range allowedIPs {
		allowedIP = strings.TrimSpace(allowedIP)
		if allowedIP == "" {
			continue
		}

		// 检查是否为CIDR格式
		if strings.Contains(allowedIP, "/") {
			if isIPInCIDR(clientIP, allowedIP) {
				return true
			}
		} else {
			// 直接IP匹配
			if clientIP == allowedIP {
				return true
			}
		}
	}

	return false
}

// validateSignature 验证HMAC签名
func validateSignature(r *ghttp.Request, secretHash, timestamp, nonce, signature string) bool {
	// 构建签名字符串
	method := r.Method
	uri := r.RequestURI
	body := string(r.GetBody())

	// 签名格式: METHOD + URI + TIMESTAMP + NONCE + BODY
	signString := method + uri + timestamp + nonce + body

	// 计算HMAC-SHA256签名
	h := hmac.New(sha256.New, []byte(secretHash))
	h.Write([]byte(signString))
	expectedSignature := hex.EncodeToString(h.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// validateRateLimit 验证频率限制
func validateRateLimit(ctx context.Context, apiKeyInfo *entity.MerchantApiKeys, clientIP string) bool {
	// 如果没有设置限制，直接通过
	if apiKeyInfo.RateLimit <= 0 {
		return true
	}

	// 使用Redis实现滑动窗口频率限制
	redis := g.Redis()
	if redis == nil {
		g.Log().Warning(ctx, "Redis not available, rate limit disabled")
		return true
	}

	// 当前时间戳（秒）
	now := time.Now().Unix()

	// 使用Redis实现简单的频率限制（使用计数器方式）
	// 构建计数器key，按分钟为窗口
	countKey := fmt.Sprintf("rate_count:%d:%s:%d", apiKeyInfo.ApiKeyId, clientIP, now/60)
	
	// 获取当前计数
	currentCount, err := redis.Incr(ctx, countKey)
	if err != nil {
		g.Log().Error(ctx, "Rate limit Incr failed:", err)
		return true // Redis错误时允许通过，避免影响业务
	}
	
	// 首次创建时设置过期时间
	if currentCount == 1 {
		redis.Expire(ctx, countKey, 60) // 60秒过期
	}
	
	// 判断是否超过限制
	isAllowed := currentCount <= int64(apiKeyInfo.RateLimit)
	
	if !isAllowed {
		g.Log().Warning(ctx, fmt.Sprintf("Rate limit exceeded for API key %d from IP %s: %d/%d requests per minute", 
			apiKeyInfo.ApiKeyId, clientIP, currentCount, apiKeyInfo.RateLimit))
	}
	
	return isAllowed
}

// validateScopes 验证权限范围
func validateScopes(r *ghttp.Request, scopes string) bool {
	if scopes == "" {
		return true // 没有设置权限限制
	}

	// 根据请求路径判断所需权限
	path := r.URL.Path
	var requiredScope string

	if strings.Contains(path, "/deposit/") {
		requiredScope = "deposit"
	} else if strings.Contains(path, "/withdraw/") {
		requiredScope = "withdraw"
	} else if strings.Contains(path, "/transaction/") {
		requiredScope = "transaction"
	} else {
		return true // 未知路径，暂时允许
	}

	// 检查权限范围
	allowedScopes := strings.Split(scopes, ",")
	for _, scope := range allowedScopes {
		scope = strings.TrimSpace(scope)
		if scope == requiredScope || scope == "all" {
			return true
		}
	}

	return false
}

// 辅助函数
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// GetClientIP 获取客户端真实IP（加强安全验证）
func GetClientIP(r *ghttp.Request) string {
	// 获取连接的直接IP
	remoteIP, _, _ := net.SplitHostPort(r.RemoteAddr)
	
	// 检查是否来自可信代理
	if !isTrustedProxy(remoteIP) {
		// 不是可信代理，直接返回连接IP
		return remoteIP
	}
	
	// 从可信代理获取真实IP，按优先级检查
	// 1. 检查 X-Forwarded-For 头
	if ip := r.Header.Get("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个非私有IP
		ips := strings.Split(ip, ",")
		for _, ipStr := range ips {
			cleanIP := strings.TrimSpace(ipStr)
			if isValidPublicIP(cleanIP) {
				return cleanIP
			}
		}
	}

	// 2. 检查 X-Real-IP 头
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		cleanIP := strings.TrimSpace(ip)
		if isValidPublicIP(cleanIP) {
			return cleanIP
		}
	}

	// 3. 检查 CF-Connecting-IP 头（Cloudflare）
	if ip := r.Header.Get("CF-Connecting-IP"); ip != "" {
		cleanIP := strings.TrimSpace(ip)
		if isValidPublicIP(cleanIP) {
			return cleanIP
		}
	}
	

	// 如果都没有有效的公共IP，返回代理IP
	return remoteIP
}

// isTrustedProxy 检查IP是否是可信代理
func isTrustedProxy(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}
	
	// 可信代理网段列表
	trustedCIDRs := []string{
		"10.0.0.0/8",      // 私有网络
		"**********/12",   // 私有网络
		"***********/16",  // 私有网络
		"*********/8",     // 本地回环
		"***********/16",  // 链路本地
	}
	
	for _, cidrStr := range trustedCIDRs {
		_, cidr, err := net.ParseCIDR(cidrStr)
		if err != nil {
			continue
		}
		if cidr.Contains(ip) {
			return true
		}
	}
	
	return false
}

// isValidPublicIP 检查是否是有效的公共IP
func isValidPublicIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}
	
	// 排除私有IP和特殊IP
	if ip.IsLoopback() || ip.IsLinkLocalMulticast() || ip.IsLinkLocalUnicast() {
		return false
	}
	
	// 排除私有网络
	privateCIDRs := []string{
		"10.0.0.0/8",
		"**********/12", 
		"***********/16",
		"***********/16", // 链路本地
		"*********/4",    // 组播
		"240.0.0.0/4",    // 保留
	}
	
	for _, cidrStr := range privateCIDRs {
		_, cidr, err := net.ParseCIDR(cidrStr)
		if err != nil {
			continue
		}
		if cidr.Contains(ip) {
			return false
		}
	}
	
	return true
}

func isIPInCIDR(ip, cidr string) bool {
	_, network, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}

	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return false
	}

	return network.Contains(ipAddr)
}

func updateLastUsedTime(ctx context.Context, apiKeyId uint) {
	// 异步更新最后使用时间，避免影响请求性能
	go func() {
		// 使用独立的context避免请求context被取消
		bgCtx := context.Background()
		_, err := dao.MerchantApiKeys.Ctx(bgCtx).
			Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
			Update(g.Map{
				dao.MerchantApiKeys.Columns().LastUsedAt: gtime.Now(),
			})
		if err != nil {
			g.Log().Error(bgCtx, "Failed to update last used time:", err)
		}
	}()
}
