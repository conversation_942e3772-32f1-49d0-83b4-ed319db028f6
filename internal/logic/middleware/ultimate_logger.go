package middleware

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gmlock"
)

// LogEntry 日志条目结构
type LogEntry struct {
	Timestamp     string                 `json:"timestamp"`
	RequestID     string                 `json:"request_id"`
	Method        string                 `json:"method"`
	URL           string                 `json:"url"`
	ClientIP      string                 `json:"client_ip"`
	UserAgent     string                 `json:"user_agent"`
	RequestHeader map[string]string      `json:"request_header"`
	RequestBody   string                 `json:"request_body"`
	ResponseCode  int                    `json:"response_code"`
	ResponseHeader map[string]string     `json:"response_header"`
	ResponseBody  string                 `json:"response_body"`
	Duration      int64                  `json:"duration_ms"`
	MerchantID    int64                  `json:"merchant_id,omitempty"`
	Error         string                 `json:"error,omitempty"`
}

// sanitizePath 清理路径，使其适合作为文件名
func sanitizePath(path string) string {
	// 移除开头的斜杠
	path = strings.TrimPrefix(path, "/")
	
	// 如果为空，使用 "index"
	if path == "" {
		return "index"
	}
	
	// 替换路径分隔符为下划线
	path = strings.ReplaceAll(path, "/", "_")
	
	// 替换其他特殊字符
	replacer := strings.NewReplacer(
		"?", "_",
		"&", "_",
		"=", "_",
		"#", "_",
		"%", "_",
		":", "_",
		"*", "_",
		"<", "_",
		">", "_",
		"|", "_",
		"\"", "_",
		"\\", "_",
		" ", "_",
	)
	path = replacer.Replace(path)
	
	// 移除连续的下划线
	for strings.Contains(path, "__") {
		path = strings.ReplaceAll(path, "__", "_")
	}
	
	// 移除末尾的下划线
	path = strings.TrimSuffix(path, "_")
	
	return path
}

// UltimateHandlerResponse 自定义的响应处理中间件（替代 ghttp.MiddlewareHandlerResponse）
func UltimateHandlerResponse(r *ghttp.Request) {
	// 记录请求开始
	startTime := time.Now()
	requestID := gmd5.MustEncryptString(fmt.Sprintf("%s%d", r.GetSessionId(), time.Now().UnixNano()))
	
	// 记录请求信息
	requestHeaders := make(map[string]string)
	for k, v := range r.Header {
		requestHeaders[k] = strings.Join(v, ",")
	}
	
	requestBody := ""
	if r.Method != "GET" && r.GetBody() != nil && len(r.GetBody()) > 0 {
		requestBody = string(r.GetBody())
	}
	
	// 保存到上下文
	r.SetCtxVar("log_request_id", requestID)
	r.SetCtxVar("log_start_time", startTime)
	r.SetCtxVar("log_request_headers", requestHeaders)
	r.SetCtxVar("log_request_body", requestBody)
	
	// 开启响应缓冲
	r.Response.Buffer()
	
	// 执行后续中间件和控制器
	r.Middleware.Next()
	
	// 检查响应是否已经被写入（例如由认证中间件的WriteJsonExit）
	if r.Response.BufferLength() > 0 {
		// 响应已经被写入，直接记录日志
		responseBody := r.Response.BufferString()
		go logUltimateResponse(r, responseBody)
		return
	}
	
	// 获取处理结果
	var (
		msg  string
		err  = r.GetError()
		res  = r.GetHandlerResponse()
		code = 0
	)
	
	// 构建响应数据
	responseData := make(map[string]interface{})
	
	if err != nil {
		code = 500
		msg = err.Error()
		responseData["code"] = code
		responseData["message"] = msg
		responseData["data"] = nil
	} else {
		// 检查响应是否已经是结构化的
		if resMap, ok := res.(map[string]interface{}); ok {
			// 如果已经是map，直接使用
			responseData = resMap
		} else if res != nil {
			// 如果不是map，包装成标准格式
			code = 0
			msg = "success"
			responseData["code"] = code
			responseData["message"] = msg
			responseData["data"] = res
		} else {
			// 没有响应数据
			code = 0
			msg = "success"
			responseData["code"] = code
			responseData["message"] = msg
			responseData["data"] = nil
		}
	}
	
	// 序列化响应
	jsonBytes, _ := json.Marshal(responseData)
	responseBody := string(jsonBytes)
	
	// 写入响应
	r.Response.WriteJson(responseData)
	
	// 记录日志
	go logUltimateResponse(r, responseBody)
}

// logUltimateResponse 记录最终响应
func logUltimateResponse(r *ghttp.Request, responseBody string) {
	// 获取之前保存的信息
	requestID := ""
	if v := r.GetCtxVar("log_request_id"); v != nil {
		requestID = v.String()
	}
	
	startTime := time.Now()
	if v := r.GetCtxVar("log_start_time"); v != nil {
		startTime = v.Time()
	}
	
	requestHeaders := make(map[string]string)
	if v := r.GetCtxVar("log_request_headers"); v != nil {
		if headers, ok := v.Val().(map[string]string); ok {
			requestHeaders = headers
		}
	}
	
	requestBody := ""
	if v := r.GetCtxVar("log_request_body"); v != nil {
		requestBody = v.String()
	}
	
	// 计算耗时
	duration := time.Since(startTime).Milliseconds()
	
	// 获取商户ID
	var merchantID int64
	if merchantIDVar := r.GetCtxVar("merchant_id"); merchantIDVar != nil {
		merchantID = merchantIDVar.Int64()
	}
	
	// 获取响应头
	responseHeaders := make(map[string]string)
	for k, v := range r.Response.Header() {
		responseHeaders[k] = strings.Join(v, ",")
	}
	
	// 创建日志条目
	logEntry := LogEntry{
		Timestamp:     time.Now().Format("2006-01-02 15:04:05.000"),
		RequestID:     requestID,
		Method:        r.Method,
		URL:           r.URL.String(),
		ClientIP:      r.GetClientIp(),
		UserAgent:     r.UserAgent(),
		RequestHeader: requestHeaders,
		RequestBody:   requestBody,
		ResponseCode:  r.Response.Status,
		ResponseHeader: responseHeaders,
		ResponseBody:  responseBody,
		Duration:      duration,
		MerchantID:    merchantID,
	}
	
	// 写入日志文件
	writeUltimateLog(r, logEntry)
}

// writeUltimateLog 写入最终日志
func writeUltimateLog(r *ghttp.Request, entry LogEntry) {
	// 生成日志文件路径
	date := time.Now().Format("2006-01-02")
	path := sanitizePath(r.URL.Path)
	logDir := filepath.Join("logs", date)
	logFile := filepath.Join(logDir, fmt.Sprintf("%s.log", path))
	
	// 确保目录存在
	if err := gfile.Mkdir(logDir); err != nil {
		g.Log().Error(r.Context(), "创建日志目录失败", err)
		return
	}
	
	// 获取文件锁
	lockKey := logFile
	gmlock.Lock(lockKey)
	defer gmlock.Unlock(lockKey)
	
	// 序列化日志条目
	jsonData, err := json.Marshal(entry)
	if err != nil {
		g.Log().Error(r.Context(), "序列化日志失败", err)
		return
	}
	
	// 追加写入文件
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		g.Log().Error(r.Context(), "打开日志文件失败", err)
		return
	}
	defer file.Close()
	
	// 写入日志行
	if _, err := file.WriteString(string(jsonData) + "\n"); err != nil {
		g.Log().Error(r.Context(), "写入日志失败", err)
		return
	}
}