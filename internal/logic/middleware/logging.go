package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"

	"merchant-server/internal/dao"
	"merchant-server/internal/model/do"
)

// LoggingMiddleware API调用日志中间件
func LoggingMiddleware(r *ghttp.Request) {
	startTime := time.Now()

	// 记录请求信息
	requestData := map[string]interface{}{
		"method":     r.Method,
		"url":        r.URL.String(),
		"headers":    r.<PERSON><PERSON>,
		"user_agent": r.UserAgent(),
		"ip":         r.GetClientIp(),
		"body":       string(r.GetBody()),
	}

	// 获取商户ID（如果已认证）
	var merchantId int64
	if merchantIdVar := r.GetCtxVar("merchant_id"); merchantIdVar != nil {
		merchantId = gconv.Int64(merchantIdVar)
	}

	// 继续处理请求
	r.Middleware.Next()

	// 计算处理时间
	duration := time.Since(startTime)

	// 记录响应信息
	responseData := map[string]interface{}{
		"status_code": r.Response.Status,
		"duration_ms": duration.Milliseconds(),
	}

	// 异步记录日志
	go func() {
		ctx := context.Background()
		logAPICall(ctx, merchantId, requestData, responseData, duration)
	}()
}

// logAPICall 记录API调用日志
func logAPICall(ctx context.Context, merchantId int64, requestData, responseData map[string]interface{}, duration time.Duration) {
	requestJSON, _ := json.Marshal(requestData)
	responseJSON, _ := json.Marshal(responseData)

	// 记录到操作日志表
	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		MemberId:      merchantId,
		MemberType:    "merchant",
		Module:        "api",
		Action:        "call",
		RequestMethod: gconv.String(requestData["method"]),
		RequestUrl:    gconv.String(requestData["url"]),
		RequestParams: gjson.New(requestJSON),
		Response:      gjson.New(responseJSON),
		Duration:      duration.Milliseconds(),
		OperationIp:   gconv.String(requestData["ip"]),
		UserAgent:     gconv.String(requestData["user_agent"]),
		Status:        1, // 成功
		CreatedAt:     gtime.Now(),
	})

	// 记录到系统日志
	g.Log().Info(ctx, "API调用", g.Map{
		"merchant_id": merchantId,
		"method":      requestData["method"],
		"url":         requestData["url"],
		"status":      responseData["status_code"],
		"duration":    duration.Milliseconds(),
		"ip":          requestData["ip"],
	})

	// 如果响应时间过长，记录性能警告
	if duration > 5*time.Second {
		g.Log().Warning(ctx, "API响应时间过长", g.Map{
			"merchant_id": merchantId,
			"url":         requestData["url"],
			"duration":    duration.Milliseconds(),
		})
	}

	// 如果是错误响应，记录错误日志
	if statusCode := gconv.Int(responseData["status_code"]); statusCode >= 400 {
		g.Log().Error(ctx, "API调用错误", g.Map{
			"merchant_id": merchantId,
			"url":         requestData["url"],
			"status":      statusCode,
			"request":     string(requestJSON),
		})
	}
}

// BusinessLogger 业务操作日志记录器
type BusinessLogger struct{}

func NewBusinessLogger() *BusinessLogger {
	return &BusinessLogger{}
}

// LogDepositOperation 记录充值操作日志
func (l *BusinessLogger) LogDepositOperation(ctx context.Context, merchantId int64, operation string, data map[string]interface{}) {
	l.logOperation(ctx, merchantId, "deposit", operation, data)
}

// LogWithdrawOperation 记录提现操作日志
func (l *BusinessLogger) LogWithdrawOperation(ctx context.Context, merchantId int64, operation string, data map[string]interface{}) {
	l.logOperation(ctx, merchantId, "withdraw", operation, data)
}

// LogAddressOperation 记录地址操作日志
func (l *BusinessLogger) LogAddressOperation(ctx context.Context, merchantId int64, operation string, data map[string]interface{}) {
	l.logOperation(ctx, merchantId, "address", operation, data)
}

// LogCallbackOperation 记录回调操作日志
func (l *BusinessLogger) LogCallbackOperation(ctx context.Context, merchantId int64, operation string, data map[string]interface{}) {
	l.logOperation(ctx, merchantId, "callback", operation, data)
}

// logOperation 通用操作日志记录
func (l *BusinessLogger) logOperation(ctx context.Context, merchantId int64, operationType, operation string, data map[string]interface{}) {
	dataJSON, _ := json.Marshal(data)

	// 记录到操作日志表
	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		MemberId:      merchantId,
		MemberType:    "merchant",
		Module:        operationType,
		Action:        operation,
		RequestParams: gjson.New(dataJSON),
		Status:        1, // 成功
		CreatedAt:     gtime.Now(),
	})

	// 记录到系统日志
	g.Log().Info(ctx, "业务操作", g.Map{
		"merchant_id": merchantId,
		"type":        operationType,
		"operation":   operation,
		"data":        data,
	})
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct{}

func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{}
}

// MonitorFunction 监控函数执行性能
func (m *PerformanceMonitor) MonitorFunction(ctx context.Context, functionName string, fn func() error) error {
	startTime := time.Now()

	err := fn()

	duration := time.Since(startTime)

	// 记录性能日志
	g.Log().Info(ctx, "函数执行性能", g.Map{
		"function": functionName,
		"duration": duration.Milliseconds(),
		"success":  err == nil,
	})

	// 如果执行时间过长，记录警告
	if duration > 3*time.Second {
		g.Log().Warning(ctx, "函数执行时间过长", g.Map{
			"function": functionName,
			"duration": duration.Milliseconds(),
		})
	}

	// 如果执行失败，记录错误
	if err != nil {
		g.Log().Error(ctx, "函数执行失败", g.Map{
			"function": functionName,
			"error":    err.Error(),
			"duration": duration.Milliseconds(),
		})
	}

	return err
}

// ErrorLogger 错误日志记录器
type ErrorLogger struct{}

func NewErrorLogger() *ErrorLogger {
	return &ErrorLogger{}
}

// LogError 记录错误日志
func (e *ErrorLogger) LogError(ctx context.Context, merchantId int64, errorType, errorMsg string, data map[string]interface{}) {
	dataJSON, _ := json.Marshal(data)

	// 记录到操作日志表
	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		MemberId:      merchantId,
		MemberType:    "merchant",
		Module:        "error",
		Action:        errorType,
		RequestParams: gjson.New(dataJSON),
		ErrMsg:        errorMsg,
		Status:        0, // 失败
		CreatedAt:     gtime.Now(),
	})

	// 记录到系统错误日志
	g.Log().Error(ctx, "业务错误", g.Map{
		"merchant_id": merchantId,
		"type":        errorType,
		"message":     errorMsg,
		"data":        data,
	})
}

// LogPanic 记录panic日志
func (e *ErrorLogger) LogPanic(ctx context.Context, panicInfo interface{}, stack string) {
	g.Log().Critical(ctx, "系统Panic", g.Map{
		"panic": panicInfo,
		"stack": stack,
	})

	// 记录到操作日志表
	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		Module:        "system",
		Action:        "panic",
		RequestParams: gjson.New(stack),
		ErrMsg:        fmt.Sprintf("系统Panic: %v", panicInfo),
		Status:        0, // 失败
		CreatedAt:     gtime.Now(),
	})
}

// SecurityLogger 安全日志记录器
type SecurityLogger struct{}

func NewSecurityLogger() *SecurityLogger {
	return &SecurityLogger{}
}

// LogAuthFailure 记录认证失败日志
func (s *SecurityLogger) LogAuthFailure(ctx context.Context, reason, ip, userAgent string, data map[string]interface{}) {
	dataJSON, _ := json.Marshal(data)

	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		Module:        "auth",
		Action:        "failure",
		RequestParams: gjson.New(dataJSON),
		ErrMsg:        fmt.Sprintf("认证失败: %s", reason),
		OperationIp:   ip,
		UserAgent:     userAgent,
		Status:        0, // 失败
		CreatedAt:     gtime.Now(),
	})

	g.Log().Warning(ctx, "认证失败", g.Map{
		"reason":     reason,
		"ip":         ip,
		"user_agent": userAgent,
		"data":       data,
	})
}

// LogSuspiciousActivity 记录可疑活动日志
func (s *SecurityLogger) LogSuspiciousActivity(ctx context.Context, merchantId int64, activity, ip string, data map[string]interface{}) {
	dataJSON, _ := json.Marshal(data)

	dao.OperationLog.Ctx(ctx).Insert(do.OperationLog{
		MemberId:      merchantId,
		MemberType:    "merchant",
		Module:        "security",
		Action:        "suspicious",
		RequestParams: gjson.New(dataJSON),
		ErrMsg:        fmt.Sprintf("可疑活动: %s", activity),
		OperationIp:   ip,
		Status:        0, // 失败
		CreatedAt:     gtime.Now(),
	})

	g.Log().Warning(ctx, "可疑活动", g.Map{
		"merchant_id": merchantId,
		"activity":    activity,
		"ip":          ip,
		"data":        data,
	})
}

// RecoveryMiddleware 恢复中间件，处理panic
func RecoveryMiddleware(r *ghttp.Request) {
	defer func() {
		if err := recover(); err != nil {
			// 记录panic日志
			errorLogger := NewErrorLogger()
			errorLogger.LogPanic(r.Context(), err, string(r.GetBody()))

			// 返回500错误
			r.Response.WriteStatus(http.StatusInternalServerError)
			r.Response.WriteJson(g.Map{
				"code":    500,
				"message": "内部服务器错误",
			})
		}
	}()

	r.Middleware.Next()
}
