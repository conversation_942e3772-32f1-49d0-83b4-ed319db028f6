package middleware

import (
	"net/http"
	"runtime/debug"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	v1 "merchant-server/api/merchant/v1"
)

// ErrorHandlerMiddleware 统一错误处理中间件
func ErrorHandlerMiddleware(r *ghttp.Request) {
	r.Middleware.Next()
	
	// 处理panic
	if err := recover(); err != nil {
		g.Log().Error(r.Context(), "Panic recovered:", err)
		g.Log().Error(r.Context(), "Stack trace:", string(debug.Stack()))
		
		// 记录详细错误信息到日志，但不返回给客户端
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeInternalError,
			"message": "Internal server error",
			"data":    nil,
		})
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware(r *ghttp.Request) {
	// 添加安全头
	r.Response.Header().Set("X-Content-Type-Options", "nosniff")
	r.Response.Header().Set("X-Frame-Options", "DENY")
	r.Response.Header().Set("X-XSS-Protection", "1; mode=block")
	r.Response.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
	r.Response.Header().Set("Content-Security-Policy", "default-src 'self'")
	
	// 移除敏感信息
	r.Response.Header().Del("Server")
	r.Response.Header().Set("Server", "merchant-server")
	
	r.Middleware.Next()
}

// CORSMiddleware CORS中间件
func CORSMiddleware(r *ghttp.Request) {
	origin := r.Header.Get("Origin")
	
	// 检查允许的域名（从配置读取，这里先硬编码）
	allowedOrigins := []string{
		"http://localhost:3000",
		"https://localhost:3000",
		// 从配置文件读取更多允许的域名
	}
	
	isAllowed := false
	for _, allowedOrigin := range allowedOrigins {
		if origin == allowedOrigin {
			isAllowed = true
			break
		}
	}
	
	if isAllowed {
		r.Response.Header().Set("Access-Control-Allow-Origin", origin)
	}
	
	r.Response.Header().Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
	r.Response.Header().Set("Access-Control-Allow-Headers", "Content-Type,Authorization,X-API-Key,X-Timestamp,X-Nonce,X-Signature")
	r.Response.Header().Set("Access-Control-Allow-Credentials", "false")
	r.Response.Header().Set("Access-Control-Max-Age", "86400")
	
	// 处理预检请求
	if r.Method == "OPTIONS" {
		r.Response.WriteHeader(http.StatusOK)
		r.Exit()
		return
	}
	
	r.Middleware.Next()
}

// SanitizeErrorMiddleware 错误信息清洗中间件
func SanitizeErrorMiddleware(r *ghttp.Request) {
	r.Middleware.Next()
	
	// 检查响应内容，清洗敏感信息
	if r.Response.BufferLength() > 0 {
		content := r.Response.BufferString()
		
		// 移除可能的敏感信息
		sensitivePatterns := []string{
			"password", "secret", "token", "key",
			"mysql", "redis", "database", 
			"/internal/", "/home/", "/root/",
			"stack trace", "panic:",
		}
		
		for _, pattern := range sensitivePatterns {
			if strings.Contains(strings.ToLower(content), pattern) {
				// 记录日志但不修改响应（在生产环境中可能需要更严格的处理）
				g.Log().Warning(r.Context(), "Potential sensitive information in response")
				break
			}
		}
	}
}

// RequestSizeLimitMiddleware 请求大小限制中间件
func RequestSizeLimitMiddleware(r *ghttp.Request) {
	// 限制请求体大小（1MB）
	maxSize := int64(1024 * 1024)
	
	if r.ContentLength > maxSize {
		r.Response.WriteJsonExit(g.Map{
			"code":    v1.CodeInvalidParams,
			"message": "Request too large",
			"data":    nil,
		})
		return
	}
	
	r.Middleware.Next()
}

// ContentTypeValidationMiddleware 内容类型验证中间件
func ContentTypeValidationMiddleware(r *ghttp.Request) {
	// 只对POST/PUT请求验证Content-Type
	if r.Method == "POST" || r.Method == "PUT" {
		contentType := r.Header.Get("Content-Type")
		
		// 允许的内容类型
		allowedTypes := []string{
			"application/json",
			"application/x-www-form-urlencoded",
			"multipart/form-data",
		}
		
		isValid := false
		for _, allowedType := range allowedTypes {
			if strings.HasPrefix(contentType, allowedType) {
				isValid = true
				break
			}
		}
		
		if !isValid && r.ContentLength > 0 {
			r.Response.WriteJsonExit(g.Map{
				"code":    v1.CodeInvalidParams,
				"message": "Invalid content type",
				"data":    nil,
			})
			return
		}
	}
	
	r.Middleware.Next()
}

// TimeoutMiddleware 请求超时中间件
func TimeoutMiddleware(r *ghttp.Request) {
	// GoFrame 本身支持超时，这里主要是记录慢请求
	startTime := r.EnterTime
	
	r.Middleware.Next()
	
	duration := r.LeaveTime.Sub(startTime)
	
	// 记录慢请求（超过5秒）
	if duration.Seconds() > 5 {
		g.Log().Warning(r.Context(), "Slow request detected:", 
			"method", r.Method,
			"path", r.URL.Path,
			"duration", duration.String(),
			"ip", getClientIPFromRequest(r))
	}
}

// getClientIPFromRequest 从请求中获取客户端IP（简化版）
func getClientIPFromRequest(r *ghttp.Request) string {
	if ip := r.Header.Get("X-Forwarded-For"); ip != "" {
		return strings.Split(ip, ",")[0]
	}
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		return ip
	}
	return r.GetClientIp()
}