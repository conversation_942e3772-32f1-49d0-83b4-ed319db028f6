package validation

import (
	"net"
	"regexp"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/shopspring/decimal"
)

// BlockchainAddressValidator 区块链地址验证器
type BlockchainAddressValidator struct{}

// ValidateAddress 验证区块链地址格式
func (v *BlockchainAddressValidator) ValidateAddress(chain, address string) error {
	if address == "" {
		return gerror.New("地址不能为空")
	}

	switch strings.ToLower(chain) {
	case "trx", "tron":
		return v.validateTronAddress(address)
	case "eth", "ethereum":
		return v.validateEthereumAddress(address)
	case "bsc", "bnb":
		return v.validateBSCAddress(address)
	case "polygon", "matic":
		return v.validatePolygonAddress(address)
	default:
		return gerror.Newf("不支持的区块链类型: %s", chain)
	}
}

// validateTronAddress 验证Tron地址
func (v *BlockchainAddressValidator) validateTronAddress(address string) error {
	// Tron地址格式：T开头，34位字符
	if len(address) != 34 {
		return gerror.New("Tron地址长度必须为34位")
	}

	if !strings.HasPrefix(address, "T") {
		return gerror.New("Tron地址必须以T开头")
	}

	// 检查是否为有效的Base58字符
	base58Pattern := regexp.MustCompile(`^[**********************************************************]+$`)
	if !base58Pattern.MatchString(address) {
		return gerror.New("Tron地址包含无效字符")
	}

	return nil
}

// validateEthereumAddress 验证以太坊地址
func (v *BlockchainAddressValidator) validateEthereumAddress(address string) error {
	// 以太坊地址格式：0x开头，42位字符
	if len(address) != 42 {
		return gerror.New("以太坊地址长度必须为42位")
	}

	if !strings.HasPrefix(address, "0x") && !strings.HasPrefix(address, "0X") {
		return gerror.New("以太坊地址必须以0x开头")
	}

	// 检查十六进制字符
	hexPattern := regexp.MustCompile(`^0[xX][0-9a-fA-F]{40}$`)
	if !hexPattern.MatchString(address) {
		return gerror.New("以太坊地址包含无效字符")
	}

	return nil
}

// validateBSCAddress BSC地址验证（与以太坊相同）
func (v *BlockchainAddressValidator) validateBSCAddress(address string) error {
	return v.validateEthereumAddress(address)
}

// validatePolygonAddress Polygon地址验证（与以太坊相同）
func (v *BlockchainAddressValidator) validatePolygonAddress(address string) error {
	return v.validateEthereumAddress(address)
}

// AmountValidator 金额验证器
type AmountValidator struct{}

// ValidateAmount 验证金额格式和范围
func (v *AmountValidator) ValidateAmount(amountStr string, minAmount, maxAmount decimal.Decimal) error {
	if amountStr == "" {
		return gerror.New("金额不能为空")
	}

	// 验证金额格式
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return gerror.New("金额格式无效")
	}

	// 检查是否为正数
	if amount.LessThanOrEqual(decimal.Zero) {
		return gerror.New("金额必须大于0")
	}

	// 检查最小金额
	if !minAmount.IsZero() && amount.LessThan(minAmount) {
		return gerror.Newf("金额不能小于 %s", minAmount.String())
	}

	// 检查最大金额
	if !maxAmount.IsZero() && amount.GreaterThan(maxAmount) {
		return gerror.Newf("金额不能大于 %s", maxAmount.String())
	}

	// 检查小数位数（最多8位）
	if amount.Exponent() < -8 {
		return gerror.New("金额小数位数不能超过8位")
	}

	return nil
}

// ValidateAmountPrecision 验证金额精度
func (v *AmountValidator) ValidateAmountPrecision(amountStr string, maxDecimals int) error {
	if strings.Contains(amountStr, ".") {
		parts := strings.Split(amountStr, ".")
		if len(parts) == 2 && len(parts[1]) > maxDecimals {
			return gerror.Newf("金额小数位数不能超过%d位", maxDecimals)
		}
	}
	return nil
}

// OrderValidator 订单验证器
type OrderValidator struct{}

// ValidateOrderID 验证订单号格式
func (v *OrderValidator) ValidateOrderID(orderID string) error {
	if orderID == "" {
		return gerror.New("订单号不能为空")
	}

	// 长度检查（6-100位）
	if len(orderID) < 6 || len(orderID) > 100 {
		return gerror.New("订单号长度必须在6-100位之间")
	}

	// 字符检查（只允许字母、数字、下划线、中横线）
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	if !validPattern.MatchString(orderID) {
		return gerror.New("订单号只能包含字母、数字、下划线和中横线")
	}

	return nil
}

// SecurityValidator 安全验证器
type SecurityValidator struct{}

// ValidateUserLabel 验证用户标识
func (v *SecurityValidator) ValidateUserLabel(userLabel string) error {
	if userLabel == "" {
		return gerror.New("用户标识不能为空")
	}

	// 长度检查
	if len(userLabel) < 1 || len(userLabel) > 100 {
		return gerror.New("用户标识长度必须在1-100位之间")
	}

	// 防止注入攻击的字符检查
	dangerousPatterns := []string{
		"<script", "</script>", "javascript:", "vbscript:",
		"onload=", "onerror=", "onclick=", "onmouseover=",
		"eval(", "alert(", "confirm(", "prompt(",
		"document.", "window.", "location.",
		"../", "./", "..\\", ".\\",
		"DROP TABLE", "DELETE FROM", "INSERT INTO", "UPDATE SET",
		"UNION SELECT", "OR 1=1", "AND 1=1",
	}

	userLabelLower := strings.ToLower(userLabel)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(userLabelLower, strings.ToLower(pattern)) {
			return gerror.New("用户标识包含不安全的字符")
		}
	}

	return nil
}

// ValidateCallback 验证回调URL
func (v *SecurityValidator) ValidateCallback(callbackURL string) error {
	if callbackURL == "" {
		return nil // 回调URL可以为空
	}

	// 检查URL格式
	if !strings.HasPrefix(callbackURL, "http://") && !strings.HasPrefix(callbackURL, "https://") {
		return gerror.New("回调URL必须以http://或https://开头")
	}

	// 长度检查
	if len(callbackURL) > 500 {
		return gerror.New("回调URL长度不能超过500字符")
	}

	// 防止内网访问
	if v.isInternalURL(callbackURL) {
		return gerror.New("回调URL不能指向内网地址")
	}

	return nil
}

// isInternalURL 检查是否是内网URL
func (v *SecurityValidator) isInternalURL(url string) bool {
	// 简单的内网检查
	internalPatterns := []string{
		"://localhost", "://127.", "://10.", "://172.16.",
		"://172.17.", "://172.18.", "://172.19.", "://172.20.",
		"://172.21.", "://172.22.", "://172.23.", "://172.24.",
		"://172.25.", "://172.26.", "://172.27.", "://172.28.",
		"://172.29.", "://172.30.", "://172.31.", "://192.168.",
		"://169.254.", "://0.0.0.0", "://[::1]", "://[::",
	}

	urlLower := strings.ToLower(url)
	for _, pattern := range internalPatterns {
		if strings.Contains(urlLower, pattern) {
			return true
		}
	}

	return false
}

// IPValidator IP验证器
type IPValidator struct{}

// ValidateIP 验证IP地址格式
func (v *IPValidator) ValidateIP(ipStr string) error {
	if ipStr == "" {
		return gerror.New("IP地址不能为空")
	}

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return gerror.New("IP地址格式无效")
	}

	return nil
}

// ValidateCIDR 验证CIDR格式
func (v *IPValidator) ValidateCIDR(cidrStr string) error {
	if cidrStr == "" {
		return gerror.New("CIDR不能为空")
	}

	_, _, err := net.ParseCIDR(cidrStr)
	if err != nil {
		return gerror.New("CIDR格式无效")
	}

	return nil
}

// TimeRangeValidator 时间范围验证器
type TimeRangeValidator struct{}

// ValidateTimeRange 验证时间范围
func (v *TimeRangeValidator) ValidateTimeRange(startTime, endTime string) error {
	if startTime == "" && endTime == "" {
		return nil // 都为空是允许的
	}

	// 时间格式验证（支持多种格式）
	timeFormats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02",
	}

	var startTimeValid, endTimeValid bool

	if startTime != "" {
		for range timeFormats {
			if _, err := strconv.ParseInt(startTime, 10, 64); err == nil {
				// 时间戳格式
				startTimeValid = true
				break
			}
			// TODO: 这里可以添加更多时间格式验证
		}
		if !startTimeValid {
			return gerror.New("开始时间格式无效")
		}
	}

	if endTime != "" {
		for range timeFormats {
			if _, err := strconv.ParseInt(endTime, 10, 64); err == nil {
				// 时间戳格式
				endTimeValid = true
				break
			}
			// TODO: 这里可以添加更多时间格式验证
		}
		if !endTimeValid {
			return gerror.New("结束时间格式无效")
		}
	}

	return nil
}

// GlobalValidator 全局验证器实例
var (
	AddressValidator   = &BlockchainAddressValidator{}
	AmountValidatorInst    = &AmountValidator{}
	OrderValidatorInst     = &OrderValidator{}
	SecurityValidatorInst  = &SecurityValidator{}
	IPValidatorInst        = &IPValidator{}
	TimeRangeValidatorInst = &TimeRangeValidator{}
)
