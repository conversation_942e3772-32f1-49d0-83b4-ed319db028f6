package merchant

import (
	"context"
	"fmt"
	"strings"

	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
	"merchant-server/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// sWithdrawalConfig is the implementation of the withdrawal config service
type sWithdrawalConfig struct{}

// NewWithdrawalConfig creates and returns a new instance of the withdrawal config service.
func NewWithdrawalConfig() *sWithdrawalConfig {
	return &sWithdrawalConfig{}
}

func init() {
	service.RegisterWithdrawalConfig(NewWithdrawalConfig())
}

// normalizeNetwork 标准化网络名称
func (s *sWithdrawalConfig) normalizeNetwork(network string) string {
	// 统一转换为大写并去除空格
	network = strings.ToUpper(strings.TrimSpace(network))

	// 处理网络名称映射，将前端/API传来的名称映射到数据库中的名称
	switch network {
	case "TRC20":
		return "TRON"
	case "ERC20":
		return "ETH"
	case "": // 空网络通常表示法币或原生币
		// 根据币种判断，CNY使用Fiat，其他使用NATIVE
		return "NATIVE"
	default:
		return network
	}
}

// normalizeCurrency 标准化币种名称
func (s *sWithdrawalConfig) normalizeCurrency(currency string) string {
	return strings.ToUpper(strings.TrimSpace(currency))
}

// GetAmountLimits retrieves withdrawal amount limits from the database
func (s *sWithdrawalConfig) GetAmountLimits(ctx context.Context, currency, network string) (*service.WithdrawalAmountLimits, error) {
	// 标准化参数
	currency = s.normalizeCurrency(currency)
	network = s.normalizeNetwork(network)

	// 特殊处理CNY，使用Fiat网络
	if currency == "CNY" {
		network = "Fiat"
	}

	// 查询该币种/网络的所有启用设置，以确定最小和最大限额
	var settings []entity.WithdrawalFeeSettings
	err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ?", currency).
		Where("network = ?", network).
		Where("status = ?", 1).
		WhereNull("deleted_at").
		OrderAsc("amount_min").
		Scan(&settings)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get withdrawal amount limits for %s/%s: %v", currency, network, err)
		return nil, gerror.Newf("未找到 %s/%s 的提现限额配置", currency, network)
	}

	if len(settings) == 0 {
		g.Log().Errorf(ctx, "No withdrawal fee settings found for currency=%s, network=%s (normalized from input)", currency, network)
		return nil, gerror.Newf("未找到 %s 的提现限额配置（网络: %s）", currency, network)
	}

	// 找出全局最小和最大限额
	minLimit := settings[0].AmountMin
	maxLimit := settings[0].AmountMax

	for _, setting := range settings {
		if setting.AmountMin.LessThan(minLimit) {
			minLimit = setting.AmountMin
		}
		if setting.AmountMax.GreaterThan(maxLimit) {
			maxLimit = setting.AmountMax
		}
	}

	return &service.WithdrawalAmountLimits{
		MinAmount: minLimit,
		MaxAmount: maxLimit,
		Currency:  currency,
		Network:   network,
	}, nil
}

// GetFeeSettings retrieves withdrawal fee settings from the database (for backward compatibility)
func (s *sWithdrawalConfig) GetFeeSettings(ctx context.Context, currency, network string) (*service.WithdrawalFeeSettings, error) {
	// 标准化参数
	currency = s.normalizeCurrency(currency)
	network = s.normalizeNetwork(network)

	// 特殊处理CNY，使用Fiat网络
	if currency == "CNY" {
		network = "Fiat"
	}

	// 获取第一个费用设置（通常是默认的或最常用的）
	var setting entity.WithdrawalFeeSettings
	err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ?", currency).
		Where("network = ?", network).
		Where("status = ?", 1).
		WhereNull("deleted_at").
		OrderAsc("amount_min").
		Limit(1).
		Scan(&setting)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get withdrawal fee settings for %s/%s: %v", currency, network, err)
		return nil, gerror.Newf("未找到 %s/%s 的手续费配置", currency, network)
	}

	if setting.Id == 0 {
		return nil, gerror.Newf("未找到 %s/%s 的手续费配置", currency, network)
	}

	return &service.WithdrawalFeeSettings{
		FeeType:  setting.FeeType,
		FeeValue: setting.FeeValue,
		Currency: currency,
		Network:  network,
	}, nil
}

// GetFeeSettingsByAmount retrieves withdrawal fee settings based on amount range
func (s *sWithdrawalConfig) GetFeeSettingsByAmount(ctx context.Context, currency, network string, amount decimal.Decimal) (*service.WithdrawalFeeSettings, error) {
	// 标准化参数
	currency = s.normalizeCurrency(currency)
	network = s.normalizeNetwork(network)

	// 特殊处理CNY，使用Fiat网络
	if currency == "CNY" {
		network = "Fiat"
	}

	// 查询符合条件的费用设置
	// 注意：数据库中可能有重叠的范围，我们选择amount_min最大的（最具体的范围）
	var setting entity.WithdrawalFeeSettings
	err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ?", currency).
		Where("network = ?", network).
		Where("amount_min <= ?", amount).
		Where("amount_max >= ?", amount).
		Where("status = ?", 1).
		WhereNull("deleted_at").
		OrderDesc("amount_min"). // 优先匹配更具体的范围（amount_min更大的）
		Limit(1).
		Scan(&setting)

	g.Log().Debugf(ctx, "Query fee settings: currency=%s, network=%s, amount=%s",
		currency, network, amount.String())

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get fee settings for amount %s: %v", amount.String(), err)
		return nil, gerror.Wrap(err, "查询提现手续费设置失败")
	}

	if setting.Id == 0 {
		// 如果没有找到匹配的设置，记录所有可用的设置用于调试
		var allSettings []entity.WithdrawalFeeSettings
		dao.WithdrawalFeeSettings.Ctx(ctx).
			Where("currency = ?", currency).
			Where("network = ?", network).
			Where("status = ?", 1).
			WhereNull("deleted_at").
			OrderAsc("amount_min").
			Scan(&allSettings)

		g.Log().Warningf(ctx, "No fee setting found for amount %s. Available settings for %s/%s:",
			amount.String(), currency, network)
		for _, s := range allSettings {
			g.Log().Warningf(ctx, "  - Range: %s to %s, Type: %s, Value: %s",
				s.AmountMin.String(), s.AmountMax.String(), s.FeeType, s.FeeValue.String())
		}

		// 尝试查找该币种/网络的默认设置（amount_min=0 且 amount_max很大的）
		err = dao.WithdrawalFeeSettings.Ctx(ctx).
			Where("currency = ?", currency).
			Where("network = ?", network).
			Where("amount_min = ?", 0).
			Where("status = ?", 1).
			WhereNull("deleted_at").
			OrderDesc("amount_max").
			Limit(1).
			Scan(&setting)

		if err != nil || setting.Id == 0 {
			return nil, gerror.Newf("提现金额 %s %s 未找到对应的手续费设置（网络: %s）",
				amount.String(), currency, network)
		}
	}

	g.Log().Infof(ctx, "Found matching fee range for %s/%s amount=%s: min=%s, max=%s, type=%s, value=%s",
		currency, network, amount.String(), setting.AmountMin.String(), setting.AmountMax.String(),
		setting.FeeType, setting.FeeValue.String())

	return &service.WithdrawalFeeSettings{
		FeeType:  setting.FeeType,
		FeeValue: setting.FeeValue,
		Currency: currency,
		Network:  network,
	}, nil
}

// CalculateWithdrawalFee calculates the withdrawal fee based on amount and settings
func (s *sWithdrawalConfig) CalculateWithdrawalFee(ctx context.Context, currency, network string, amount decimal.Decimal) (decimal.Decimal, error) {
	// Get fee settings based on amount range
	feeSettings, err := s.GetFeeSettingsByAmount(ctx, currency, network, amount)
	if err != nil {
		return decimal.Zero, err
	}

	var fee decimal.Decimal
	switch strings.ToLower(feeSettings.FeeType) {
	case "fixed":
		fee = feeSettings.FeeValue
	case "percent":
		fee = amount.Mul(feeSettings.FeeValue).Div(decimal.NewFromInt(100))
	default:
		g.Log().Warningf(ctx, "Unknown fee type %s for %s/%s, using fee value directly",
			feeSettings.FeeType, currency, network)
		fee = feeSettings.FeeValue
	}

	// 确保手续费不为负数
	if fee.IsNegative() {
		fee = decimal.Zero
	}

	return fee, nil
}

// ValidateWithdrawalAmount validates if the amount is within allowed limits
func (s *sWithdrawalConfig) ValidateWithdrawalAmount(ctx context.Context, currency, network string, amount decimal.Decimal) error {
	limits, err := s.GetAmountLimits(ctx, currency, network)
	if err != nil {
		return err
	}

	if amount.LessThan(limits.MinAmount) {
		return gerror.Newf("提现金额不能小于最小限额 %s %s", limits.MinAmount.String(), currency)
	}

	if amount.GreaterThan(limits.MaxAmount) {
		return gerror.Newf("提现金额不能大于最大限额 %s %s", limits.MaxAmount.String(), currency)
	}

	// 确认存在覆盖该金额的费用设置
	_, err = s.GetFeeSettingsByAmount(ctx, currency, network, amount)
	if err != nil {
		return gerror.Wrap(err, "验证提现金额范围失败")
	}

	return nil
}

// IsWithdrawalEnabled checks if withdrawal is globally enabled
func (s *sWithdrawalConfig) IsWithdrawalEnabled(ctx context.Context) (bool, error) {
	// 这可以从系统设置表或专门的提现控制表检查
	// 暂时返回true作为占位符
	// TODO: 实现实际的数据库检查
	return true, nil
}

// GetApprovalSettings retrieves withdrawal approval settings from the database
func (s *sWithdrawalConfig) GetApprovalSettings(ctx context.Context, currency, network string) (*service.WithdrawalApprovalSettings, error) {
	// 标准化参数
	currency = s.normalizeCurrency(currency)
	network = s.normalizeNetwork(network)

	// 暂时返回默认设置，实际应该从withdrawal_approval_settings表查询
	// TODO: 实现从withdrawal_approval_settings表查询的逻辑
	g.Log().Warningf(ctx, "Approval settings not yet implemented for %s/%s, using defaults", currency, network)
	return &service.WithdrawalApprovalSettings{
		AutoReleaseMin:    decimal.Zero,
		AutoReleaseMax:    decimal.Zero,
		ApprovalAutoMin:   decimal.Zero,
		ApprovalAutoMax:   decimal.Zero,
		ApprovalManualMin: decimal.Zero,
		ApprovalManualMax: decimal.Zero,
	}, nil
}

// DetermineWithdrawalStatus determines audit and processing status based on amount
func (s *sWithdrawalConfig) DetermineWithdrawalStatus(ctx context.Context, currency, network string, amount decimal.Decimal) (auditStatus uint, processingStatus uint, error error) {
	// Get approval settings
	settings, err := s.GetApprovalSettings(ctx, currency, network)
	if err != nil {
		// If we can't get settings, default to manual review for safety
		g.Log().Errorf(ctx, "Failed to get approval settings, defaulting to manual review: %v", err)
		return 2, 3, nil // 2=待审核, 3=待人工转账
	}

	// Determine audit status and processing status based on amount ranges

	// 1. Check if amount falls within auto-release range (no approval needed)
	if amount.GreaterThanOrEqual(settings.AutoReleaseMin) &&
		(settings.AutoReleaseMax.IsZero() || amount.LessThanOrEqual(settings.AutoReleaseMax)) {
		// 免审，自动放币处理中
		return 1, 1, nil // audit_status=1(免审), processing_status=1(自动放币处理中)
	}

	// 2. Check if amount needs approval but can be auto-released after approval
	if amount.GreaterThanOrEqual(settings.ApprovalAutoMin) &&
		(settings.ApprovalAutoMax.IsZero() || amount.LessThanOrEqual(settings.ApprovalAutoMax)) {
		// 待审核，审核通过后自动放币
		return 2, 0, nil // audit_status=2(待审核), processing_status=0(初始状态，审核后变为1)
	}

	// 3. Check if amount needs approval and manual transfer after approval
	if amount.GreaterThanOrEqual(settings.ApprovalManualMin) &&
		(settings.ApprovalManualMax.IsZero() || amount.LessThanOrEqual(settings.ApprovalManualMax)) {
		// 待审核，审核通过后需要人工转账
		return 2, 0, nil // audit_status=2(待审核), processing_status=0(初始状态，审核后变为3)
	}

	// 4. If amount doesn't fall into any configured range, default to manual for safety
	g.Log().Warningf(ctx, "Amount %s doesn't match any approval range for %s/%s, defaulting to manual review",
		amount.String(), currency, network)
	return 2, 0, nil // audit_status=2(待审核), processing_status=0(初始状态，审核后变为3)
}

// GetAllFeeSettings retrieves all fee settings for a currency and network, ordered by amount_min
func (s *sWithdrawalConfig) GetAllFeeSettings(ctx context.Context, currency, network string) ([]*service.WithdrawalFeeDetail, error) {
	// 标准化参数
	currency = s.normalizeCurrency(currency)
	network = s.normalizeNetwork(network)

	// 特殊处理CNY，使用Fiat网络
	if currency == "CNY" {
		network = "Fiat"
	}

	// Get all fee settings from database
	var feeSettings []entity.WithdrawalFeeSettings
	err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ?", currency).
		Where("network = ?", network).
		Where("status = ?", 1).
		WhereNull("deleted_at").
		OrderAsc("amount_min").
		Scan(&feeSettings)

	if err != nil {
		return nil, gerror.Wrap(err, "查询提现手续费设置失败")
	}

	// Convert to service layer structure
	var result []*service.WithdrawalFeeDetail
	for _, fs := range feeSettings {
		result = append(result, &service.WithdrawalFeeDetail{
			ID:        uint(fs.Id),
			FeeType:   fs.FeeType,
			FeeValue:  fs.FeeValue,
			AmountMin: fs.AmountMin,
			AmountMax: fs.AmountMax,
			Currency:  fs.Currency,
			Network:   fs.Network,
		})
	}

	return result, nil
}

// FormatFeeRangeDisplay formats tiered fee settings into a numbered list for display
func (s *sWithdrawalConfig) FormatFeeRangeDisplay(ctx context.Context, currency, network string) (string, error) {
	// Get all fee settings
	feeSettings, err := s.GetAllFeeSettings(ctx, currency, network)
	if err != nil {
		return "", err
	}

	if len(feeSettings) == 0 {
		return fmt.Sprintf("0 %s", currency), nil
	}

	// Build formatted string with numbered list
	var result strings.Builder
	for i, fee := range feeSettings {
		if i > 0 {
			result.WriteString("\n")
		}

		// Format: 1. 比例||金额+代币单位 (amount_min<金额<=amount_max)
		result.WriteString(fmt.Sprintf("%d. ", i+1))

		// Add fee value
		if strings.ToLower(fee.FeeType) == "percent" {
			result.WriteString(fmt.Sprintf("%s%%", fee.FeeValue.String()))
		} else {
			result.WriteString(fmt.Sprintf("%s %s", fee.FeeValue.String(), currency))
		}

		// Add amount range
		result.WriteString(" (")
		if fee.AmountMin.IsZero() && fee.AmountMax.IsZero() {
			result.WriteString("所有金额")
		} else if fee.AmountMin.IsZero() {
			result.WriteString(fmt.Sprintf("金额≤%s", fee.AmountMax.String()))
		} else if fee.AmountMax.IsZero() {
			result.WriteString(fmt.Sprintf("金额>%s", fee.AmountMin.String()))
		} else {
			result.WriteString(fmt.Sprintf("%s<金额≤%s", fee.AmountMin.String(), fee.AmountMax.String()))
		}
		result.WriteString(")")
	}

	return result.String(), nil
}
