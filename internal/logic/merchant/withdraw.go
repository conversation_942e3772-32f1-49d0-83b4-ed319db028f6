package merchant

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/config"
	"merchant-server/internal/dao"
	"merchant-server/internal/logic/validation"
	"merchant-server/internal/model/do"
	"merchant-server/internal/model/entity"
	"merchant-server/internal/service"
	"merchant-server/internal/wallet"
)

type sWithdrawService struct{}

func init() {
	service.RegisterWithdrawService(NewWithdrawService())
}

func NewWithdrawService() *sWithdrawService {
	return &sWithdrawService{}
}

// getClientIP 获取客户端IP地址
func (s *sWithdrawService) getClientIP(ctx context.Context) string {
	// 从请求上下文中获取客户端IP
	request := g.RequestFromCtx(ctx)
	if request != nil {
		// 优先从 X-Forwarded-For 头获取真实IP
		if ip := request.Header.Get("X-Forwarded-For"); ip != "" {
			// 取第一个IP，移除空白字符
			firstIP := strings.TrimSpace(strings.Split(ip, ",")[0])
			if validIP := s.cleanIP(firstIP); validIP != "" {
				return validIP
			}
		}
		// 其次从 X-Real-IP 头获取
		if ip := request.Header.Get("X-Real-IP"); ip != "" {
			if validIP := s.cleanIP(strings.TrimSpace(ip)); validIP != "" {
				return validIP
			}
		}
		// 最后使用远程地址
		if clientIP := request.GetClientIp(); clientIP != "" {
			if validIP := s.cleanIP(clientIP); validIP != "" {
				return validIP
			}
		}
	}
	return "127.0.0.1" // 默认值
}

// cleanIP 清理和验证IP地址
func (s *sWithdrawService) cleanIP(ip string) string {
	if ip == "" {
		return ""
	}

	// 移除端口号（如果存在）
	if host, _, err := net.SplitHostPort(ip); err == nil {
		ip = host
	}

	// 移除空白字符
	ip = strings.TrimSpace(ip)

	// 验证IP格式
	if net.ParseIP(ip) != nil {
		return ip
	}

	return ""
}

// mapChainAndToken 映射API参数到数据库存储格式
func (s *sWithdrawService) mapChainAndToken(apiChain, apiToken string) (chainType, tokenSymbol string) {
	switch strings.ToUpper(apiChain) {
	case "TRX":
		return "TRON", "TRX"
	case "TRC20":
		// TRC20代币，使用传入的token作为符号，通常是USDT
		symbol := strings.ToUpper(apiToken)
		if symbol == "NATIVE" {
			symbol = "USDT" // TRC20默认是USDT
		}
		return "TRON", symbol
	case "ETH":
		return "ETH", "ETH"
	case "ERC20":
		// ERC20代币，使用传入的token作为符号，通常是USDT
		symbol := strings.ToUpper(apiToken)
		if symbol == "NATIVE" {
			symbol = "USDT" // ERC20默认是USDT
		}
		return "ETH", symbol
	default:
		// fallback: 直接使用原值
		return apiChain, apiToken
	}
}

// mapChainToTokenSymbol 映射链类型到标准代币符号（用于新的费用配置系统）
func (s *sWithdrawService) mapChainToTokenSymbol(chain, token string) string {
	switch strings.ToUpper(chain) {
	case "TRC20":
		return "USDT" // TRC20 统一映射为 USDT
	case "ERC20":
		return "USDT" // ERC20 统一映射为 USDT
	case "TRX":
		return "TRX" // TRX 原生币
	case "ETH":
		return "ETH" // ETH 原生币
	default:
		// 对于其他情况，使用传入的token参数
		return strings.ToUpper(token)
	}
}

// calculateTokenFee 计算代币提现手续费（使用新的配置系统）
func (s *sWithdrawService) calculateTokenFee(ctx context.Context, amountDec decimal.Decimal, chain, token string) (string, error) {
	// 映射到标准代币符号
	tokenSymbol := s.mapChainToTokenSymbol(chain, token)

	// 获取费用类型配置
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "rate")
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get fee_type config: %v", err)
		return "", gerror.New("手续费配置错误 (无法获取费用类型配置)")
	}

	feeType = strings.ToLower(strings.TrimSpace(feeType))
	var feeDec decimal.Decimal

	switch feeType {
	case "rate":
		// 使用GetMapKey方法获取比例费率配置
		rateValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", tokenSymbol, "0")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get fee_rate for token %s: %v", tokenSymbol, err)
			return "", gerror.Newf("手续费配置错误 (无法获取 %s 的费率配置)", tokenSymbol)
		}

		// 转换为字符串然后解析为decimal
		feeRateStr := fmt.Sprintf("%v", rateValue)
		rateDec, err := decimal.NewFromString(feeRateStr)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse fee_rate '%s' for token %s: %v", feeRateStr, tokenSymbol, err)
			return "", gerror.Wrapf(err, "手续费配置错误 (费率格式不正确): %s", tokenSymbol)
		}

		if rateDec.IsNegative() {
			g.Log().Warningf(ctx, "Fee rate is negative ('%s') for token %s, using 0", feeRateStr, tokenSymbol)
			rateDec = decimal.Zero
		}

		// 计算费用: amount * (rate / 100)
		feeDec = amountDec.Mul(rateDec).Div(decimal.NewFromInt(100))

	case "fix":
		// 使用GetMapKey方法获取固定费用配置
		amountValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", tokenSymbol, "0")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get fee_amount for token %s: %v", tokenSymbol, err)
			return "", gerror.Newf("手续费配置错误 (无法获取 %s 的固定费用配置)", tokenSymbol)
		}

		// 转换为字符串然后解析为decimal
		feeAmountStr := fmt.Sprintf("%v", amountValue)
		fixedFee, err := decimal.NewFromString(feeAmountStr)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse fee_amount '%s' for token %s: %v", feeAmountStr, tokenSymbol, err)
			return "", gerror.Wrapf(err, "手续费配置错误 (固定费用格式不正确): %s", tokenSymbol)
		}

		if fixedFee.IsNegative() {
			g.Log().Warningf(ctx, "Fixed fee is negative ('%s') for token %s, using 0", feeAmountStr, tokenSymbol)
			fixedFee = decimal.Zero
		}

		feeDec = fixedFee

	case "":
		g.Log().Warningf(ctx, "Fee type not configured, using zero fee for token %s", tokenSymbol)
		feeDec = decimal.Zero

	default:
		g.Log().Errorf(ctx, "Unsupported fee_type '%s' for token %s", feeType, tokenSymbol)
		return "", gerror.Newf("不支持的手续费类型配置: %s", feeType)
	}

	// 确保最终计算的费用不为负数
	if feeDec.IsNegative() {
		feeDec = decimal.Zero
	}

	return feeDec.String(), nil
}

// CreateWithdraw 创建提现申请
func (s *sWithdrawService) CreateWithdraw(ctx context.Context, merchantId int64, req *v1.WithdrawCreateReq) (*v1.WithdrawCreateData, error) {
	// 1. 输入验证
	if err := s.validateWithdrawRequest(req); err != nil {
		return nil, err
	}

	// 2. 检查订单号是否已存在
	exists, err := s.checkOrderExists(ctx, merchantId, req.OrderID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.New("订单号已存在")
	}

	// 3. 计算手续费
	fee, netAmount, err := s.calculateFee(ctx, req.Chain, req.Token, req.Amount)
	if err != nil {
		return nil, err
	}

	// 4. 验证提现限额（配置驱动）
	if err := s.validateWithdrawLimits(ctx, req.Chain, req.Token, req.Amount); err != nil {
		return nil, err
	}

	// 5. 验证商户余额
	if err := s.validateMerchantBalance(ctx, merchantId, req.Chain, req.Token, req.Amount); err != nil {
		return nil, err
	}

	// 6. 使用钱包库准备提现（冻结余额）
	walletService := GetWalletService()

	// 映射链类型和代币符号
	_, tokenSymbol := s.mapChainAndToken(req.Chain, req.Token)

	// 构建提现请求
	withdrawReq := &wallet.WithdrawRequest{
		MerchantID:    uint64(merchantId),
		TokenSymbol:   tokenSymbol,
		Amount:        decimal.RequireFromString(req.Amount),
		Address:       req.ToAddress,
		OrderID:       req.OrderID,
		Memo:          req.Memo,
		RequestSource: "api",
		RequestIP:     s.getClientIP(ctx),
	}

	// 准备提现（冻结余额）
	walletResult, err := walletService.PrepareWithdraw(ctx, withdrawReq)
	if err != nil {
		return nil, gerror.Wrap(err, "准备提现失败")
	}

	// 7. 创建提现记录
	var withdrawId int64
	result, err := dao.MerchantWithdraws.Ctx(ctx).Insert(do.MerchantWithdraws{
		MerchantId:   merchantId,
		OrderNo:      req.OrderID,
		Address:      req.ToAddress,
		Chan:         req.Chain,
		Name:         req.Token,
		Amount:       req.Amount,
		HandlingFee:  fee,
		ActualAmount: netAmount,
		State:        "1", // 1-待审核
		UserRemark:   req.Memo,
		CreatedAt:    gtime.Now(),
		UpdatedAt:    gtime.Now(),
	})

	if err != nil {
		// 如果创建提现记录失败，需要取消余额冻结
		if _, cancelErr := walletService.CancelWithdraw(ctx, withdrawReq); cancelErr != nil {
			g.Log().Errorf(ctx, "取消提现冻结失败: %v", cancelErr)
		}
		return nil, gerror.Wrap(err, "创建提现申请失败")
	}

	id, err := result.LastInsertId()
	if err != nil {
		// 如果获取ID失败，也需要取消余额冻结
		if _, cancelErr := walletService.CancelWithdraw(ctx, withdrawReq); cancelErr != nil {
			g.Log().Errorf(ctx, "取消提现冻结失败: %v", cancelErr)
		}
		return nil, gerror.Wrap(err, "获取提现记录ID失败")
	}
	withdrawId = id

	// 记录钱包交易ID到日志
	g.Log().Infof(ctx, "提现申请创建成功, withdrawId: %d, walletTransactionId: %d",
		withdrawId, walletResult.TransactionID)

	return &v1.WithdrawCreateData{
		ID:          withdrawId,
		OrderID:     req.OrderID,
		Status:      "pending",
		ToAddress:   req.ToAddress,
		Chain:       req.Chain,
		Token:       req.Token,
		Amount:      req.Amount,
		Fee:         fee,
		NetAmount:   netAmount,
		CreatedAt:   gtime.Now().String(),
		EstimatedAt: gtime.Now().Add(time.Hour * 24).String(), // 预计24小时内完成
	}, nil
}

// checkOrderExists 检查订单号是否已存在
func (s *sWithdrawService) checkOrderExists(ctx context.Context, merchantId int64, orderID string) (bool, error) {
	count, err := dao.MerchantWithdraws.Ctx(ctx).
		Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId).
		Where(dao.MerchantWithdraws.Columns().OrderNo, orderID).
		Count()

	if err != nil {
		return false, gerror.Wrap(err, "检查订单号失败")
	}

	return count > 0, nil
}

// validateWithdrawRequest 验证提现请求
func (s *sWithdrawService) validateWithdrawRequest(req *v1.WithdrawCreateReq) error {
	// 验证订单号
	if err := validation.OrderValidatorInst.ValidateOrderID(req.OrderID); err != nil {
		return gerror.Wrap(err, "订单号验证失败")
	}

	// 验证区块链地址
	if err := validation.AddressValidator.ValidateAddress(req.Chain, req.ToAddress); err != nil {
		return gerror.Wrap(err, "目标地址验证失败")
	}

	// 验证金额
	minAmount := decimal.NewFromFloat(0.000001) // 最小提现金额
	maxAmount := decimal.NewFromFloat(1000000)  // 最大提现金额
	if err := validation.AmountValidatorInst.ValidateAmount(req.Amount, minAmount, maxAmount); err != nil {
		return gerror.Wrap(err, "提现金额验证失败")
	}

	// 验证金额精度（根据不同代币设置不同精度）
	maxDecimals := s.getMaxDecimals(req.Token)
	if err := validation.AmountValidatorInst.ValidateAmountPrecision(req.Amount, maxDecimals); err != nil {
		return gerror.Wrap(err, "金额精度验证失败")
	}

	// 验证备注（如果有）
	if req.Memo != "" && len(req.Memo) > 200 {
		return gerror.New("备注长度不能超过200字符")
	}

	return nil
}

// getMaxDecimals 获取代币的最大小数位数
func (s *sWithdrawService) getMaxDecimals(token string) int {
	// 根据不同代币返回不同的精度
	switch strings.ToUpper(token) {
	case "BTC":
		return 8
	case "ETH":
		return 18
	case "TRX":
		return 6
	case "USDT", "USDC":
		return 6
	default:
		return 8 // 默认8位小数
	}
}

// calculateFee 计算手续费（使用新的配置系统）
func (s *sWithdrawService) calculateFee(ctx context.Context, chain, token, amount string) (fee, netAmount string, err error) {
	// 将字符串金额转换为 decimal
	amountDecimal, parseErr := decimal.NewFromString(amount)
	if parseErr != nil {
		return "", "", gerror.Wrap(parseErr, "金额格式错误")
	}

	// 使用新的代币手续费计算逻辑
	feeStr, err := s.calculateTokenFee(ctx, amountDecimal, chain, token)
	if err != nil {
		return "", "", gerror.Wrap(err, "计算手续费失败")
	}

	// 转换手续费为decimal以便计算净额
	feeDecimal, err := decimal.NewFromString(feeStr)
	if err != nil {
		return "", "", gerror.Wrap(err, "手续费格式错误")
	}

	// 计算实际到账金额
	netAmountDecimal := amountDecimal.Sub(feeDecimal)

	// 检查最小到账金额 - 使用配置的最小金额
	tokenSymbol := s.mapChainToTokenSymbol(chain, token)
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.000001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小到账金额失败: %v，使用默认值", err)
		minAmountValue = "0.000001"
	}
	minAmountStr := fmt.Sprintf("%v", minAmountValue)
	minAmount, _ := decimal.NewFromString(minAmountStr)

	if netAmountDecimal.LessThan(minAmount) {
		return "", "", gerror.Newf("扣除手续费后金额过小，最小到账金额: %s %s", minAmount.String(), tokenSymbol)
	}

	// 确保到账金额不为负数
	if netAmountDecimal.IsNegative() {
		return "", "", gerror.New("手续费过高，到账金额为负数")
	}

	return feeDecimal.String(), netAmountDecimal.String(), nil
}

// QueryWithdraws 查询提现记录
func (s *sWithdrawService) QueryWithdraws(ctx context.Context, merchantId int64, req *v1.WithdrawQueryReq) (*v1.PageResponse, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	// 添加筛选条件
	if req.OrderID != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().OrderNo, req.OrderID)
	}

	if req.ToAddress != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Address, req.ToAddress)
	}

	if req.Chain != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Chan, req.Chain)
	}

	if req.Token != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Name, req.Token)
	}

	if req.Status != "" {
		// 状态映射
		statusMap := map[string]string{
			"pending":    "1",
			"processing": "2",
			"completed":  "4",
			"failed":     "5",
			"cancelled":  "3",
		}
		if dbStatus, ok := statusMap[req.Status]; ok {
			query = query.Where(dao.MerchantWithdraws.Columns().State, dbStatus)
		}
	}

	if req.TxHash != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().TxHash, req.TxHash)
	}

	if req.StartTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" >= ?", req.StartTime)
	}

	if req.EndTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	if req.MinAmount != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Amount+" >= ?", req.MinAmount)
	}

	if req.MaxAmount != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Amount+" <= ?", req.MaxAmount)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录总数失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	var withdraws []*entity.MerchantWithdraws
	err = query.OrderDesc(dao.MerchantWithdraws.Columns().CreatedAt).
		Limit(req.PageSize).
		Offset(offset).
		Scan(&withdraws)

	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}

	// 转换为API响应格式
	records := make([]*v1.WithdrawRecord, len(withdraws))
	for i, withdraw := range withdraws {
		records[i] = s.convertWithdrawToRecord(withdraw)
	}

	return &v1.PageResponse{
		Total:    int64(total),
		Page:     req.Page,
		PageSize: req.PageSize,
		Data:     records,
	}, nil
}

// convertWithdrawToRecord 转换提现记录格式
func (s *sWithdrawService) convertWithdrawToRecord(withdraw *entity.MerchantWithdraws) *v1.WithdrawRecord {
	// 状态映射
	statusMap := map[string]string{
		"1": "pending",
		"2": "processing",
		"3": "cancelled",
		"4": "completed",
		"5": "failed",
	}

	status := statusMap[gconv.String(withdraw.State)]
	if status == "" {
		status = "pending"
	}

	// 正确格式化时间
	createdAt := ""
	updatedAt := ""

	if withdraw.CreatedAt != nil && !withdraw.CreatedAt.IsZero() {
		createdAt = withdraw.CreatedAt.String()
	}
	if withdraw.UpdatedAt != nil && !withdraw.UpdatedAt.IsZero() {
		updatedAt = withdraw.UpdatedAt.String()
	}

	return &v1.WithdrawRecord{
		ID:         gconv.Int64(withdraw.WithdrawsId),
		MerchantID: gconv.Int64(withdraw.MerchantId),
		OrderID:    gconv.String(withdraw.OrderNo),
		ToAddress:  gconv.String(withdraw.Address),
		Chain:      gconv.String(withdraw.Chan),
		Token:      gconv.String(withdraw.Name),
		Amount:     gconv.String(withdraw.Amount),
		Fee:        gconv.String(withdraw.HandlingFee),
		NetAmount:  gconv.String(withdraw.ActualAmount),
		TxHash:     gconv.String(withdraw.TxHash),
		Status:     status,
		Memo:       gconv.String(withdraw.UserRemark),
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}
}

// GetWithdrawDetail 获取提现详情
func (s *sWithdrawService) GetWithdrawDetail(ctx context.Context, merchantId int64, req *v1.WithdrawDetailReq) (*v1.WithdrawRecord, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	if req.ID > 0 {
		query = query.Where(dao.MerchantWithdraws.Columns().WithdrawsId, req.ID)
	} else if req.OrderID != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().OrderNo, req.OrderID)
	} else {
		return nil, gerror.New("请提供提现记录ID或订单号")
	}

	var withdraw *entity.MerchantWithdraws
	err := query.Scan(&withdraw)
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现详情失败")
	}

	if withdraw == nil {
		return nil, gerror.New("提现记录不存在")
	}

	return s.convertWithdrawToRecord(withdraw), nil
}

// CancelWithdraw 取消提现申请
func (s *sWithdrawService) CancelWithdraw(ctx context.Context, merchantId int64, req *v1.WithdrawCancelReq) (*v1.WithdrawCancelData, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	if req.ID > 0 {
		query = query.Where(dao.MerchantWithdraws.Columns().WithdrawsId, req.ID)
	} else if req.OrderID != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().OrderNo, req.OrderID)
	} else {
		return nil, gerror.New("请提供提现记录ID或订单号")
	}

	// 检查提现记录是否存在且可以取消
	var withdraw *entity.MerchantWithdraws
	err := query.Scan(&withdraw)
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现记录失败")
	}

	if withdraw == nil {
		return nil, gerror.New("提现记录不存在")
	}

	// 只有待审核状态的提现可以取消
	if gconv.String(withdraw.State) != "1" {
		return nil, gerror.New("只有待审核状态的提现可以取消")
	}

	// 使用钱包库取消提现（解锁余额）
	walletService := GetWalletService()

	// 映射链类型和代币符号
	_, tokenSymbol := s.mapChainAndToken(gconv.String(withdraw.Chan), gconv.String(withdraw.Name))

	// 构建提现请求
	withdrawReq := &wallet.WithdrawRequest{
		MerchantID:    uint64(merchantId),
		TokenSymbol:   tokenSymbol,
		Amount:        decimal.RequireFromString(gconv.String(withdraw.Amount)),
		Address:       gconv.String(withdraw.Address),
		OrderID:       gconv.String(withdraw.OrderNo),
		Memo:          req.Reason,
		RequestSource: "api",
		RequestIP:     s.getClientIP(ctx),
	}

	// 取消提现（解锁余额）
	_, err = walletService.CancelWithdraw(ctx, withdrawReq)
	if err != nil {
		return nil, gerror.Wrap(err, "解锁余额失败")
	}

	// 更新提现记录状态
	_, err = dao.MerchantWithdraws.Ctx(ctx).
		Where(dao.MerchantWithdraws.Columns().WithdrawsId, withdraw.WithdrawsId).
		Update(g.Map{
			dao.MerchantWithdraws.Columns().State:          "3", // 3-已取消
			dao.MerchantWithdraws.Columns().RefuseReasonZh: req.Reason,
			dao.MerchantWithdraws.Columns().UpdatedAt:      gtime.Now().String(),
		})

	if err != nil {
		// 如果更新状态失败，记录错误但不回滚余额操作
		// 因为钱包库的操作已经完成，避免数据不一致
		g.Log().Errorf(ctx, "更新提现状态失败，但余额已解锁: %v", err)
		return nil, gerror.Wrap(err, "更新提现状态失败")
	}

	return &v1.WithdrawCancelData{
		ID:          gconv.Int64(withdraw.WithdrawsId),
		OrderID:     gconv.String(withdraw.OrderNo),
		Status:      "cancelled",
		Reason:      req.Reason,
		CancelledAt: gtime.Now().String(),
	}, nil
}

// GetWithdrawFee 获取提现手续费
func (s *sWithdrawService) GetWithdrawFee(ctx context.Context, merchantId int64, req *v1.WithdrawFeeReq) (*v1.WithdrawFeeData, error) {
	// 计算手续费
	fee, netAmount, err := s.calculateFee(ctx, req.Chain, req.Token, req.Amount)
	if err != nil {
		return nil, err
	}

	// 获取商户可用余额
	availableBalance, err := s.getMerchantAvailableBalance(ctx, merchantId, req.Chain, req.Token)
	if err != nil {
		return nil, gerror.Wrap(err, "获取可用余额失败")
	}

	// 构建费率显示字符串（基于实际使用的配置）
	tokenSymbol := s.mapChainToTokenSymbol(req.Chain, req.Token)
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "fix")
	if err != nil {
		g.Log().Warningf(ctx, "获取费用类型失败: %v，使用默认值", err)
		feeType = "fix"
	}

	var feeRateStr string
	switch strings.ToLower(strings.TrimSpace(feeType)) {
	case "fix":
		// 获取固定费用
		amountValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", tokenSymbol, "0")
		if err != nil {
			feeRateStr = "固定手续费"
		} else {
			feeAmountStr := fmt.Sprintf("%v", amountValue)
			feeRateStr = fmt.Sprintf("固定 %s %s", feeAmountStr, req.Token)
		}
	case "rate":
		// 获取费率
		rateValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", tokenSymbol, "0")
		if err != nil {
			feeRateStr = "比例手续费"
		} else {
			rateStr := fmt.Sprintf("%v", rateValue)
			feeRateStr = fmt.Sprintf("%s%%", rateStr)
		}
	default:
		feeRateStr = "动态费率"
	}

	// 获取最小和最大提现限额（使用实际配置）
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.000001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小限额失败: %v，使用默认值", err)
		minAmountValue = "0.000001"
	}
	minAmount := fmt.Sprintf("%v", minAmountValue)

	maxAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.max_single_amount", tokenSymbol, "1000000")
	if err != nil {
		g.Log().Warningf(ctx, "获取最大限额失败: %v，使用默认值", err)
		maxAmountValue = "1000000"
	}
	maxAmount := fmt.Sprintf("%v", maxAmountValue)

	return &v1.WithdrawFeeData{
		FeeInfo: v1.FeeInfo{
			Fee:         fee,
			FeeCurrency: req.Token,
			FeeRate:     feeRateStr,
		},
		NetAmount:     netAmount,
		MinAmount:     minAmount,
		MaxAmount:     maxAmount,
		Available:     availableBalance.String(),
		EstimatedTime: s.getEstimatedTime(req.Chain),
	}, nil
}

// validateMerchantBalance 验证商户余额（使用钱包库）
func (s *sWithdrawService) validateMerchantBalance(ctx context.Context, merchantId int64, chain, token, amount string) error {
	// 将字符串金额转换为 decimal
	amountDecimal, err := decimal.NewFromString(amount)
	if err != nil {
		return gerror.Wrap(err, "金额格式错误")
	}

	// 检查是否有足够的可用余额（使用钱包库）
	availableBalance, err := s.getMerchantAvailableBalance(ctx, merchantId, chain, token)
	if err != nil {
		return gerror.Wrap(err, "获取可用余额失败")
	}

	if availableBalance.LessThan(amountDecimal) {
		return gerror.Newf("可用余额不足，当前可用余额: %s, 提现金额: %s", availableBalance.String(), amount)
	}

	return nil
}

// getMerchantAvailableBalance 获取商户可用余额（使用钱包库）
func (s *sWithdrawService) getMerchantAvailableBalance(ctx context.Context, merchantId int64, chain, token string) (decimal.Decimal, error) {
	// 映射链类型和代币符号
	_, tokenSymbol := s.mapChainAndToken(chain, token)

	// 使用钱包库获取余额信息
	walletService := GetWalletService()
	walletInfo, err := walletService.GetBalance(ctx, uint64(merchantId), tokenSymbol)
	if err != nil {
		// 如果钱包库返回错误，可能是钱包不存在，返回0余额
		if walletErr := wallet.GetWalletError(err); walletErr != nil && walletErr.Code == wallet.ErrCodeWalletNotFound {
			return decimal.Zero, nil
		}
		return decimal.Zero, gerror.Wrap(err, "获取钱包余额失败")
	}

	// 修复：available_balance就是真实可用余额，不需要减去frozen_balance
	realAvailableBalance := walletInfo.Available

	// 确保可用余额不为负数（安全检查）
	if realAvailableBalance.LessThan(decimal.Zero) {
		realAvailableBalance = decimal.Zero
	}

	return realAvailableBalance, nil
}

// getEstimatedTime 获取预估到账时间
func (s *sWithdrawService) getEstimatedTime(chain string) string {
	// 根据不同区块链返回不同的预估时间
	switch strings.ToLower(chain) {
	case "trx", "tron":
		return "5-30分钟"
	case "eth", "ethereum":
		return "10-60分钟"
	case "bsc", "bnb":
		return "3-15分钟"
	case "polygon", "matic":
		return "1-10分钟"
	default:
		return "1-24小时"
	}
}

// 配置辅助函数 - 由于config包可能需要初始化，这里提供fallback机制

// getBoolConfig 获取布尔配置值
func (s *sWithdrawService) getBoolConfig(ctx context.Context, key string, defaultValue bool) (bool, error) {
	// 使用真实的配置管理器
	return config.GetBool(ctx, key, defaultValue)
}

// validateWithdrawLimits 验证提现限额（配置驱动）
func (s *sWithdrawService) validateWithdrawLimits(ctx context.Context, chain, token, amount string) error {
	// 检查提现功能是否启用
	withdrawalState, err := s.getBoolConfig(ctx, "withdrawals_setting.state", true)
	if err != nil {
		g.Log().Warningf(ctx, "获取提现开关失败: %v，默认启用", err)
		withdrawalState = true
	}
	if !withdrawalState {
		return gerror.New("提现功能已暂停")
	}

	amountDecimal, err := decimal.NewFromString(amount)
	if err != nil {
		return gerror.Wrap(err, "金额格式错误")
	}

	// 获取最小限额配置 - 使用 GetMapKey 从 Map 配置中获取
	tokenSymbol := strings.ToUpper(token)
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小限额失败: %v，使用默认值", err)
		minAmountValue = "0.001"
	}
	minAmountStr := fmt.Sprintf("%v", minAmountValue)

	minAmount, err := decimal.NewFromString(minAmountStr)
	if err != nil {
		g.Log().Warningf(ctx, "解析最小限额失败: %v，使用默认值", err)
		minAmount = decimal.NewFromFloat(0.001)
	}

	// 获取最大限额配置 - 使用 GetMapKey 从 Map 配置中获取
	maxAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.max_single_amount", tokenSymbol, "1000000")
	if err != nil {
		g.Log().Warningf(ctx, "获取最大限额失败: %v，使用默认值", err)
		maxAmountValue = "1000000"
	}
	maxAmountStr := fmt.Sprintf("%v", maxAmountValue)

	maxAmount, err := decimal.NewFromString(maxAmountStr)
	if err != nil {
		g.Log().Warningf(ctx, "解析最大限额失败: %v，使用默认值", err)
		maxAmount = decimal.NewFromFloat(1000000)
	}

	// 验证限额
	if amountDecimal.LessThan(minAmount) {
		return gerror.Newf("提现金额过小，最小限额: %s %s", minAmount.String(), token)
	}

	if amountDecimal.GreaterThan(maxAmount) {
		return gerror.Newf("提现金额过大，最大限额: %s %s", maxAmount.String(), token)
	}

	return nil
}
