package merchant

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/constants"
	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
	"merchant-server/internal/service"
	"merchant-server/internal/utility"
	"merchant-server/internal/wallet"
)

type sDepositService struct{}

func init() {
	service.RegisterDepositService(New())
}

func New() *sDepositService {
	return &sDepositService{}
}

// ProcessDeposit 处理充值确认（区块链回调时调用）
func (s *sDepositService) ProcessDeposit(ctx context.Context, merchantId int64, txHash, toAddress, chain, token, amount string) error {
	// 1. 验证参数
	if txHash == "" || toAddress == "" || chain == "" || token == "" || amount == "" {
		return gerror.New("充值参数不完整")
	}

	// 2. 检查是否已经处理过这笔充值
	exists, err := s.checkDepositExists(ctx, txHash)
	if err != nil {
		return gerror.Wrap(err, "检查充值记录失败")
	}
	if exists {
		g.Log().Infof(ctx, "充值已处理过，跳过: txHash=%s", txHash)
		return nil
	}

	// 3. 验证地址是否属于该商户
	addressInfo, err := s.validateDepositAddress(ctx, merchantId, toAddress, chain, token)
	if err != nil {
		return gerror.Wrap(err, "验证充值地址失败")
	}

	// 4. 处理充值
	// 映射链类型和代币符号
	_, tokenSymbol := s.mapChainAndToken(chain, token)

	// 构建充值请求
	depositReq := &wallet.DepositRequest{
		MerchantID:    uint64(merchantId),
		TokenSymbol:   tokenSymbol,
		Amount:        decimal.RequireFromString(amount),
		TxHash:        txHash,
		Memo:          "区块链充值",
		RequestSource: "blockchain",
	}

	// 先执行充值（钱包服务内部会处理事务）
	walletService := GetWalletService()
	walletResult, err := walletService.Deposit(ctx, depositReq)
	if err != nil {
		return gerror.Wrap(err, "钱包充值处理失败")
	}

	// 使用事务创建充值记录和回调记录
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 创建充值记录
		depositId, err := s.createDepositRecordInTx(ctx, tx, merchantId, addressInfo, txHash, chain, token, amount, int64(walletResult.TransactionID))
		if err != nil {
			return gerror.Wrap(err, "创建充值记录失败")
		}

		// 创建回调记录
		err = s.createDepositCallbackRecordInTx(ctx, tx, depositId, merchantId, amount, tokenSymbol, txHash)
		if err != nil {
			// 回调记录创建失败不影响主流程，只记录日志
			g.Log().Errorf(ctx, "创建充值回调记录失败: %v, depositId: %d", err, depositId)
			// 不返回错误，允许事务继续提交
		}

		g.Log().Infof(ctx, "充值处理成功: merchantId=%d, amount=%s %s, txHash=%s, walletTransactionId=%d, depositId=%d",
			merchantId, amount, tokenSymbol, txHash, walletResult.TransactionID, depositId)

		return nil
	})
}

// GetDepositAddress 获取充值地址
func (s *sDepositService) GetDepositAddress(ctx context.Context, merchantId int64, req *v1.DepositAddressReq) (*v1.DepositAddressData, error) {
	// 1. 映射链类型 API参数 -> 数据库字段
	chainType := s.mapChainType(req.Chain)

	// 2. 优先查询是否已为该用户绑定过地址
	existingAddress, err := s.findExistingMerchantAddress(ctx, merchantId, req.UserLabel, chainType, req.Token)
	if err != nil {
		return nil, err
	}

	if existingAddress != nil {
		// 返回已存在的地址 (用户复用)
		return s.buildMerchantAddressResponse(existingAddress, req.Chain, req.Token, true), nil
	}

	// 3. 从预先准备的地址池中分配新地址并在事务中绑定
	address, err := s.allocateAndBindNewAddress(ctx, merchantId, req.UserLabel, chainType, req.Chain, req.Token)
	if err != nil {
		return nil, err
	}

	return s.buildMerchantAddressResponse(address, req.Chain, req.Token, false), nil
}

// mapChainType 映射API参数到数据库链类型
func (s *sDepositService) mapChainType(apiChain string) string {
	switch strings.ToUpper(apiChain) {
	case "TRX", "TRC20":
		return "TRON"
	case "ETH", "ERC20":
		return "ETH"
	default:
		return apiChain // fallback
	}
}

// findExistingMerchantAddress 查找已存在的地址绑定 (基于merchant_id + lable + chan + name)
func (s *sDepositService) findExistingMerchantAddress(ctx context.Context, merchantId int64, userLabel, chainType, tokenType string) (*entity.MerchantAddress, error) {
	var address *entity.MerchantAddress
	err := dao.MerchantAddress.Ctx(ctx).
		Where(dao.MerchantAddress.Columns().MerchantId, merchantId).
		Where(dao.MerchantAddress.Columns().Lable, userLabel).
		Where(dao.MerchantAddress.Columns().Chan, chainType).
		Where(dao.MerchantAddress.Columns().Name, tokenType).
		Scan(&address)

	if err != nil {
		// 不暴露SQL错误细节，记录到日志并返回友好错误信息
		g.Log().Errorf(ctx, "查询已存在地址失败 - MerchantId: %d, UserLabel: %s, ChainType: %s, TokenType: %s, Error: %v", 
			merchantId, userLabel, chainType, tokenType, err)
		return nil, gerror.New("查询地址失败")
	}

	return address, nil
}

// allocateAndBindNewAddress 从预先准备的地址池中分配新地址并绑定到merchant_address表
func (s *sDepositService) allocateAndBindNewAddress(ctx context.Context, merchantId int64, userLabel, chainType, apiChain, token string) (*entity.MerchantAddress, error) {
	var result *entity.MerchantAddress
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 从预先准备的地址池中查找未绑定的地址
		var availableAddr *entity.Address
		err := dao.Address.Ctx(ctx).TX(tx).
			Where(dao.Address.Columns().Chan, chainType). // 使用映射后的链类型 (TRON/ETH)
			Where(dao.Address.Columns().BindStatus, 0).   // 0表示未绑定
			OrderAsc(dao.Address.Columns().Id).
			Limit(1).
			Scan(&availableAddr)

		if err != nil {
			g.Log().Errorf(ctx, "查询可用地址失败 - ChainType: %s, Error: %v", chainType, err)
			return gerror.New("查询可用地址失败")
		}

		if availableAddr == nil {
			return gerror.Newf("暂无可用的%s链地址，请联系管理员补充地址池", chainType)
		}

		// 2. 更新地址池中的绑定状态
		_, err = dao.Address.Ctx(ctx).TX(tx).
			Where(dao.Address.Columns().Id, availableAddr.Id).
			Update(g.Map{
				dao.Address.Columns().BindStatus: 1, // 1表示已绑定
				dao.Address.Columns().UpdatedAt:  gtime.Now(),
			})

		if err != nil {
			return gerror.Wrap(err, "更新地址绑定状态失败")
		}

		// 3. 生成二维码
		qrCodeBase64, qrCodeUrl, err := s.generateAndUploadQRCode(ctx, availableAddr.Address)
		if err != nil {
			return gerror.Wrap(err, "生成或上传二维码失败")
		}

		// 4. 创建商户地址绑定记录到 merchant_address 表
		insertResult, err := dao.MerchantAddress.Ctx(ctx).TX(tx).Insert(g.Map{
			dao.MerchantAddress.Columns().MerchantId: merchantId,
			dao.MerchantAddress.Columns().Lable:      userLabel,
			dao.MerchantAddress.Columns().Chan:       chainType, // 存储映射后的链类型 (TRON/ETH)
			dao.MerchantAddress.Columns().Name:       token,     // 代币类型
			dao.MerchantAddress.Columns().Address:    availableAddr.Address,
			dao.MerchantAddress.Columns().Image:      qrCodeBase64, // 二维码Base64
			"qr_url": qrCodeUrl, // 二维码S3 URL
			dao.MerchantAddress.Columns().Type:       "deposit",    // 类型为充值
			dao.MerchantAddress.Columns().CreatedAt:  gtime.Now(),
			dao.MerchantAddress.Columns().UpdatedAt:  gtime.Now(),
		})

		if err != nil {
			return gerror.Wrap(err, "创建地址绑定记录失败")
		}

		// 5. 获取创建的记录
		id, err := insertResult.LastInsertId()
		if err != nil {
			g.Log().Errorf(ctx, "获取插入ID失败 - Error: %v", err)
			return gerror.New("获取插入ID失败")
		}

		var newAddress *entity.MerchantAddress
		err = dao.MerchantAddress.Ctx(ctx).TX(tx).
			Where(dao.MerchantAddress.Columns().AddressId, id).
			Scan(&newAddress)

		if err != nil {
			g.Log().Errorf(ctx, "获取新创建的地址记录失败 - AddressId: %d, Error: %v", id, err)
			return gerror.New("获取新创建的地址记录失败")
		}

		result = newAddress
		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// buildMerchantAddressResponse 构建地址响应数据
func (s *sDepositService) buildMerchantAddressResponse(address *entity.MerchantAddress, apiChain, token string, isReused bool) *v1.DepositAddressData {
	// 如果是重用的地址，且没有二维码，需要重新生成
	qrCodeBase64 := address.Image
	qrCodeUrl := address.QrUrl // 读取现有的S3 URL
	
	// 检查是否需要生成二维码
	if qrCodeBase64 == "" && address.Address != "" {
		// 重新生成二维码
		newQrCodeBase64, newQrCodeUrl, err := s.generateAndUploadQRCode(context.Background(), address.Address)
		if err == nil {
			qrCodeBase64 = newQrCodeBase64
			qrCodeUrl = newQrCodeUrl
			// 可选：更新数据库中的二维码信息
		}
	}
	
	return &v1.DepositAddressData{
		AddressInfo: v1.AddressInfo{
			Address:   address.Address,
			QrCode:    qrCodeBase64, // Base64编码的二维码
			QrCodeUrl: qrCodeUrl,    // S3 URL
		},
		UserLabel: address.Lable, // 使用正确的字段名
		Chain:     apiChain,      // 返回API请求中的原始链类型
		Token:     token,         // 返回API请求中的代币类型
		CreatedAt: address.CreatedAt.String(),
		IsReused:  isReused,
	}
}

// generateAndUploadQRCode 生成二维码并上传到S3
func (s *sDepositService) generateAndUploadQRCode(ctx context.Context, address string) (string, string, error) {
	// 生成二维码Base64
	qrCodeBase64, err := utility.GenerateQRCodeBase64(address, 256)
	if err != nil {
		return "", "", gerror.Wrap(err, "生成二维码失败")
	}

	// 生成二维码PNG数据用于上传
	qrCodePNG, err := utility.GenerateQRCode(address, 256)
	if err != nil {
		return "", "", gerror.Wrap(err, "生成二维码PNG失败")
	}

	// 创建S3客户端
	s3Client, err := utility.NewS3Client(ctx)
	if err != nil {
		// 如果S3配置失败，仍然返回Base64，但URL为空
		g.Log().Warningf(ctx, "创建S3客户端失败: %v，将只返回Base64二维码", err)
		return qrCodeBase64, "", nil
	}

	// 生成唯一的文件名
	fileName := fmt.Sprintf("qrcodes/deposit/%s/%s.png", gtime.Now().Format("2006/01/02"), address)

	// 上传到S3
	qrCodeUrl, err := s3Client.UploadFile(ctx, fileName, qrCodePNG, "image/png")
	if err != nil {
		// 如果上传失败，仍然返回Base64，但URL为空
		g.Log().Warningf(ctx, "上传二维码到S3失败: %v，将只返回Base64二维码", err)
		return qrCodeBase64, "", nil
	}

	return qrCodeBase64, qrCodeUrl, nil
}

// QueryDeposits 查询充值记录
func (s *sDepositService) QueryDeposits(ctx context.Context, merchantId int64, req *v1.DepositQueryReq) (*v1.PageResponse, error) {
	// 构建查询条件
	query := dao.MerchantDeposits.Ctx(ctx).Where(dao.MerchantDeposits.Columns().MerchantId, merchantId)

	// 添加筛选条件
	if req.UserLabel != "" {
		// 需要通过merchant_address表关联查询
		query = query.LeftJoin("merchant_address ma", "ma.address = merchant_deposits.to_address AND ma.merchant_id = merchant_deposits.merchant_id").
			Where("ma.lable", req.UserLabel)
	}

	if req.Address != "" {
		query = query.Where(dao.MerchantDeposits.Columns().ToAddress, req.Address)
	}

	if req.Chain != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Chan, req.Chain)
	}

	if req.Token != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Name, req.Token)
	}

	if req.Status != "" {
		// 状态映射：pending->1, confirmed->2, failed->3
		statusMap := map[string]string{
			"pending":   "1",
			"confirmed": "2",
			"failed":    "3",
		}
		if dbStatus, ok := statusMap[req.Status]; ok {
			query = query.Where(dao.MerchantDeposits.Columns().State, dbStatus)
		}
	}

	if req.TxHash != "" {
		query = query.Where(dao.MerchantDeposits.Columns().TxHash, req.TxHash)
	}

	if req.StartTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" >= ?", req.StartTime)
	}

	if req.EndTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	if req.MinAmount != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Amount+" >= ?", req.MinAmount)
	}

	if req.MaxAmount != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Amount+" <= ?", req.MaxAmount)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值记录总数失败")
	}

	// 分页查询 - 明确指定SELECT字段避免歧义
	offset := (req.Page - 1) * req.PageSize
	var deposits []*entity.MerchantDeposits

	// 构建明确的字段列表，避免JOIN时的字段歧义
	selectFields := []string{
		"merchant_deposits.recharges_id",
		"merchant_deposits.merchant_id",
		"merchant_deposits.token_id",
		"merchant_deposits.name",
		"merchant_deposits.chan",
		"merchant_deposits.token_contract_address",
		"merchant_deposits.from_address",
		"merchant_deposits.to_address",
		"merchant_deposits.tx_hash",
		"merchant_deposits.error",
		"merchant_deposits.amount",
		"merchant_deposits.state",
		"merchant_deposits.failure_reason",
		"merchant_deposits.confirmations",
		"merchant_deposits.created_at",
		"merchant_deposits.completed_at",
		"merchant_deposits.notification_sent",
		"merchant_deposits.notification_sent_at",
		"merchant_deposits.updated_at",
	}

	err = query.Fields(strings.Join(selectFields, ",")).
		OrderDesc("merchant_deposits.created_at").
		Limit(req.PageSize).
		Offset(offset).
		Scan(&deposits)

	if err != nil {
		return nil, gerror.Wrap(err, "查询充值记录失败")
	}

	// 转换为API响应格式
	records := make([]*v1.DepositRecord, len(deposits))
	for i, deposit := range deposits {
		records[i] = s.convertDepositToRecord(deposit)
	}

	return &v1.PageResponse{
		Total:    int64(total),
		Page:     req.Page,
		PageSize: req.PageSize,
		Data:     records,
	}, nil
}

// convertDepositToRecord 转换充值记录格式
func (s *sDepositService) convertDepositToRecord(deposit *entity.MerchantDeposits) *v1.DepositRecord {
	// 状态映射
	statusMap := map[string]string{
		"1": "pending",
		"2": "confirmed",
		"3": "failed",
	}

	status := statusMap[gconv.String(deposit.State)]
	if status == "" {
		status = "pending"
	}

	return &v1.DepositRecord{
		ID:         gconv.Int64(deposit.RechargesId),
		MerchantID: gconv.Int64(deposit.MerchantId),
		Address:    gconv.String(deposit.ToAddress),
		Chain:      gconv.String(deposit.Chan),
		Token:      gconv.String(deposit.Name),
		Amount:     gconv.String(deposit.Amount),
		TxHash:     gconv.String(deposit.TxHash),
		Status:     status,
		CreatedAt:  deposit.CreatedAt.String(),
		UpdatedAt:  deposit.UpdatedAt.String(),
	}
}

// GetDepositDetail 获取充值详情
func (s *sDepositService) GetDepositDetail(ctx context.Context, merchantId int64, req *v1.DepositDetailReq) (*v1.DepositRecord, error) {
	query := dao.MerchantDeposits.Ctx(ctx).Where(dao.MerchantDeposits.Columns().MerchantId, merchantId)

	if req.ID > 0 {
		query = query.Where(dao.MerchantDeposits.Columns().RechargesId, req.ID)
	} else if req.TxHash != "" {
		query = query.Where(dao.MerchantDeposits.Columns().TxHash, req.TxHash)
	} else {
		return nil, gerror.New("请提供充值记录ID或交易哈希")
	}

	var deposit *entity.MerchantDeposits
	err := query.Scan(&deposit)
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值详情失败")
	}

	if deposit == nil {
		return nil, gerror.New("充值记录不存在")
	}

	return s.convertDepositToRecord(deposit), nil
}

// GetAddressList 获取地址列表
func (s *sDepositService) GetAddressList(ctx context.Context, merchantId int64, req *v1.DepositAddressListReq) (*v1.PageResponse, error) {
	query := dao.MerchantAddress.Ctx(ctx).Where(dao.MerchantAddress.Columns().MerchantId, merchantId)

	// 添加筛选条件
	if req.UserLabel != "" {
		query = query.Where(dao.MerchantAddress.Columns().Lable, req.UserLabel)
	}

	if req.Chain != "" {
		// 映射API链类型参数到数据库存储值
		chainType := s.mapChainType(req.Chain)
		query = query.Where(dao.MerchantAddress.Columns().Chan, chainType)
	}

	if req.Token != "" {
		query = query.Where(dao.MerchantAddress.Columns().Name, req.Token)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "查询地址总数失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	var addresses []*entity.MerchantAddress
	err = query.OrderDesc(dao.MerchantAddress.Columns().CreatedAt).
		Limit(req.PageSize).
		Offset(offset).
		Scan(&addresses)

	if err != nil {
		return nil, gerror.Wrap(err, "查询地址列表失败")
	}

	// 转换为API响应格式
	records := make([]*v1.MerchantAddress, len(addresses))
	for i, addr := range addresses {
		records[i] = &v1.MerchantAddress{
			ID:         gconv.Int64(addr.AddressId),
			MerchantID: gconv.Int64(addr.MerchantId),
			UserLabel:  addr.Lable, // 使用正确的字段名
			Address:    addr.Address,
			Chain:      addr.Chan, // 使用正确的字段名
			Token:      addr.Name, // 使用正确的字段名
			Status:     "active",  // 简化状态处理
			CreatedAt:  addr.CreatedAt.String(),
			UpdatedAt:  addr.UpdatedAt.String(),
		}
	}

	return &v1.PageResponse{
		Total:    int64(total),
		Page:     req.Page,
		PageSize: req.PageSize,
		Data:     records,
	}, nil
}

// checkDepositExists 检查充值记录是否已存在
func (s *sDepositService) checkDepositExists(ctx context.Context, txHash string) (bool, error) {
	count, err := dao.MerchantDeposits.Ctx(ctx).Where("tx_hash", txHash).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// validateDepositAddress 验证充值地址是否属于该商户
func (s *sDepositService) validateDepositAddress(ctx context.Context, merchantId int64, address, chain, token string) (*entity.MerchantAddress, error) {
	chainType := s.mapChainType(chain)

	var addressInfo *entity.MerchantAddress
	err := dao.MerchantAddress.Ctx(ctx).
		Where("merchant_id", merchantId).
		Where("address", address).
		Where("chan", chainType).
		Where("name", token).
		Scan(&addressInfo)

	if err != nil {
		return nil, err
	}

	if addressInfo == nil {
		return nil, gerror.New("充值地址不属于该商户")
	}

	return addressInfo, nil
}

// mapChainAndToken 映射链类型和代币符号（复用withdraw中的逻辑）
func (s *sDepositService) mapChainAndToken(apiChain, apiToken string) (chainType, tokenSymbol string) {
	switch strings.ToUpper(apiChain) {
	case "TRX":
		return "TRON", "TRX"
	case "TRC20":
		return "TRON", strings.ToUpper(apiToken)
	case "ETH":
		return "ETH", "ETH"
	case "ERC20":
		return "ETH", strings.ToUpper(apiToken)
	default:
		return strings.ToUpper(apiChain), strings.ToUpper(apiToken)
	}
}

// createDepositRecord 创建充值记录（兼容旧版本）
func (s *sDepositService) createDepositRecord(ctx context.Context, merchantId int64, addressInfo *entity.MerchantAddress, txHash, chain, token, amount string, walletTransactionId int64) error {
	_, err := s.createDepositRecordInTx(ctx, nil, merchantId, addressInfo, txHash, chain, token, amount, walletTransactionId)
	return err
}

// createDepositRecordInTx 在事务中创建充值记录
func (s *sDepositService) createDepositRecordInTx(ctx context.Context, tx gdb.TX, merchantId int64, addressInfo *entity.MerchantAddress, txHash, chain, token, amount string, walletTransactionId int64) (uint64, error) {
	insertData := g.Map{
		"merchant_id":           merchantId,
		"to_address":            addressInfo.Address,
		"chan":                  chain,
		"name":                  token,
		"amount":                amount,
		"tx_hash":               txHash,
		"state":                 "2",                 // 2-已确认
		"wallet_transaction_id": walletTransactionId, // 关联钱包交易ID
		"created_at":            gtime.Now(),
		"updated_at":            gtime.Now(),
	}

	var insertId int64
	var err error

	if tx != nil {
		insertId, err = dao.MerchantDeposits.Ctx(ctx).TX(tx).InsertAndGetId(insertData)
	} else {
		insertId, err = dao.MerchantDeposits.Ctx(ctx).InsertAndGetId(insertData)
	}

	if err != nil {
		return 0, err
	}

	return uint64(insertId), nil
}

// createDepositCallbackRecordInTx 在事务中创建充值回调记录
func (s *sDepositService) createDepositCallbackRecordInTx(ctx context.Context, tx gdb.TX, depositId uint64, merchantId int64, amount, currency, txHash string) error {
	// 1. 查询商户回调URL
	var merchant gdb.Record
	var err error

	if tx != nil {
		err = dao.Merchants.Ctx(ctx).TX(tx).
			Where("merchant_id", merchantId).
			Where("deleted_at IS NULL").
			Scan(&merchant)
	} else {
		err = dao.Merchants.Ctx(ctx).
			Where("merchant_id", merchantId).
			Where("deleted_at IS NULL").
			Scan(&merchant)
	}

	if err != nil {
		return gerror.Wrap(err, "查询商户信息失败")
	}

	if merchant == nil {
		return gerror.New("商户不存在")
	}

	callbackUrl := merchant["callback_url"].String()
	if callbackUrl == "" {
		// 没有配置回调URL，直接返回
		g.Log().Infof(ctx, "商户未配置回调URL，跳过回调记录创建: merchantId=%d", merchantId)
		return nil
	}

	// 2. 构建回调数据
	callbackData := g.Map{
		"event_type":    "deposit_confirmed",
		"order_no":      fmt.Sprintf("DP%d", depositId),
		"merchant_id":   merchantId,
		"amount":        amount,
		"currency":      currency,
		"tx_hash":       txHash,
		"confirmations": 12,
		"completed_at":  gtime.Now().Format("c"),
		"timestamp":     time.Now().Unix(),
	}

	payload, err := json.Marshal(callbackData)
	if err != nil {
		return gerror.Wrap(err, "序列化回调数据失败")
	}

	// 3. 插入回调记录
	insertData := g.Map{
		"merchant_id":   merchantId,
		"callback_type": constants.CallbackEventDepositConfirmed,
		"related_id":    depositId,
		"callback_url":  callbackUrl,
		"payload":       string(payload),
		"status":        "pending",
		"retry_count":   0,
		"created_at":    gtime.Now(),
		"updated_at":    gtime.Now(),
	}

	if tx != nil {
		_, err = dao.MerchantCallbacks.Ctx(ctx).TX(tx).Insert(insertData)
	} else {
		_, err = dao.MerchantCallbacks.Ctx(ctx).Insert(insertData)
	}

	if err != nil {
		return gerror.Wrap(err, "创建回调记录失败")
	}

	g.Log().Infof(ctx, "成功创建充值回调记录: depositId=%d, merchantId=%d, callbackUrl=%s",
		depositId, merchantId, callbackUrl)

	return nil
}
