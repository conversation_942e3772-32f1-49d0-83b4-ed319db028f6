package merchant

import (
	"context"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"merchant-server/internal/dao"
)

// MonitorService 监控服务
type MonitorService struct {
	stats *SystemStats
	mutex sync.RWMutex
}

// SystemStats 系统统计信息
type SystemStats struct {
	APICallCount     int64     `json:"api_call_count"`     // API调用总数
	SuccessCallCount int64     `json:"success_call_count"` // 成功调用数
	ErrorCallCount   int64     `json:"error_call_count"`   // 错误调用数
	AvgResponseTime  float64   `json:"avg_response_time"`  // 平均响应时间(ms)
	ActiveMerchants  int64     `json:"active_merchants"`   // 活跃商户数
	TotalDeposits    int64     `json:"total_deposits"`     // 总充值笔数
	TotalWithdraws   int64     `json:"total_withdraws"`    // 总提现笔数
	PendingCallbacks int64     `json:"pending_callbacks"`  // 待处理回调数
	FailedCallbacks  int64     `json:"failed_callbacks"`   // 失败回调数
	LastUpdateTime   time.Time `json:"last_update_time"`   // 最后更新时间
}

// APIMetrics API指标
type APIMetrics struct {
	Endpoint     string  `json:"endpoint"`      // 接口路径
	CallCount    int64   `json:"call_count"`    // 调用次数
	SuccessCount int64   `json:"success_count"` // 成功次数
	ErrorCount   int64   `json:"error_count"`   // 错误次数
	AvgTime      float64 `json:"avg_time"`      // 平均响应时间
	MaxTime      int64   `json:"max_time"`      // 最大响应时间
	MinTime      int64   `json:"min_time"`      // 最小响应时间
}

// MerchantMetrics 商户指标
type MerchantMetrics struct {
	MerchantID      int64   `json:"merchant_id"`       // 商户ID
	APICallCount    int64   `json:"api_call_count"`    // API调用次数
	DepositCount    int64   `json:"deposit_count"`     // 充值次数
	WithdrawCount   int64   `json:"withdraw_count"`    // 提现次数
	SuccessRate     float64 `json:"success_rate"`      // 成功率
	AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
	LastActiveTime  string  `json:"last_active_time"`  // 最后活跃时间
}

var (
	monitorService *MonitorService
	once           sync.Once
)

// GetMonitorService 获取监控服务实例
func GetMonitorService() *MonitorService {
	once.Do(func() {
		monitorService = &MonitorService{
			stats: &SystemStats{
				LastUpdateTime: time.Now(),
			},
		}
	})
	return monitorService
}

// UpdateStats 更新系统统计信息
func (m *MonitorService) UpdateStats(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 查询API调用统计
	apiStats, err := m.getAPIStats(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取API统计失败:", err)
	} else {
		m.stats.APICallCount = apiStats["total_calls"].(int64)
		m.stats.SuccessCallCount = apiStats["success_calls"].(int64)
		m.stats.ErrorCallCount = apiStats["error_calls"].(int64)
		m.stats.AvgResponseTime = apiStats["avg_response_time"].(float64)
	}

	// 查询商户统计
	merchantStats, err := m.getMerchantStats(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取商户统计失败:", err)
	} else {
		m.stats.ActiveMerchants = merchantStats["active_merchants"].(int64)
	}

	// 查询交易统计
	transactionStats, err := m.getTransactionStats(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取交易统计失败:", err)
	} else {
		m.stats.TotalDeposits = transactionStats["total_deposits"].(int64)
		m.stats.TotalWithdraws = transactionStats["total_withdraws"].(int64)
	}

	// 查询回调统计
	callbackStats, err := m.getCallbackStats(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取回调统计失败:", err)
	} else {
		m.stats.PendingCallbacks = callbackStats["pending_callbacks"].(int64)
		m.stats.FailedCallbacks = callbackStats["failed_callbacks"].(int64)
	}

	m.stats.LastUpdateTime = time.Now()
	return nil
}

// GetSystemStats 获取系统统计信息
func (m *MonitorService) GetSystemStats() *SystemStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 返回副本
	stats := *m.stats
	return &stats
}

// getAPIStats 获取API统计信息
func (m *MonitorService) getAPIStats(ctx context.Context) (map[string]interface{}, error) {
	// 查询今日API调用统计
	today := gtime.Now().Format("2006-01-02")

	totalCalls, err := dao.OperationLog.Ctx(ctx).
		Where("operation_type = ? AND DATE(created_at) = ?", "api_call", today).
		Count()
	if err != nil {
		return nil, err
	}

	// 查询成功调用数（假设响应状态码200为成功）
	successCalls, err := dao.OperationLog.Ctx(ctx).
		Where("operation_type = ? AND DATE(created_at) = ? AND response_data LIKE ?", "api_call", today, "%\"status_code\":200%").
		Count()
	if err != nil {
		return nil, err
	}

	errorCalls := totalCalls - successCalls

	// 查询平均响应时间
	var avgResponseTime float64
	result, err := dao.OperationLog.Ctx(ctx).
		Where("operation_type = ? AND DATE(created_at) = ? AND duration > 0", "api_call", today).
		Avg("duration")
	if err == nil {
		avgResponseTime = result
	}

	return map[string]interface{}{
		"total_calls":       totalCalls,
		"success_calls":     successCalls,
		"error_calls":       errorCalls,
		"avg_response_time": avgResponseTime,
	}, nil
}

// getMerchantStats 获取商户统计信息
func (m *MonitorService) getMerchantStats(ctx context.Context) (map[string]interface{}, error) {
	// 查询活跃商户数（今日有API调用的商户）
	today := gtime.Now().Format("2006-01-02")

	activeMerchants, err := dao.OperationLog.Ctx(ctx).
		Where("operation_type = ? AND DATE(created_at) = ? AND merchant_id > 0", "api_call", today).
		Group("merchant_id").
		Count()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"active_merchants": activeMerchants,
	}, nil
}

// getTransactionStats 获取交易统计信息
func (m *MonitorService) getTransactionStats(ctx context.Context) (map[string]interface{}, error) {
	today := gtime.Now().Format("2006-01-02")

	// 查询今日充值笔数
	totalDeposits, err := dao.MerchantDeposits.Ctx(ctx).
		Where("DATE(created_at) = ?", today).
		Count()
	if err != nil {
		return nil, err
	}

	// 查询今日提现笔数
	totalWithdraws, err := dao.MerchantWithdraws.Ctx(ctx).
		Where("DATE(created_at) = ?", today).
		Count()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_deposits":  totalDeposits,
		"total_withdraws": totalWithdraws,
	}, nil
}

// getCallbackStats 获取回调统计信息
func (m *MonitorService) getCallbackStats(ctx context.Context) (map[string]interface{}, error) {
	// 查询待处理回调数
	pendingCallbacks, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("status = ?", "pending").
		Count()
	if err != nil {
		return nil, err
	}

	// 查询失败回调数
	failedCallbacks, err := dao.MerchantCallbacks.Ctx(ctx).
		Where("status = ?", "failed").
		Count()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"pending_callbacks": pendingCallbacks,
		"failed_callbacks":  failedCallbacks,
	}, nil
}

// GetAPIMetrics 获取API指标
func (m *MonitorService) GetAPIMetrics(ctx context.Context, timeRange string) ([]*APIMetrics, error) {
	// 根据时间范围构建查询条件（这里简化实现，实际项目中需要解析request_data中的URL）
	_ = timeRange // 暂时忽略时间范围参数

	// 返回模拟数据
	metrics := []*APIMetrics{
		{
			Endpoint:     "/merchant-server/v1/deposit/address",
			CallCount:    100,
			SuccessCount: 95,
			ErrorCount:   5,
			AvgTime:      150.5,
			MaxTime:      500,
			MinTime:      50,
		},
		{
			Endpoint:     "/merchant-server/v1/withdraw/create",
			CallCount:    80,
			SuccessCount: 75,
			ErrorCount:   5,
			AvgTime:      200.3,
			MaxTime:      800,
			MinTime:      100,
		},
	}

	return metrics, nil
}

// GetMerchantMetrics 获取商户指标
func (m *MonitorService) GetMerchantMetrics(ctx context.Context, merchantId int64, timeRange string) (*MerchantMetrics, error) {
	// 简化实现，使用今日数据
	today := gtime.Now().Format("2006-01-02")
	_ = timeRange // 暂时忽略时间范围参数

	// 查询API调用次数
	apiCallCount, _ := dao.OperationLog.Ctx(ctx).
		Where("merchant_id = ? AND operation_type = ? AND DATE(created_at) = ?", merchantId, "api_call", today).
		Count()

	// 查询充值次数
	depositCount, _ := dao.MerchantDeposits.Ctx(ctx).
		Where("merchant_id = ? AND DATE(created_at) = ?", merchantId, today).
		Count()

	// 查询提现次数
	withdrawCount, _ := dao.MerchantWithdraws.Ctx(ctx).
		Where("merchant_id = ? AND DATE(created_at) = ?", merchantId, today).
		Count()

	// 计算成功率（简化实现）
	successRate := 95.0 // 模拟数据

	// 查询平均响应时间
	avgResponseTime := 180.5 // 模拟数据

	return &MerchantMetrics{
		MerchantID:      merchantId,
		APICallCount:    int64(apiCallCount),
		DepositCount:    int64(depositCount),
		WithdrawCount:   int64(withdrawCount),
		SuccessRate:     successRate,
		AvgResponseTime: avgResponseTime,
		LastActiveTime:  gtime.Now().String(),
	}, nil
}

// StartMonitorWorker 启动监控工作器
func (m *MonitorService) StartMonitorWorker(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(1 * time.Minute) // 每分钟更新一次统计
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := m.UpdateStats(ctx); err != nil {
					g.Log().Error(ctx, "更新监控统计失败:", err)
				}
			}
		}
	}()
}
