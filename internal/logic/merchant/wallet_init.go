package merchant

import (
	"context"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"merchant-server/internal/wallet"
)

var (
	// 全局钱包服务实例
	globalWalletService *wallet.Service
	walletServiceOnce   sync.Once
)

// InitWalletService 初始化钱包服务
func InitWalletService() error {
	var err error
	walletServiceOnce.Do(func() {
		ctx := context.Background()
		
		// 创建钱包服务实例
		globalWalletService = wallet.NewService(
			wallet.WithDBGroup("default"),
			wallet.WithAudit(true, "info"),
			wallet.WithMaxConcurrent(500),
			wallet.WithConfigPath("manifest/config/wallet.yaml"),
		)
		
		g.Log().Info(ctx, "钱包服务初始化成功")
	})
	return err
}

// GetWalletService 获取全局钱包服务实例
func GetWalletService() *wallet.Service {
	if globalWalletService == nil {
		// 如果还没有初始化，则自动初始化
		if err := InitWalletService(); err != nil {
			g.Log().Fatalf(context.Background(), "钱包服务初始化失败: %v", err)
		}
	}
	return globalWalletService
}

// init 函数在包加载时自动初始化钱包服务
func init() {
	// 在包加载时自动初始化钱包服务
	if err := InitWalletService(); err != nil {
		g.Log().Errorf(context.Background(), "钱包服务自动初始化失败: %v", err)
	}
}
