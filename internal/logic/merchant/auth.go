package merchant

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"merchant-server/internal/dao"
	"merchant-server/internal/model/do"
	"merchant-server/internal/model/entity"
)

// AuthService 商户认证服务
type AuthService struct{}

// NewAuthService 创建认证服务实例
func NewAuthService() *AuthService {
	return &AuthService{}
}

// CreateAPIKey 创建API密钥
func (s *AuthService) CreateAPIKey(ctx context.Context, merchantId uint, label string, scopes []string, ipWhitelist string, expiresAt *time.Time) (*entity.MerchantApiKeys, error) {
	// 生成API Key和Secret
	apiKey := s.generateAPIKey()
	secret := s.generateSecret()
	secretHash := s.hashSecret(secret)

	// 构建数据
	data := &do.MerchantApiKeys{
		MerchantId:  merchantId,
		ApiKey:      apiKey,
		Secret:      secret, // 注意：实际项目中不应该存储明文secret
		SecretHash:  secretHash,
		Label:       label,
		Status:      "active",
		Scopes:      s.joinScopes(scopes),
		IpWhitelist: ipWhitelist,
		ExpiresAt: func() *gtime.Time {
			if expiresAt != nil {
				return gtime.NewFromTime(*expiresAt)
			}
			return nil
		}(),
		RateLimit:  1000,   // 默认每分钟1000次
		DailyLimit: 100000, // 默认每日10万次
		CreatedAt:  gtime.Now(),
		UpdatedAt:  gtime.Now(),
	}

	// 插入数据库
	result, err := dao.MerchantApiKeys.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get inserted ID: %w", err)
	}

	// 返回创建的API密钥信息
	var apiKeyInfo *entity.MerchantApiKeys
	err = dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, id).
		Scan(&apiKeyInfo)

	if err != nil {
		return nil, fmt.Errorf("failed to fetch created API key: %w", err)
	}

	return apiKeyInfo, nil
}

// RevokeAPIKey 撤销API密钥
func (s *AuthService) RevokeAPIKey(ctx context.Context, apiKeyId uint, merchantId uint) error {
	_, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Update(g.Map{
			dao.MerchantApiKeys.Columns().Status:    "revoked",
			dao.MerchantApiKeys.Columns().UpdatedAt: gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("failed to revoke API key: %w", err)
	}

	return nil
}

// GetAPIKeys 获取商户的API密钥列表
func (s *AuthService) GetAPIKeys(ctx context.Context, merchantId uint) ([]*entity.MerchantApiKeys, error) {
	var apiKeys []*entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Where(dao.MerchantApiKeys.Columns().DeletedAt + " IS NULL").
		OrderDesc(dao.MerchantApiKeys.Columns().CreatedAt).
		Scan(&apiKeys)

	if err != nil {
		return nil, fmt.Errorf("failed to get API keys: %w", err)
	}

	// 清除敏感信息
	for _, apiKey := range apiKeys {
		apiKey.Secret = "***"
		apiKey.SecretHash = "***"
	}

	return apiKeys, nil
}

// UpdateAPIKeyScopes 更新API密钥权限范围
func (s *AuthService) UpdateAPIKeyScopes(ctx context.Context, apiKeyId uint, merchantId uint, scopes []string) error {
	_, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Update(g.Map{
			dao.MerchantApiKeys.Columns().Scopes:    s.joinScopes(scopes),
			dao.MerchantApiKeys.Columns().UpdatedAt: gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("failed to update API key scopes: %w", err)
	}

	return nil
}

// UpdateAPIKeyIPWhitelist 更新API密钥IP白名单
func (s *AuthService) UpdateAPIKeyIPWhitelist(ctx context.Context, apiKeyId uint, merchantId uint, ipWhitelist string) error {
	_, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Update(g.Map{
			dao.MerchantApiKeys.Columns().IpWhitelist: ipWhitelist,
			dao.MerchantApiKeys.Columns().UpdatedAt:   gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("failed to update API key IP whitelist: %w", err)
	}

	return nil
}

// UpdateAPIKeyRateLimit 更新API密钥频率限制
func (s *AuthService) UpdateAPIKeyRateLimit(ctx context.Context, apiKeyId uint, merchantId uint, rateLimit, dailyLimit int) error {
	_, err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Update(g.Map{
			dao.MerchantApiKeys.Columns().RateLimit:  rateLimit,
			dao.MerchantApiKeys.Columns().DailyLimit: dailyLimit,
			dao.MerchantApiKeys.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("failed to update API key rate limit: %w", err)
	}

	return nil
}

// ValidateAPIKeyPermission 验证API密钥权限
func (s *AuthService) ValidateAPIKeyPermission(ctx context.Context, apiKey string, requiredScope string) (*entity.MerchantApiKeys, error) {
	var apiKeyInfo *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKey, apiKey).
		Where(dao.MerchantApiKeys.Columns().Status, "active").
		Where(dao.MerchantApiKeys.Columns().DeletedAt + " IS NULL").
		Scan(&apiKeyInfo)

	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	if apiKeyInfo == nil {
		return nil, fmt.Errorf("API key not found or inactive")
	}

	// 检查过期时间
	if apiKeyInfo.ExpiresAt != nil && apiKeyInfo.ExpiresAt.Before(gtime.Now()) {
		return nil, fmt.Errorf("API key expired")
	}

	// 检查权限范围
	if !s.hasScope(apiKeyInfo.Scopes, requiredScope) {
		return nil, fmt.Errorf("insufficient permissions")
	}

	return apiKeyInfo, nil
}

// GetAPIKeyUsageStats 获取API密钥使用统计
func (s *AuthService) GetAPIKeyUsageStats(ctx context.Context, apiKeyId uint, merchantId uint) (map[string]interface{}, error) {
	// 这里可以从日志表或Redis中获取使用统计
	// 暂时返回基本信息

	var apiKeyInfo *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().ApiKeyId, apiKeyId).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantId).
		Scan(&apiKeyInfo)

	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	if apiKeyInfo == nil {
		return nil, fmt.Errorf("API key not found")
	}

	stats := map[string]interface{}{
		"api_key_id":   apiKeyInfo.ApiKeyId,
		"label":        apiKeyInfo.Label,
		"status":       apiKeyInfo.Status,
		"last_used_at": apiKeyInfo.LastUsedAt,
		"created_at":   apiKeyInfo.CreatedAt,
		"rate_limit":   apiKeyInfo.RateLimit,
		"daily_limit":  apiKeyInfo.DailyLimit,
		// TODO: 添加实际使用统计
		"today_calls": 0,
		"total_calls": 0,
	}

	return stats, nil
}

// 私有方法

// generateAPIKey 生成API Key
func (s *AuthService) generateAPIKey() string {
	// 生成32字节随机数据
	bytes := make([]byte, 32)
	rand.Read(bytes)

	// 使用时间戳和随机数生成API Key
	timestamp := time.Now().Unix()
	data := fmt.Sprintf("%d_%s", timestamp, hex.EncodeToString(bytes))

	// 计算MD5哈希
	hash := gmd5.MustEncrypt(data)

	// 格式化为API Key格式
	return fmt.Sprintf("mk_%s", hash[:24])
}

// generateSecret 生成Secret
func (s *AuthService) generateSecret() string {
	// 生成64字节随机数据
	bytes := make([]byte, 64)
	rand.Read(bytes)

	return hex.EncodeToString(bytes)
}

// hashSecret 计算Secret的哈希值
func (s *AuthService) hashSecret(secret string) string {
	hash := sha256.Sum256([]byte(secret))
	return hex.EncodeToString(hash[:])
}

// joinScopes 连接权限范围
func (s *AuthService) joinScopes(scopes []string) string {
	if len(scopes) == 0 {
		return ""
	}

	result := ""
	for i, scope := range scopes {
		if i > 0 {
			result += ","
		}
		result += scope
	}

	return result
}

// hasScope 检查是否有指定权限
func (s *AuthService) hasScope(scopes, requiredScope string) bool {
	if scopes == "" {
		return false
	}

	if scopes == "all" {
		return true
	}

	scopeList := strings.Split(scopes, ",")
	for _, scope := range scopeList {
		scope = strings.TrimSpace(scope)
		if scope == requiredScope || scope == "all" {
			return true
		}
	}

	return false
}
