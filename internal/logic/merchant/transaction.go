package merchant

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
	"merchant-server/internal/service"
)

type sTransactionService struct{}

func init() {
	service.RegisterTransactionService(NewTransactionService())
}

func NewTransactionService() *sTransactionService {
	return &sTransactionService{}
}

// mapChainAndToken 映射API参数到数据库存储格式（复用withdraw中的逻辑）
func (s *sTransactionService) mapChainAndToken(apiChain, apiToken string) (chainType, tokenSymbol string) {
	switch strings.ToUpper(apiChain) {
	case "TRX":
		return "TRON", "TRX"
	case "TRC20":
		// TRC20代币，使用传入的token作为符号，通常是USDT
		symbol := strings.ToUpper(apiToken)
		if symbol == "NATIVE" {
			symbol = "USDT" // TRC20默认是USDT
		}
		return "TRON", symbol
	case "ETH":
		return "ETH", "ETH"
	case "ERC20":
		// ERC20代币，使用传入的token作为符号，通常是USDT
		symbol := strings.ToUpper(apiToken)
		if symbol == "NATIVE" {
			symbol = "USDT" // ERC20默认是USDT
		}
		return "ETH", symbol
	default:
		// fallback: 直接使用原值
		return apiChain, apiToken
	}
}

// QueryTransactions 查询交易记录
func (s *sTransactionService) QueryTransactions(ctx context.Context, merchantId int64, req *v1.TransactionQueryReq) (*v1.PageResponse, error) {
	var records []*v1.TransactionRecord
	var total int64

	if req.Type == "" || req.Type == "deposit" {
		// 查询充值记录
		depositRecords, depositTotal, err := s.queryDepositTransactions(ctx, merchantId, req)
		if err != nil {
			return nil, err
		}
		records = append(records, depositRecords...)
		total += depositTotal
	}

	if req.Type == "" || req.Type == "withdraw" {
		// 查询提现记录
		withdrawRecords, withdrawTotal, err := s.queryWithdrawTransactions(ctx, merchantId, req)
		if err != nil {
			return nil, err
		}
		records = append(records, withdrawRecords...)
		total += withdrawTotal
	}

	// 如果没有指定类型，需要合并排序和分页
	if req.Type == "" {
		// 简化实现：这里应该按时间排序并分页
		// 实际项目中可能需要更复杂的联合查询
		start := (req.Page - 1) * req.PageSize
		end := start + req.PageSize
		if start > len(records) {
			records = []*v1.TransactionRecord{}
		} else if end > len(records) {
			records = records[start:]
		} else {
			records = records[start:end]
		}
	}

	return &v1.PageResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Data:     records,
	}, nil
}

// queryDepositTransactions 查询充值交易记录
func (s *sTransactionService) queryDepositTransactions(ctx context.Context, merchantId int64, req *v1.TransactionQueryReq) ([]*v1.TransactionRecord, int64, error) {
	query := dao.MerchantDeposits.Ctx(ctx).Where(dao.MerchantDeposits.Columns().MerchantId, merchantId)

	// 添加筛选条件
	if req.Address != "" {
		query = query.Where(dao.MerchantDeposits.Columns().ToAddress, req.Address)
	}

	if req.Chain != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Chan, req.Chain)
	}

	if req.Token != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Name, req.Token)
	}

	if req.Status != "" {
		statusMap := map[string]string{
			"pending":   "1",
			"confirmed": "2",
			"failed":    "3",
		}
		if dbStatus, ok := statusMap[req.Status]; ok {
			query = query.Where(dao.MerchantDeposits.Columns().State, dbStatus)
		}
	}

	if req.TxHash != "" {
		query = query.Where(dao.MerchantDeposits.Columns().TxHash, req.TxHash)
	}

	if req.StartTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" >= ?", req.StartTime)
	}

	if req.EndTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	if req.MinAmount != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Amount+" >= ?", req.MinAmount)
	}

	if req.MaxAmount != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Amount+" <= ?", req.MaxAmount)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询充值记录总数失败")
	}

	// 如果指定了类型为充值，则分页查询
	if req.Type == "deposit" {
		offset := (req.Page - 1) * req.PageSize
		query = query.Limit(req.PageSize).Offset(offset)
	}

	var deposits []*entity.MerchantDeposits
	err = query.OrderDesc(dao.MerchantDeposits.Columns().CreatedAt).Scan(&deposits)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询充值记录失败")
	}

	// 转换为交易记录格式
	records := make([]*v1.TransactionRecord, len(deposits))
	for i, deposit := range deposits {
		records[i] = s.convertDepositToTransaction(deposit)
	}

	return records, int64(total), nil
}

// queryWithdrawTransactions 查询提现交易记录
func (s *sTransactionService) queryWithdrawTransactions(ctx context.Context, merchantId int64, req *v1.TransactionQueryReq) ([]*v1.TransactionRecord, int64, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	// 添加筛选条件
	if req.OrderID != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().OrderNo, req.OrderID)
	}

	if req.Address != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Address, req.Address)
	}

	if req.Chain != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Chan, req.Chain)
	}

	if req.Token != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Name, req.Token)
	}

	if req.Status != "" {
		statusMap := map[string]string{
			"pending":    "1",
			"processing": "2",
			"completed":  "4",
			"failed":     "5",
			"cancelled":  "3",
		}
		if dbStatus, ok := statusMap[req.Status]; ok {
			query = query.Where(dao.MerchantWithdraws.Columns().State, dbStatus)
		}
	}

	if req.TxHash != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().TxHash, req.TxHash)
	}

	if req.StartTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" >= ?", req.StartTime)
	}

	if req.EndTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	if req.MinAmount != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Amount+" >= ?", req.MinAmount)
	}

	if req.MaxAmount != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Amount+" <= ?", req.MaxAmount)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询提现记录总数失败")
	}

	// 如果指定了类型为提现，则分页查询
	if req.Type == "withdraw" {
		offset := (req.Page - 1) * req.PageSize
		query = query.Limit(req.PageSize).Offset(offset)
	}

	var withdraws []*entity.MerchantWithdraws
	err = query.OrderDesc(dao.MerchantWithdraws.Columns().CreatedAt).Scan(&withdraws)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询提现记录失败")
	}

	// 转换为交易记录格式
	records := make([]*v1.TransactionRecord, len(withdraws))
	for i, withdraw := range withdraws {
		records[i] = s.convertWithdrawToTransaction(withdraw)
	}

	return records, int64(total), nil
}

// convertDepositToTransaction 转换充值记录为交易记录
func (s *sTransactionService) convertDepositToTransaction(deposit *entity.MerchantDeposits) *v1.TransactionRecord {
	statusMap := map[string]string{
		"1": "pending",
		"2": "confirmed",
		"3": "failed",
	}

	status := statusMap[gconv.String(deposit.State)]
	if status == "" {
		status = "pending"
	}

	return &v1.TransactionRecord{
		ID:         gconv.Int64(deposit.RechargesId),
		MerchantID: gconv.Int64(deposit.MerchantId),
		Type:       "deposit",
		Address:    gconv.String(deposit.ToAddress),
		Chain:      gconv.String(deposit.Chan),
		Token:      gconv.String(deposit.Name),
		Amount:     gconv.String(deposit.Amount),
		TxHash:     gconv.String(deposit.TxHash),
		Status:     status,
		CreatedAt:  deposit.CreatedAt.String(),
		UpdatedAt:  deposit.UpdatedAt.String(),
	}
}

// convertWithdrawToTransaction 转换提现记录为交易记录
func (s *sTransactionService) convertWithdrawToTransaction(withdraw *entity.MerchantWithdraws) *v1.TransactionRecord {
	statusMap := map[string]string{
		"1": "pending",
		"2": "processing",
		"3": "cancelled",
		"4": "completed",
		"5": "failed",
	}

	status := statusMap[gconv.String(withdraw.State)]
	if status == "" {
		status = "pending"
	}

	return &v1.TransactionRecord{
		ID:         gconv.Int64(withdraw.WithdrawsId),
		MerchantID: gconv.Int64(withdraw.MerchantId),
		Type:       "withdraw",
		OrderID:    gconv.String(withdraw.OrderNo),
		Address:    gconv.String(withdraw.Address),
		Chain:      gconv.String(withdraw.Chan),
		Token:      gconv.String(withdraw.Name),
		Amount:     gconv.String(withdraw.Amount),
		Fee:        gconv.String(withdraw.HandlingFee),
		NetAmount:  gconv.String(withdraw.ActualAmount),
		TxHash:     gconv.String(withdraw.TxHash),
		Status:     status,
		CreatedAt:  withdraw.CreatedAt.String(),
		UpdatedAt:  withdraw.UpdatedAt.String(),
	}
}

// GetTransactionDetail 获取交易详情
func (s *sTransactionService) GetTransactionDetail(ctx context.Context, merchantId int64, req *v1.TransactionDetailReq) (*v1.TransactionDetailData, error) {
	if req.Type == "deposit" || (req.Type == "" && req.TxHash != "") {
		// 查询充值记录
		detail, err := s.getDepositDetail(ctx, merchantId, req)
		if err == nil && detail != nil {
			return detail, nil
		}
	}

	if req.Type == "withdraw" || req.Type == "" {
		// 查询提现记录
		detail, err := s.getWithdrawDetail(ctx, merchantId, req)
		if err == nil && detail != nil {
			return detail, nil
		}
	}

	return nil, gerror.New("交易记录不存在")
}

// getDepositDetail 获取充值详情
func (s *sTransactionService) getDepositDetail(ctx context.Context, merchantId int64, req *v1.TransactionDetailReq) (*v1.TransactionDetailData, error) {
	query := dao.MerchantDeposits.Ctx(ctx).Where(dao.MerchantDeposits.Columns().MerchantId, merchantId)

	if req.ID > 0 {
		query = query.Where(dao.MerchantDeposits.Columns().RechargesId, req.ID)
	} else if req.TxHash != "" {
		query = query.Where(dao.MerchantDeposits.Columns().TxHash, req.TxHash)
	} else {
		return nil, gerror.New("请提供交易ID或交易哈希")
	}

	var deposit *entity.MerchantDeposits
	err := query.Scan(&deposit)
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值详情失败")
	}

	if deposit == nil {
		return nil, nil
	}

	transactionRecord := s.convertDepositToTransaction(deposit)
	return &v1.TransactionDetailData{
		TransactionRecord: *transactionRecord,
		FailReason:        gconv.String(deposit.Error),
	}, nil
}

// getWithdrawDetail 获取提现详情
func (s *sTransactionService) getWithdrawDetail(ctx context.Context, merchantId int64, req *v1.TransactionDetailReq) (*v1.TransactionDetailData, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	if req.ID > 0 {
		query = query.Where(dao.MerchantWithdraws.Columns().WithdrawsId, req.ID)
	} else if req.TxHash != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().TxHash, req.TxHash)
	} else {
		return nil, gerror.New("请提供交易ID或交易哈希")
	}

	var withdraw *entity.MerchantWithdraws
	err := query.Scan(&withdraw)
	if err != nil {
		return nil, gerror.Wrap(err, "查询提现详情失败")
	}

	if withdraw == nil {
		return nil, nil
	}

	transactionRecord := s.convertWithdrawToTransaction(withdraw)
	return &v1.TransactionDetailData{
		TransactionRecord: *transactionRecord,
		Memo:              gconv.String(withdraw.UserRemark),
		FailReason:        gconv.String(withdraw.ErrorMessage),
	}, nil
}

// GetTransactionStats 获取交易统计
func (s *sTransactionService) GetTransactionStats(ctx context.Context, merchantId int64, req *v1.TransactionStatsReq) (*v1.TransactionStatsData, error) {
	stats := &v1.TransactionStatsData{
		ByChain:  make(map[string]*v1.ChainStats),
		ByToken:  make(map[string]*v1.TokenStats),
		ByDate:   make(map[string]*v1.DateStats),
		ByStatus: make(map[string]*v1.StatusStats),
	}

	// 查询充值统计
	if req.Type == "" || req.Type == "deposit" {
		depositStats, err := s.getDepositStats(ctx, merchantId, req)
		if err != nil {
			return nil, gerror.Wrap(err, "查询充值统计失败")
		}
		s.mergeStats(stats, depositStats)
	}

	// 查询提现统计
	if req.Type == "" || req.Type == "withdraw" {
		withdrawStats, err := s.getWithdrawStats(ctx, merchantId, req)
		if err != nil {
			return nil, gerror.Wrap(err, "查询提现统计失败")
		}
		s.mergeStats(stats, withdrawStats)
	}

	return stats, nil
}

// getDepositStats 获取充值统计
func (s *sTransactionService) getDepositStats(ctx context.Context, merchantId int64, req *v1.TransactionStatsReq) (*v1.TransactionStatsData, error) {
	query := dao.MerchantDeposits.Ctx(ctx).Where(dao.MerchantDeposits.Columns().MerchantId, merchantId)

	// 添加时间范围筛选
	if req.StartTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where(dao.MerchantDeposits.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	// 添加其他筛选条件
	if req.Chain != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Chan, req.Chain)
	}
	if req.Token != "" {
		query = query.Where(dao.MerchantDeposits.Columns().Name, req.Token)
	}

	var deposits []*entity.MerchantDeposits
	err := query.Scan(&deposits)
	if err != nil {
		return nil, err
	}

	return s.calculateStatsFromDeposits(deposits), nil
}

// getWithdrawStats 获取提现统计
func (s *sTransactionService) getWithdrawStats(ctx context.Context, merchantId int64, req *v1.TransactionStatsReq) (*v1.TransactionStatsData, error) {
	query := dao.MerchantWithdraws.Ctx(ctx).Where(dao.MerchantWithdraws.Columns().MerchantId, merchantId)

	// 添加时间范围筛选
	if req.StartTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().CreatedAt+" <= ?", req.EndTime)
	}

	// 添加其他筛选条件
	if req.Chain != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Chan, req.Chain)
	}
	if req.Token != "" {
		query = query.Where(dao.MerchantWithdraws.Columns().Name, req.Token)
	}

	var withdraws []*entity.MerchantWithdraws
	err := query.Scan(&withdraws)
	if err != nil {
		return nil, err
	}

	return s.calculateStatsFromWithdraws(withdraws), nil
}

// calculateStatsFromDeposits 从充值记录计算统计
func (s *sTransactionService) calculateStatsFromDeposits(deposits []*entity.MerchantDeposits) *v1.TransactionStatsData {
	stats := &v1.TransactionStatsData{
		ByChain:  make(map[string]*v1.ChainStats),
		ByToken:  make(map[string]*v1.TokenStats),
		ByDate:   make(map[string]*v1.DateStats),
		ByStatus: make(map[string]*v1.StatusStats),
	}

	for _, deposit := range deposits {
		amount := gconv.String(deposit.Amount)
		chain := gconv.String(deposit.Chan)
		token := gconv.String(deposit.Name)
		status := s.mapDepositStatus(gconv.String(deposit.State))
		date := deposit.CreatedAt.Format("2006-01-02")

		// 更新总计
		stats.TotalCount++
		stats.TotalAmount = s.addAmount(stats.TotalAmount, amount)

		// 按状态统计
		switch status {
		case "confirmed":
			stats.SuccessCount++
			stats.SuccessAmount = s.addAmount(stats.SuccessAmount, amount)
		case "pending":
			stats.PendingCount++
			stats.PendingAmount = s.addAmount(stats.PendingAmount, amount)
		case "failed":
			stats.FailedCount++
			stats.FailedAmount = s.addAmount(stats.FailedAmount, amount)
		}

		// 按链统计
		if stats.ByChain[chain] == nil {
			stats.ByChain[chain] = &v1.ChainStats{Chain: chain}
		}
		stats.ByChain[chain].Count++
		stats.ByChain[chain].Amount = s.addAmount(stats.ByChain[chain].Amount, amount)

		// 按代币统计
		if stats.ByToken[token] == nil {
			stats.ByToken[token] = &v1.TokenStats{Token: token}
		}
		stats.ByToken[token].Count++
		stats.ByToken[token].Amount = s.addAmount(stats.ByToken[token].Amount, amount)

		// 按日期统计
		if stats.ByDate[date] == nil {
			stats.ByDate[date] = &v1.DateStats{Date: date}
		}
		stats.ByDate[date].Count++
		stats.ByDate[date].Amount = s.addAmount(stats.ByDate[date].Amount, amount)

		// 按状态统计
		if stats.ByStatus[status] == nil {
			stats.ByStatus[status] = &v1.StatusStats{Status: status}
		}
		stats.ByStatus[status].Count++
		stats.ByStatus[status].Amount = s.addAmount(stats.ByStatus[status].Amount, amount)
	}

	return stats
}

// calculateStatsFromWithdraws 从提现记录计算统计
func (s *sTransactionService) calculateStatsFromWithdraws(withdraws []*entity.MerchantWithdraws) *v1.TransactionStatsData {
	stats := &v1.TransactionStatsData{
		ByChain:  make(map[string]*v1.ChainStats),
		ByToken:  make(map[string]*v1.TokenStats),
		ByDate:   make(map[string]*v1.DateStats),
		ByStatus: make(map[string]*v1.StatusStats),
	}

	for _, withdraw := range withdraws {
		amount := gconv.String(withdraw.Amount)
		chain := gconv.String(withdraw.Chan)
		token := gconv.String(withdraw.Name)
		status := s.mapWithdrawStatus(gconv.String(withdraw.State))
		date := withdraw.CreatedAt.Format("2006-01-02")

		// 更新总计
		stats.TotalCount++
		stats.TotalAmount = s.addAmount(stats.TotalAmount, amount)

		// 按状态统计
		switch status {
		case "completed":
			stats.SuccessCount++
			stats.SuccessAmount = s.addAmount(stats.SuccessAmount, amount)
		case "pending", "processing":
			stats.PendingCount++
			stats.PendingAmount = s.addAmount(stats.PendingAmount, amount)
		case "failed", "cancelled":
			stats.FailedCount++
			stats.FailedAmount = s.addAmount(stats.FailedAmount, amount)
		}

		// 按链统计
		if stats.ByChain[chain] == nil {
			stats.ByChain[chain] = &v1.ChainStats{Chain: chain}
		}
		stats.ByChain[chain].Count++
		stats.ByChain[chain].Amount = s.addAmount(stats.ByChain[chain].Amount, amount)

		// 按代币统计
		if stats.ByToken[token] == nil {
			stats.ByToken[token] = &v1.TokenStats{Token: token}
		}
		stats.ByToken[token].Count++
		stats.ByToken[token].Amount = s.addAmount(stats.ByToken[token].Amount, amount)

		// 按日期统计
		if stats.ByDate[date] == nil {
			stats.ByDate[date] = &v1.DateStats{Date: date}
		}
		stats.ByDate[date].Count++
		stats.ByDate[date].Amount = s.addAmount(stats.ByDate[date].Amount, amount)

		// 按状态统计
		if stats.ByStatus[status] == nil {
			stats.ByStatus[status] = &v1.StatusStats{Status: status}
		}
		stats.ByStatus[status].Count++
		stats.ByStatus[status].Amount = s.addAmount(stats.ByStatus[status].Amount, amount)
	}

	return stats
}

// mergeStats 合并统计数据
func (s *sTransactionService) mergeStats(target, source *v1.TransactionStatsData) {
	target.TotalCount += source.TotalCount
	target.TotalAmount = s.addAmount(target.TotalAmount, source.TotalAmount)
	target.SuccessCount += source.SuccessCount
	target.SuccessAmount = s.addAmount(target.SuccessAmount, source.SuccessAmount)
	target.PendingCount += source.PendingCount
	target.PendingAmount = s.addAmount(target.PendingAmount, source.PendingAmount)
	target.FailedCount += source.FailedCount
	target.FailedAmount = s.addAmount(target.FailedAmount, source.FailedAmount)

	// 合并分组统计
	for key, value := range source.ByChain {
		if target.ByChain[key] == nil {
			target.ByChain[key] = &v1.ChainStats{Chain: key}
		}
		target.ByChain[key].Count += value.Count
		target.ByChain[key].Amount = s.addAmount(target.ByChain[key].Amount, value.Amount)
	}

	for key, value := range source.ByToken {
		if target.ByToken[key] == nil {
			target.ByToken[key] = &v1.TokenStats{Token: key}
		}
		target.ByToken[key].Count += value.Count
		target.ByToken[key].Amount = s.addAmount(target.ByToken[key].Amount, value.Amount)
	}

	for key, value := range source.ByDate {
		if target.ByDate[key] == nil {
			target.ByDate[key] = &v1.DateStats{Date: key}
		}
		target.ByDate[key].Count += value.Count
		target.ByDate[key].Amount = s.addAmount(target.ByDate[key].Amount, value.Amount)
	}

	for key, value := range source.ByStatus {
		if target.ByStatus[key] == nil {
			target.ByStatus[key] = &v1.StatusStats{Status: key}
		}
		target.ByStatus[key].Count += value.Count
		target.ByStatus[key].Amount = s.addAmount(target.ByStatus[key].Amount, value.Amount)
	}
}

// mapDepositStatus 映射充值状态
func (s *sTransactionService) mapDepositStatus(dbStatus string) string {
	statusMap := map[string]string{
		"1": "pending",
		"2": "confirmed",
		"3": "failed",
	}
	if status, ok := statusMap[dbStatus]; ok {
		return status
	}
	return "pending"
}

// mapWithdrawStatus 映射提现状态
func (s *sTransactionService) mapWithdrawStatus(dbStatus string) string {
	statusMap := map[string]string{
		"1": "pending",
		"2": "processing",
		"3": "cancelled",
		"4": "completed",
		"5": "failed",
	}
	if status, ok := statusMap[dbStatus]; ok {
		return status
	}
	return "pending"
}

// addAmount 金额相加（字符串格式）
func (s *sTransactionService) addAmount(amount1, amount2 string) string {
	if amount1 == "" {
		amount1 = "0"
	}
	if amount2 == "" {
		amount2 = "0"
	}

	// 使用decimal进行精确计算
	dec1, _ := decimal.NewFromString(amount1)
	dec2, _ := decimal.NewFromString(amount2)
	return dec1.Add(dec2).String()
}
