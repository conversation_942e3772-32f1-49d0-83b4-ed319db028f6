package merchant

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"

	"merchant-server/internal/dao"
	"merchant-server/internal/model/do"
	"merchant-server/internal/model/entity"
)

type sCallbackService struct{}

func NewCallbackService() *sCallbackService {
	return &sCallbackService{}
}

// DepositCallbackData 充值回调数据
type DepositCallbackData struct {
	Type          string `json:"type"`          // 回调类型：deposit
	MerchantID    int64  `json:"merchant_id"`   // 商户ID
	DepositID     int64  `json:"deposit_id"`    // 充值记录ID
	UserLabel     string `json:"user_label"`    // 用户标识
	Address       string `json:"address"`       // 充值地址
	Chain         string `json:"chain"`         // 区块链类型
	Token         string `json:"token"`         // 代币类型
	Amount        string `json:"amount"`        // 充值金额
	TxHash        string `json:"tx_hash"`       // 交易哈希
	BlockHeight   int64  `json:"block_height"`  // 区块高度
	Confirmations int    `json:"confirmations"` // 确认数
	Status        string `json:"status"`        // 状态
	CreatedAt     string `json:"created_at"`    // 创建时间
	ConfirmedAt   string `json:"confirmed_at"`  // 确认时间
	Timestamp     int64  `json:"timestamp"`     // 回调时间戳
}

// WithdrawCallbackData 提现回调数据
type WithdrawCallbackData struct {
	Type        string `json:"type"`         // 回调类型：withdraw
	MerchantID  int64  `json:"merchant_id"`  // 商户ID
	WithdrawID  int64  `json:"withdraw_id"`  // 提现记录ID
	OrderID     string `json:"order_id"`     // 商户订单号
	UserLabel   string `json:"user_label"`   // 用户标识
	ToAddress   string `json:"to_address"`   // 提现地址
	Chain       string `json:"chain"`        // 区块链类型
	Token       string `json:"token"`        // 代币类型
	Amount      string `json:"amount"`       // 提现金额
	Fee         string `json:"fee"`          // 手续费
	NetAmount   string `json:"net_amount"`   // 实际到账金额
	TxHash      string `json:"tx_hash"`      // 交易哈希
	Status      string `json:"status"`       // 状态
	FailReason  string `json:"fail_reason"`  // 失败原因
	CreatedAt   string `json:"created_at"`   // 创建时间
	ProcessedAt string `json:"processed_at"` // 处理时间
	CompletedAt string `json:"completed_at"` // 完成时间
	Timestamp   int64  `json:"timestamp"`    // 回调时间戳
}

// CallbackRequest 回调请求结构
type CallbackRequest struct {
	Data      interface{} `json:"data"`      // 回调数据
	Signature string      `json:"signature"` // 签名
	Timestamp int64       `json:"timestamp"` // 时间戳
	Nonce     string      `json:"nonce"`     // 随机数
}

// SendDepositCallback 发送充值回调通知
func (s *sCallbackService) SendDepositCallback(ctx context.Context, deposit *entity.MerchantDeposits, callbackURL string) error {
	if callbackURL == "" {
		return nil // 没有回调地址，跳过
	}

	// 构建回调数据
	callbackData := &DepositCallbackData{
		Type:        "deposit",
		MerchantID:  gconv.Int64(deposit.MerchantId),
		DepositID:   gconv.Int64(deposit.RechargesId),
		Address:     gconv.String(deposit.ToAddress),
		Chain:       gconv.String(deposit.Chan),
		Token:       gconv.String(deposit.Name),
		Amount:      gconv.String(deposit.Amount),
		TxHash:      gconv.String(deposit.TxHash),
		Status:      s.convertDepositStatus(gconv.String(deposit.State)),
		CreatedAt:   deposit.CreatedAt.String(),
		ConfirmedAt: deposit.UpdatedAt.String(),
		Timestamp:   time.Now().Unix(),
	}

	// 获取商户密钥用于签名
	merchantSecret, err := s.getMerchantSecret(ctx, gconv.Int64(deposit.MerchantId))
	if err != nil {
		return gerror.Wrap(err, "获取商户密钥失败")
	}

	// 发送回调
	return s.sendCallback(ctx, callbackURL, callbackData, merchantSecret, "deposit", gconv.Int64(deposit.RechargesId))
}

// SendWithdrawCallback 发送提现回调通知
func (s *sCallbackService) SendWithdrawCallback(ctx context.Context, withdraw *entity.MerchantWithdraws, callbackURL string) error {
	if callbackURL == "" {
		return nil // 没有回调地址，跳过
	}

	// 构建回调数据
	callbackData := &WithdrawCallbackData{
		Type:        "withdraw",
		MerchantID:  gconv.Int64(withdraw.MerchantId),
		WithdrawID:  gconv.Int64(withdraw.WithdrawsId),
		OrderID:     gconv.String(withdraw.OrderNo),
		ToAddress:   gconv.String(withdraw.Address),
		Chain:       gconv.String(withdraw.Chan),
		Token:       gconv.String(withdraw.Name),
		Amount:      gconv.String(withdraw.Amount),
		Fee:         gconv.String(withdraw.HandlingFee),
		NetAmount:   gconv.String(withdraw.ActualAmount),
		TxHash:      gconv.String(withdraw.TxHash),
		Status:      s.convertWithdrawStatus(gconv.String(withdraw.State)),
		FailReason:  gconv.String(withdraw.ErrorMessage),
		CreatedAt:   withdraw.CreatedAt.String(),
		ProcessedAt: gconv.String(withdraw.ProcessingAt),
		CompletedAt: gconv.String(withdraw.CompletedAt),
		Timestamp:   time.Now().Unix(),
	}

	// 获取商户密钥用于签名
	merchantSecret, err := s.getMerchantSecret(ctx, gconv.Int64(withdraw.MerchantId))
	if err != nil {
		return gerror.Wrap(err, "获取商户密钥失败")
	}

	// 发送回调
	return s.sendCallback(ctx, callbackURL, callbackData, merchantSecret, "withdraw", gconv.Int64(withdraw.WithdrawsId))
}

// sendCallback 发送回调通知
func (s *sCallbackService) sendCallback(ctx context.Context, callbackURL string, data interface{}, secret, callbackType string, recordID int64) error {
	// 生成随机数和时间戳
	nonce := s.generateNonce()
	timestamp := time.Now().Unix()

	// 序列化数据
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return gerror.Wrap(err, "序列化回调数据失败")
	}

	// 生成签名
	signature := s.generateSignature(string(dataBytes), secret, timestamp, nonce)

	// 构建请求
	request := &CallbackRequest{
		Data:      data,
		Signature: signature,
		Timestamp: timestamp,
		Nonce:     nonce,
	}

	// 记录回调日志
	callbackID, err := s.recordCallback(ctx, callbackType, recordID, callbackURL, request)
	if err != nil {
		g.Log().Error(ctx, "记录回调日志失败:", err)
	}

	// 发送HTTP请求
	client := gclient.New()
	client.SetTimeout(30 * time.Second)

	response, err := client.Post(ctx, callbackURL, request)
	if err != nil {
		// 更新回调状态为失败
		s.updateCallbackStatus(ctx, callbackID, "failed", err.Error())
		return gerror.Wrap(err, "发送回调请求失败")
	}
	defer response.Close()

	// 检查响应状态
	if response.StatusCode != http.StatusOK {
		errMsg := fmt.Sprintf("回调响应状态码错误: %d", response.StatusCode)
		s.updateCallbackStatus(ctx, callbackID, "failed", errMsg)
		return gerror.New(errMsg)
	}

	// 更新回调状态为成功
	s.updateCallbackStatus(ctx, callbackID, "success", "")
	return nil
}

// getMerchantSecret 获取商户密钥
func (s *sCallbackService) getMerchantSecret(ctx context.Context, merchantID int64) (string, error) {
	var apiKey *entity.MerchantApiKeys
	err := dao.MerchantApiKeys.Ctx(ctx).
		Where(dao.MerchantApiKeys.Columns().MerchantId, merchantID).
		Where(dao.MerchantApiKeys.Columns().Status, "active").
		OrderDesc(dao.MerchantApiKeys.Columns().CreatedAt).
		Limit(1).
		Scan(&apiKey)

	if err != nil {
		return "", err
	}

	if apiKey == nil {
		return "", gerror.New("商户API密钥不存在")
	}

	return gconv.String(apiKey.SecretHash), nil
}

// generateNonce 生成随机数
func (s *sCallbackService) generateNonce() string {
	return gconv.String(time.Now().UnixNano())
}

// generateSignature 生成签名
func (s *sCallbackService) generateSignature(data, secret string, timestamp int64, nonce string) string {
	// 构建签名字符串：data + timestamp + nonce
	signStr := fmt.Sprintf("%s%d%s", data, timestamp, nonce)

	// HMAC-SHA256签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// convertDepositStatus 转换充值状态
func (s *sCallbackService) convertDepositStatus(state string) string {
	statusMap := map[string]string{
		"1": "pending",
		"2": "confirmed",
		"3": "failed",
	}
	if status, ok := statusMap[state]; ok {
		return status
	}
	return "pending"
}

// convertWithdrawStatus 转换提现状态
func (s *sCallbackService) convertWithdrawStatus(state string) string {
	statusMap := map[string]string{
		"1": "pending",
		"2": "processing",
		"3": "cancelled",
		"4": "completed",
		"5": "failed",
	}
	if status, ok := statusMap[state]; ok {
		return status
	}
	return "pending"
}

// recordCallback 记录回调日志
func (s *sCallbackService) recordCallback(ctx context.Context, callbackType string, recordID int64, url string, request *CallbackRequest) (int64, error) {
	requestData, _ := json.Marshal(request)

	result, err := dao.MerchantCallbacks.Ctx(ctx).Insert(do.MerchantCallbacks{
		CallbackType: callbackType,
		RelatedId:    recordID,
		CallbackUrl:  url,
		Payload:      string(requestData),
		Status:       "pending",
		CreatedAt:    gtime.Now(),
		UpdatedAt:    gtime.Now(),
	})

	if err != nil {
		return 0, err
	}

	id, _ := result.LastInsertId()
	return id, nil
}

// updateCallbackStatus 更新回调状态
func (s *sCallbackService) updateCallbackStatus(ctx context.Context, callbackID int64, status, errorMsg string) {
	updateData := g.Map{
		dao.MerchantCallbacks.Columns().Status:    status,
		dao.MerchantCallbacks.Columns().UpdatedAt: gtime.Now(),
	}

	if errorMsg != "" {
		updateData[dao.MerchantCallbacks.Columns().ResponseBody] = errorMsg
	}

	if status == "success" {
		updateData[dao.MerchantCallbacks.Columns().LastAttemptAt] = gtime.Now()
	}

	dao.MerchantCallbacks.Ctx(ctx).
		Where(dao.MerchantCallbacks.Columns().Id, callbackID).
		Update(updateData)
}

// RetryFailedCallbacks 重试失败的回调
func (s *sCallbackService) RetryFailedCallbacks(ctx context.Context) error {
	// 查询需要重试的回调（失败状态且重试次数小于最大值）
	var callbacks []*entity.MerchantCallbacks
	err := dao.MerchantCallbacks.Ctx(ctx).
		Where(dao.MerchantCallbacks.Columns().Status, "failed").
		Where(dao.MerchantCallbacks.Columns().RetryCount+" < ?", 3).                             // 最多重试3次
		Where(dao.MerchantCallbacks.Columns().CreatedAt+" > ?", gtime.Now().Add(-24*time.Hour)). // 只重试24小时内的
		OrderAsc(dao.MerchantCallbacks.Columns().CreatedAt).
		Limit(100). // 每次最多处理100条
		Scan(&callbacks)

	if err != nil {
		return gerror.Wrap(err, "查询失败回调记录失败")
	}

	for _, callback := range callbacks {
		// 解析请求数据
		var request CallbackRequest
		if err := json.Unmarshal([]byte(gconv.String(callback.Payload)), &request); err != nil {
			g.Log().Error(ctx, "解析回调请求数据失败:", err)
			continue
		}

		// 重新发送回调
		client := gclient.New()
		client.SetTimeout(30 * time.Second)

		response, err := client.Post(ctx, gconv.String(callback.CallbackUrl), &request)
		if err != nil {
			// 增加重试次数
			s.incrementRetryCount(ctx, gconv.Int64(callback.Id))
			g.Log().Error(ctx, "重试回调失败:", err)
			continue
		}

		if response.StatusCode == http.StatusOK {
			// 重试成功
			s.updateCallbackStatus(ctx, gconv.Int64(callback.Id), "success", "")
			g.Log().Info(ctx, "回调重试成功:", callback.Id)
		} else {
			// 重试失败，增加重试次数
			s.incrementRetryCount(ctx, gconv.Int64(callback.Id))
			g.Log().Error(ctx, "回调重试失败，状态码:", response.StatusCode)
		}

		response.Close()
	}

	return nil
}

// incrementRetryCount 增加重试次数
func (s *sCallbackService) incrementRetryCount(ctx context.Context, callbackID int64) {
	dao.MerchantCallbacks.Ctx(ctx).
		Where(dao.MerchantCallbacks.Columns().Id, callbackID).
		Update(g.Map{
			dao.MerchantCallbacks.Columns().RetryCount: gdb.Raw(dao.MerchantCallbacks.Columns().RetryCount + " + 1"),
			dao.MerchantCallbacks.Columns().UpdatedAt:  gtime.Now(),
		})
}

// StartCallbackRetryWorker 启动回调重试工作器
func (s *sCallbackService) StartCallbackRetryWorker(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(5 * time.Minute) // 每5分钟执行一次
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := s.RetryFailedCallbacks(ctx); err != nil {
					g.Log().Error(ctx, "回调重试任务执行失败:", err)
				}
			}
		}
	}()
}
