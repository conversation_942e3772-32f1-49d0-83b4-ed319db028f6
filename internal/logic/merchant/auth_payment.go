package merchant

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/shopspring/decimal"
	externalWallet "github.com/yalks/wallet"
	externalWalletConstants "github.com/yalks/wallet/constants"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/config"
	"merchant-server/internal/constants"
	"merchant-server/internal/dao"
	"merchant-server/internal/model/entity"
	"merchant-server/internal/service"
	"merchant-server/internal/utility"
	"merchant-server/internal/utils"
	"merchant-server/internal/wallet"

	uuid "github.com/google/uuid"
)

type sAuthPaymentService struct{}

func init() {
	service.RegisterAuthPaymentService(NewAuthPaymentService())

	// Initialize external wallet manager for user wallets
	ctx := context.Background()
	if err := externalWallet.Initialize(ctx); err != nil {
		g.Log().Errorf(ctx, "Failed to initialize external Wallet service: %v", err)
		// Don't panic here as it might already be initialized by other services
	}
}

func NewAuthPaymentService() *sAuthPaymentService {
	return &sAuthPaymentService{}
}

// 订单参数结构
type createOrderParams struct {
	orderNo         string
	merchantId      int64
	userAccount     string
	userId          int64
	orderType       string
	tokenSymbol     string
	amount          decimal.Decimal
	authReason      string
	merchantOrderNo string
	callbackUrl     string
	callbackBot     string
	status          string
	expireAt        *gtime.Time
	requestIP       string
}

// CreateAuthPaymentOrder 创建授权支付订单
func (s *sAuthPaymentService) CreateAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentCreateReq) (*v1.AuthPaymentCreateData, error) {
	//1. 授权校验
	isValid, errorKey := utility.ValidateAuthPayment(ctx, req)
	if !isValid {
		g.Log().Errorf(ctx, "ValidateAuthPayment failed - MerchantId: %d, OrderType: %s, ErrorKey: %s", merchantId, req.OrderType, errorKey)
		return nil, gerror.New(errorKey)
	}

	// 2. 金额校验
	isValid, errorKey = utility.ValidateAmountDecimal(ctx, req.Amount, req.TokenSymbol)
	if !isValid {
		return nil, gerror.New(errorKey)
	}

	// 3. 幂等性检查（如果提供了merchantOrderNo）
	if req.MerchantOrderNo != "" {
		err := s.ensureIdempotency(ctx, merchantId, req.MerchantOrderNo)
		if err != nil {
			return nil, err
		}
	}

	// 4. 获取请求IP
	requestIP := s.getClientIP(ctx)

	// 5. 根据订单类型处理
	var result *v1.AuthPaymentCreateData
	var err error

	switch req.OrderType {
	case "add":
		g.Log().Infof(ctx, "Processing add funds order - MerchantId: %d, UserAccount: %s, Amount: %s %s", 
			merchantId, req.UserAccount, req.Amount.String(), req.TokenSymbol)
		result, err = s.processAddFundsOrder(ctx, merchantId, req, requestIP)
	case "deduct":
		result, err = s.processDeductFundsOrder(ctx, merchantId, req, requestIP)
	default:
		return nil, gerror.New("无效的订单类型")
	}

	// 6. 释放分布式锁（如果使用了merchantOrderNo）
	if req.MerchantOrderNo != "" {
		s.releaseIdempotencyLock(ctx, merchantId, req.MerchantOrderNo)
	}

	if err != nil {
		g.Log().Errorf(ctx, "CreateAuthPaymentOrder error - MerchantId: %d, OrderType: %s, Error: %v", 
			merchantId, req.OrderType, err)
	} else if result == nil {
		g.Log().Warningf(ctx, "CreateAuthPaymentOrder returned nil result - MerchantId: %d, OrderType: %s", 
			merchantId, req.OrderType)
	} else {
		g.Log().Infof(ctx, "CreateAuthPaymentOrder success - OrderNo: %s, OrderType: %s, Status: %s", 
			result.OrderNo, result.OrderType, result.Status)
	}

	return result, err
}

// processAddFundsOrder 处理加款订单
func (s *sAuthPaymentService) processAddFundsOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentCreateReq, requestIP string) (*v1.AuthPaymentCreateData, error) {
	// 1. 查询用户信息
	var user entity.Users
	err := dao.Users.Ctx(ctx).Where("account", req.UserAccount).Scan(&user)
	if err != nil {
		// 不暴露SQL错误细节，统一返回用户友好的错误信息
		g.Log().Errorf(ctx, "查询用户失败 - UserAccount: %s, Error: %v", req.UserAccount, err)
		return nil, gerror.New("用户不存在")
	}
	if user.Id == 0 {
		return nil, gerror.New("用户不存在")
	}

	// 2. 验证商户余额是否充足（加款订单需要从商户账户扣款）
	walletService := GetWalletService()
	merchantBalance, err := walletService.GetBalance(ctx, uint64(merchantId), req.TokenSymbol)
	if err != nil {
		// 如果钱包库返回错误，可能是钱包不存在
		if walletErr := wallet.GetWalletError(err); walletErr != nil && walletErr.Code == wallet.ErrCodeWalletNotFound {
			return nil, gerror.New("商户钱包不存在")
		}
		return nil, gerror.Wrap(err, "获取商户余额失败")
	}

	// 检查商户可用余额是否充足
	if merchantBalance.Available.LessThan(req.Amount) {
		return nil, gerror.Newf("商户余额不足，当前可用余额: %s, 加款金额: %s",
			merchantBalance.Available.String(), req.Amount.String())
	}

	// 3. 生成订单号
	orderNo := s.generateOrderNo("AP")

	// 4. 创建资金操作描述器
	descriptor := utils.NewFundOperationDescriptor("zh")
	businessID := fmt.Sprintf("auth_payment_%s", orderNo)

	var result *v1.AuthPaymentCreateData

	// 5. 事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 5.1 创建订单记录
		orderId, err := s.createAuthPaymentOrder(ctx, tx, &createOrderParams{
			orderNo:         orderNo,
			merchantId:      merchantId,
			userAccount:     req.UserAccount,
			userId:          int64(user.Id),
			orderType:       req.OrderType,
			tokenSymbol:     req.TokenSymbol,
			amount:          req.Amount,
			authReason:      req.AuthReason,
			merchantOrderNo: req.MerchantOrderNo,
			callbackUrl:     req.CallbackUrl,
			callbackBot:     req.CallbackBot,
			status:          "pending",
			requestIP:       requestIP,
		})
		if err != nil {
			return err
		}

		g.Log().Infof(ctx, "Starting balance transfer for order %s: merchant_id=%d, user_id=%d, amount=%s %s", 
			orderNo, merchantId, user.Id, req.Amount.String(), req.TokenSymbol)

		// 5.2 执行商户扣款
		merchantDebitReq := &wallet.TransactionRequest{
			MerchantID:       uint64(merchantId),
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Operation:        wallet.OperationTransfer,
			Direction:        wallet.DirectionOut,
			WalletType:       wallet.WalletTypeAvailable,
			BusinessID:       businessID + "_debit",
			Memo:             &req.AuthReason,
			RequestSource:    &[]string{"auth_payment"}[0],
			RequestIP:        &requestIP,
			RelatedEntityID:  &[]uint64{uint64(user.Id)}[0],
			RelatedEntityType: &[]string{"user"}[0],
			RequestMetadata: map[string]interface{}{
				"order_no":       orderNo,
				"order_type":     "auth_payment_debit",
				"user_id":        fmt.Sprintf("%d", user.Id),
				"user_account":   req.UserAccount,
				"auth_reason":    req.AuthReason,
			},
		}

		// 获取钱包管理器实例
		walletManager := wallet.NewManager(
			wallet.WithDBGroup("default"),
			wallet.WithAutoEnsureWallet(true),
		)

		// 执行商户扣款
		merchantResult, err := walletManager.UpdateBalance(ctx, tx, merchantDebitReq)
		if err != nil {
			// 更新订单状态为失败
			s.updateOrderStatus(ctx, tx, orderId, "failed", fmt.Sprintf("商户扣款失败: %v", err), nil, nil)
			return err
		}

		g.Log().Infof(ctx, "Merchant debit successful for order %s: merchant_tx_id=%d", orderNo, merchantResult.TransactionID)

		// 5.3 执行用户加款（使用external wallet）
		// 使用 github.com/yalks/wallet 库处理用户资金
		userCreditDescription := descriptor.FormatBasicDescription(constants.FundOpAuthPaymentAdd, req.Amount.String(), req.TokenSymbol)
		
		// 构建用户加款请求
		userCreditReq := &externalWalletConstants.FundOperationRequest{
			UserID:      uint64(user.Id),
			TokenSymbol: req.TokenSymbol,
			Amount:      req.Amount,
			BusinessID:  businessID + "_credit",
			FundType:    externalWalletConstants.FundTypeTransferIn,
			Description: userCreditDescription,
			RelatedID:   int64(merchantId),
			Metadata: map[string]string{
				"order_no":      orderNo,
				"order_type":    "auth_payment_credit",
				"merchant_id":   fmt.Sprintf("%d", merchantId),
				"auth_reason":   req.AuthReason,
			},
			RequestSource: "auth_payment",
		}

		// 使用 external wallet 执行用户加款
		userResult, err := externalWallet.Manager().ProcessFundOperationInTx(ctx, tx, userCreditReq)
		if err != nil {
			// 更新订单状态为失败
			s.updateOrderStatus(ctx, tx, orderId, "failed", fmt.Sprintf("用户加款失败: %v", err), nil, nil)
			return err
		}

		// TransactionID 是 string 类型，需要转换
		userTxIdStr := userResult.TransactionID
		userTxId := uint64(0)
		if userTxIdStr != "" {
			// 尝试将字符串转换为 uint64
			if id, err := strconv.ParseUint(userTxIdStr, 10, 64); err == nil {
				userTxId = id
			}
		}
		g.Log().Infof(ctx, "User credit successful for order %s: user_tx_id=%s", orderNo, userTxIdStr)

		// 5.4 更新订单为完成状态
		merchantTxId := merchantResult.TransactionID
		err = s.updateOrderStatus(ctx, tx, orderId, "completed", "",
			&merchantTxId, &userTxId)
		if err != nil {
			return err
		}

		// 5.5 创建回调记录
		callbackUrl := req.CallbackUrl
		if callbackUrl == "" {
			callbackUrl = s.getMerchantCallbackUrl(ctx, merchantId)
		}
		if callbackUrl != "" {
			err = s.createCallbackRecord(ctx, tx, orderId, merchantId, orderNo, constants.CallbackEventAuthAddCompleted, req.Amount, req.TokenSymbol)
			if err != nil {
				g.Log().Errorf(ctx, "创建回调记录失败: %v", err)
				// 回调失败不影响主流程
			}
		}

		// 5.6 构建响应
		result = &v1.AuthPaymentCreateData{
			OrderNo:          orderNo,
			MerchantOrderNo:  req.MerchantOrderNo,
			UserAccount:      req.UserAccount,
			OrderType:        req.OrderType,
			TokenSymbol:      req.TokenSymbol,
			Amount:           req.Amount,
			Status:           "completed",
			CreatedAt:        gtime.Now().Format("c"),
			CallbackRobotUrl: s.generateTelegramDeepLink(ctx, orderNo),
		}

		g.Log().Infof(ctx, "Auth payment order %s processed: merchant_tx=%d, user_tx=%d", 
			orderNo, merchantTxId, userTxId)

		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "加款订单处理失败: %v", err)
		return nil, gerror.Wrap(err, "订单处理失败")
	}

	g.Log().Infof(ctx, "Add funds order processed successfully - OrderNo: %s, MerchantOrderNo: %s, Amount: %s %s", 
		result.OrderNo, result.MerchantOrderNo, result.Amount.String(), result.TokenSymbol)

	return result, nil
}

// processDeductFundsOrder 处理扣款订单
func (s *sAuthPaymentService) processDeductFundsOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentCreateReq, requestIP string) (*v1.AuthPaymentCreateData, error) {
	// 1. 查询用户信息
	var user entity.Users
	err := dao.Users.Ctx(ctx).Where("account", req.UserAccount).Scan(&user)
	if err != nil {
		// 不暴露SQL错误细节，统一返回用户友好的错误信息
		g.Log().Errorf(ctx, "查询用户失败 - UserAccount: %s, Error: %v", req.UserAccount, err)
		return nil, gerror.New("用户不存在")
	}
	if user.Id == 0 {
		return nil, gerror.New("用户不存在")
	}

	// 2. 验证用户余额是否充足（扣款订单创建时暂不验证，等用户确认时再验证）
	// 这里仅做提示性验证，让商户知道用户当前余额状态
	userBalanceInfo, balanceErr := externalWallet.Manager().GetBalance(ctx, user.Id, req.TokenSymbol)
	if balanceErr == nil && userBalanceInfo != nil {
		// 如果用户余额不足，仍然创建订单但记录警告日志
		if userBalanceInfo.AvailableBalance.LessThan(req.Amount) {
			g.Log().Warningf(ctx, "用户余额可能不足，当前可用余额: %s, 扣款金额: %s",
				userBalanceInfo.AvailableBalance.String(), req.Amount.String())
		}
	} else if balanceErr != nil {
		// 如果获取余额失败，记录日志但不阻止订单创建
		g.Log().Warningf(ctx, "获取用户余额失败: %v", balanceErr)
	}

	// 3. 生成订单号
	orderNo := s.generateOrderNo("DP")

	// 4. 计算过期时间
	expireMinutes := req.ExpireMinutes
	if expireMinutes == 0 {
		expireMinutes = 30 // 默认30分钟
	}
	expireAt := gtime.Now().Add(time.Duration(expireMinutes) * time.Minute)

	// 5. 创建待确认订单
	_, err = s.createAuthPaymentOrder(ctx, nil, &createOrderParams{
		orderNo:         orderNo,
		merchantId:      merchantId,
		userAccount:     req.UserAccount,
		userId:          int64(user.Id),
		orderType:       req.OrderType,
		tokenSymbol:     req.TokenSymbol,
		amount:          req.Amount,
		authReason:      req.AuthReason,
		merchantOrderNo: req.MerchantOrderNo,
		callbackUrl:     req.CallbackUrl,
		callbackBot:     req.CallbackBot,
		status:          "pending",
		expireAt:        expireAt,
		requestIP:       requestIP,
	})

	if err != nil {
		return nil, gerror.Wrap(err, "创建订单失败")
	}

	// 6. 构建响应
	return &v1.AuthPaymentCreateData{
		OrderNo:          orderNo,
		MerchantOrderNo:  req.MerchantOrderNo,
		UserAccount:      req.UserAccount,
		OrderType:        req.OrderType,
		TokenSymbol:      req.TokenSymbol,
		Amount:           req.Amount,
		Status:           "pending",
		ExpireAt:         expireAt.Format("c"),
		CreatedAt:        gtime.Now().Format("c"),
		CallbackRobotUrl: s.generateTelegramDeepLink(ctx, orderNo),
	}, nil
}

// QueryAuthPaymentOrder 查询授权支付订单
func (s *sAuthPaymentService) QueryAuthPaymentOrder(ctx context.Context, merchantId int64, req *v1.AuthPaymentQueryReq) (*v1.AuthPaymentQueryData, error) {
	query := dao.AuthPaymentOrders.Ctx(ctx).Where("merchant_id", merchantId)

	if req.OrderNo != "" {
		query = query.Where("order_no", req.OrderNo)
	} else if req.MerchantOrderNo != "" {
		query = query.Where("merchant_order_no", req.MerchantOrderNo)
	} else {
		return nil, gerror.New("订单号和商户订单号至少填一个")
	}

	var order entity.AuthPaymentOrders
	err := query.Scan(&order)
	if err != nil {
		return nil, gerror.New("查询订单失败")
	}
	if order.Id == 0 {
		return nil, gerror.New("订单不存在")
	}

	return &v1.AuthPaymentQueryData{
		OrderNo:          order.OrderNo,
		MerchantOrderNo:  order.MerchantOrderNo,
		UserAccount:      order.UserAccount,
		OrderType:        order.OrderType,
		TokenSymbol:      order.TokenSymbol,
		Amount:           order.Amount,
		AuthReason:       order.AuthReason,
		Status:           order.Status,
		CallbackStatus:   order.CallbackStatus,
		ErrorMessage:     order.ErrorMessage,
		ExpireAt:         s.formatTime(order.ExpireAt),
		CompletedAt:      s.formatTime(order.CompletedAt),
		CreatedAt:        order.CreatedAt.Format("c"),
		CallbackRobotUrl: s.generateTelegramDeepLink(ctx, order.OrderNo),
	}, nil
}

// ListAuthPaymentOrders 获取授权支付订单列表
func (s *sAuthPaymentService) ListAuthPaymentOrders(ctx context.Context, merchantId int64, req *v1.AuthPaymentListReq) (*v1.AuthPaymentListData, error) {
	query := dao.AuthPaymentOrders.Ctx(ctx).Where("merchant_id", merchantId)

	// 添加筛选条件
	if req.UserAccount != "" {
		query = query.Where("user_account", req.UserAccount)
	}
	if req.OrderType != "" {
		query = query.Where("order_type", req.OrderType)
	}
	if req.Status != "" {
		query = query.Where("status", req.Status)
	}
	if req.StartTime != "" {
		query = query.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("created_at <= ?", req.EndTime)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Errorf(ctx, "查询订单总数失败 - MerchantId: %d, Error: %v", merchantId, err)
		return nil, gerror.New("查询订单总数失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	var orders []entity.AuthPaymentOrders
	err = query.OrderDesc("created_at").
		Limit(req.PageSize).
		Offset(offset).
		Scan(&orders)

	if err != nil {
		g.Log().Errorf(ctx, "查询订单列表失败 - MerchantId: %d, Error: %v", merchantId, err)
		return nil, gerror.New("查询订单列表失败")
	}

	// 转换为响应格式
	list := make([]v1.AuthPaymentQueryData, len(orders))
	for i, order := range orders {
		list[i] = v1.AuthPaymentQueryData{
			OrderNo:          order.OrderNo,
			MerchantOrderNo:  order.MerchantOrderNo,
			UserAccount:      order.UserAccount,
			OrderType:        order.OrderType,
			TokenSymbol:      order.TokenSymbol,
			Amount:           order.Amount,
			AuthReason:       order.AuthReason,
			Status:           order.Status,
			CallbackStatus:   order.CallbackStatus,
			ErrorMessage:     order.ErrorMessage,
			ExpireAt:         s.formatTime(order.ExpireAt),
			CompletedAt:      s.formatTime(order.CompletedAt),
			CreatedAt:        order.CreatedAt.Format("c"),
			CallbackRobotUrl: s.generateTelegramDeepLink(ctx, order.OrderNo),
		}
	}

	return &v1.AuthPaymentListData{
		PageResponse: v1.PageResponse{
			Total:    int64(total),
			Page:     req.Page,
			PageSize: req.PageSize,
		},
		List: list,
	}, nil
}

// ProcessAuthPaymentCallback 处理授权支付回调
func (s *sAuthPaymentService) ProcessAuthPaymentCallback(ctx context.Context, orderId int64) error {
	// 这个方法由后台任务调用，执行实际的回调通知
	// 具体实现将在回调服务中处理
	return nil
}

// createAuthPaymentOrder 创建授权支付订单
func (s *sAuthPaymentService) createAuthPaymentOrder(ctx context.Context, tx gdb.TX, params *createOrderParams) (int64, error) {
	data := g.Map{
		"order_no":          params.orderNo,
		"merchant_id":       params.merchantId,
		"user_account":      params.userAccount,
		"user_id":           params.userId,
		"order_type":        params.orderType,
		"token_symbol":      params.tokenSymbol,
		"amount":            params.amount.String(),
		"auth_reason":       params.authReason,
		"merchant_order_no": params.merchantOrderNo,
		"callback_url":      params.callbackUrl,
		"callback_bot":      params.callbackBot,
		"status":            params.status,
		"request_ip":        params.requestIP,
		"created_at":        gtime.Now(),
		"updated_at":        gtime.Now(),
	}

	if params.expireAt != nil {
		data["expire_at"] = params.expireAt
	}

	var id int64
	var err error

	if tx != nil {
		id, err = dao.AuthPaymentOrders.Ctx(ctx).TX(tx).InsertAndGetId(data)
	} else {
		id, err = dao.AuthPaymentOrders.Ctx(ctx).InsertAndGetId(data)
	}

	if err != nil {
		return 0, gerror.Wrap(err, "创建订单失败")
	}

	return id, nil
}

// updateOrderStatus 更新订单状态
func (s *sAuthPaymentService) updateOrderStatus(ctx context.Context, tx gdb.TX, orderId int64, status string, errorMsg string, merchantTxId, userTxId *uint64) error {
	data := g.Map{
		"status":     status,
		"updated_at": gtime.Now(),
	}

	if errorMsg != "" {
		data["error_message"] = errorMsg
	}

	if merchantTxId != nil {
		data["merchant_transaction_id"] = *merchantTxId
	}

	if userTxId != nil {
		data["user_transaction_id"] = *userTxId
	}

	if status == "completed" {
		data["completed_at"] = gtime.Now()
	}

	var err error
	if tx != nil {
		_, err = dao.AuthPaymentOrders.Ctx(ctx).TX(tx).Where("id", orderId).Update(data)
	} else {
		_, err = dao.AuthPaymentOrders.Ctx(ctx).Where("id", orderId).Update(data)
	}

	return err
}

// createCallbackRecord 创建回调记录
func (s *sAuthPaymentService) createCallbackRecord(ctx context.Context, tx gdb.TX, orderId int64, merchantId int64, orderNo string, eventType string, amount decimal.Decimal, tokenSymbol string) error {
	// 获取商户信息和回调URL
	callbackUrl := s.getMerchantCallbackUrl(ctx, merchantId)
	if callbackUrl == "" {
		return nil // 无回调URL，跳过
	}

	// 构建回调数据
	callbackData := g.Map{
		"event_type":   eventType,
		"order_no":     orderNo,
		"merchant_id":  merchantId,
		"amount":       amount.String(),
		"token_symbol": tokenSymbol,
		"timestamp":    time.Now().Unix(),
	}

	payload, err := json.Marshal(callbackData)
	if err != nil {
		return gerror.Wrap(err, "序列化回调数据失败")
	}

	// 插入回调记录
	insertData := g.Map{
		"merchant_id":   merchantId,
		"callback_type": constants.CallbackEventAuthPayment,
		"related_id":    orderId,
		"callback_url":  callbackUrl,
		"payload":       string(payload),
		"status":        "pending",
		"retry_count":   0,
		"created_at":    gtime.Now(),
		"updated_at":    gtime.Now(),
	}

	if tx != nil {
		_, err = dao.MerchantCallbacks.Ctx(ctx).TX(tx).Insert(insertData)
	} else {
		_, err = dao.MerchantCallbacks.Ctx(ctx).Insert(insertData)
	}

	return err
}

// 辅助方法

// generateOrderNo 生成订单号
func (s *sAuthPaymentService) generateOrderNo(prefix string) string {
	// 格式: 前缀 + 时间戳 + 随机数
	// 例如: AP20250109120530123456
	timestamp := gtime.Now().Format("YmdHis")
	random := grand.Digits(6)
	uuidStr := uuid.New().String()
	// 确保随机数和UUID的唯一性
	//拼接 uuid +随机数+时间戳
	uuidStr = fmt.Sprintf("%s%s%s", uuidStr, random, timestamp)

	//使用 md5 缩短uuid
	md5Hash := md5.Sum([]byte(uuidStr))

	return fmt.Sprintf("%s%x", prefix, md5Hash)
}

// getClientIP 获取客户端IP地址
func (s *sAuthPaymentService) getClientIP(ctx context.Context) string {
	request := g.RequestFromCtx(ctx)
	if request != nil {
		// 优先从 X-Forwarded-For 头获取真实IP
		if ip := request.Header.Get("X-Forwarded-For"); ip != "" {
			// 取第一个IP，移除空白字符
			firstIP := strings.TrimSpace(strings.Split(ip, ",")[0])
			if validIP := s.cleanIP(firstIP); validIP != "" {
				return validIP
			}
		}
		// 其次从 X-Real-IP 头获取
		if ip := request.Header.Get("X-Real-IP"); ip != "" {
			if validIP := s.cleanIP(strings.TrimSpace(ip)); validIP != "" {
				return validIP
			}
		}
		// 最后使用远程地址
		if clientIP := request.GetClientIp(); clientIP != "" {
			if validIP := s.cleanIP(clientIP); validIP != "" {
				return validIP
			}
		}
	}
	return "127.0.0.1" // 默认值
}

// cleanIP 清理和验证IP地址
func (s *sAuthPaymentService) cleanIP(ip string) string {
	if ip == "" {
		return ""
	}

	// 移除端口号（如果存在）
	if host, _, err := net.SplitHostPort(ip); err == nil {
		ip = host
	}

	// 移除空白字符
	ip = strings.TrimSpace(ip)

	// 验证IP格式
	if net.ParseIP(ip) != nil {
		return ip
	}

	return ""
}

// getMerchantCallbackUrl 获取商户回调URL
func (s *sAuthPaymentService) getMerchantCallbackUrl(ctx context.Context, merchantId int64) string {
	var merchant entity.Merchants
	err := dao.Merchants.Ctx(ctx).Where("merchant_id", merchantId).Where("deleted_at IS NULL").Scan(&merchant)
	if err != nil || merchant.MerchantId == 0 {
		return ""
	}
	return merchant.CallbackUrl
}

// formatTime 格式化时间
func (s *sAuthPaymentService) formatTime(t *gtime.Time) string {
	if t == nil || t.IsZero() {
		return ""
	}
	return t.Format("c")
}

// generateTelegramDeepLink 生成Telegram机器人深度链接
func (s *sAuthPaymentService) generateTelegramDeepLink(ctx context.Context, orderNo string) string {
	// 从配置获取bot名称
	botName, err := config.GetString(ctx, "telegram_bot_setting.name", "")
	if err != nil || botName == "" {
		g.Log().Warning(ctx, "Telegram bot name not configured")
		return ""
	}

	// 构建深度链接
	// 格式: https://t.me/{botName}?start={prefix}{orderNo}
	deepLink := fmt.Sprintf("https://t.me/%s?start=%s",
		botName,
		orderNo)

	return deepLink
}

// ensureIdempotency 确保幂等性（使用分布式锁防止重复创建相同merchantOrderNo的订单）
func (s *sAuthPaymentService) ensureIdempotency(ctx context.Context, merchantId int64, merchantOrderNo string) error {
	if merchantOrderNo == "" {
		return nil
	}

	// 构建锁的key
	lockKey := fmt.Sprintf("auth_payment:lock:%d:%s", merchantId, merchantOrderNo)

	// 获取Redis实例
	redis := g.Redis()
	if redis == nil {
		g.Log().Warning(ctx, "Redis not available for idempotency check")
		// Redis不可用时，降级处理：检查数据库是否已存在该订单
		return s.checkDuplicateOrder(ctx, merchantId, merchantOrderNo)
	}

	// 尝试获取分布式锁（锁定时间30秒，避免长时间占用）
	lockValue := grand.S(16) // 生成随机锁值
	success, err := redis.SetNX(ctx, lockKey, lockValue)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to acquire idempotency lock - Key: %s, Error: %v", lockKey, err)
		// Redis错误时，降级到数据库检查
		return s.checkDuplicateOrder(ctx, merchantId, merchantOrderNo)
	}

	// 如果成功获取锁，设置过期时间
	if success {
		_, _ = redis.Expire(ctx, lockKey, 30)
	}

	if !success {
		// 锁已被占用，说明有并发请求在处理相同的merchantOrderNo
		g.Log().Warningf(ctx, "Duplicate request detected - MerchantId: %d, MerchantOrderNo: %s", merchantId, merchantOrderNo)
		return gerror.New("订单正在处理中，请勿重复提交")
	}

	// 成功获取锁后，再次检查数据库是否已存在该订单（双重检查）
	err = s.checkDuplicateOrder(ctx, merchantId, merchantOrderNo)
	if err != nil {
		// 如果订单已存在，释放锁并返回错误
		redis.Del(ctx, lockKey)
		return err
	}

	// 锁会在订单创建完成后释放，或者30秒后自动过期
	return nil
}

// releaseIdempotencyLock 释放幂等性锁
func (s *sAuthPaymentService) releaseIdempotencyLock(ctx context.Context, merchantId int64, merchantOrderNo string) {
	if merchantOrderNo == "" {
		return
	}

	lockKey := fmt.Sprintf("auth_payment:lock:%d:%s", merchantId, merchantOrderNo)

	redis := g.Redis()
	if redis != nil {
		_, err := redis.Del(ctx, lockKey)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to release idempotency lock - Key: %s, Error: %v", lockKey, err)
		}
	}
}

// checkDuplicateOrder 检查是否存在重复订单
func (s *sAuthPaymentService) checkDuplicateOrder(ctx context.Context, merchantId int64, merchantOrderNo string) error {
	count, err := dao.AuthPaymentOrders.Ctx(ctx).
		Where("merchant_id", merchantId).
		Where("merchant_order_no", merchantOrderNo).
		Count()

	if err != nil {
		g.Log().Errorf(ctx, "Check duplicate order failed - MerchantId: %d, MerchantOrderNo: %s, Error: %v",
			merchantId, merchantOrderNo, err)
		return gerror.New("订单重复性检查失败")
	}

	if count > 0 {
		g.Log().Warningf(ctx, "Duplicate order found - MerchantId: %d, MerchantOrderNo: %s", merchantId, merchantOrderNo)
		return gerror.New("商户订单号已存在")
	}

	return nil
}
