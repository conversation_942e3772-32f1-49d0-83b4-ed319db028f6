package merchant

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/service"
)

func (c *ControllerV1) AuthPaymentCreate(ctx context.Context, req *v1.AuthPaymentCreateReq) (res *v1.AuthPaymentCreateRes, err error) {
	// 从上下文获取商户ID
	merchantId := gconv.Int64(g.RequestFromCtx(ctx).GetCtxVar("merchant_id"))
	if merchantId == 0 {
		return &v1.AuthPaymentCreateRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeUnauthorized,
				Message: v1.MsgUnauthorized,
			},
		}, nil
	}

	// 调用业务逻辑
	data, err := service.AuthPaymentService().CreateAuthPaymentOrder(ctx, merchantId, req)
	if err != nil {
		// 根据错误类型返回对应的错误码
		var code int
		errorMsg := err.Error()
		
		// 判断错误类型
		switch {
		case strings.Contains(errorMsg, "余额不足"):
			code = v1.CodeInsufficientFunds
		case strings.Contains(errorMsg, "用户不存在"):
			code = v1.CodeNotFound
		case strings.Contains(errorMsg, "订单号已存在"):
			code = v1.CodeInvalidParams
		case strings.Contains(errorMsg, "授权支付功能未启用"):
			code = v1.CodeServiceUnavailable
		case strings.Contains(errorMsg, "不支持的代币类型"):
			code = v1.CodeInvalidParams
		case strings.Contains(errorMsg, "金额"):
			code = v1.CodeInvalidParams
		default:
			code = v1.CodeInternalError
		}
		
		return &v1.AuthPaymentCreateRes{
			CommonResponse: v1.CommonResponse{
				Code:    code,
				Message: errorMsg,
			},
		}, nil
	}

	return &v1.AuthPaymentCreateRes{
		CommonResponse: v1.CommonResponse{
			Code:    v1.CodeSuccess,
			Message: v1.MsgSuccess,
		},
		Data: data,
	}, nil
}