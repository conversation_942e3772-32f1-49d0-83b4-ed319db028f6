package merchant

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/service"
)

func (c *ControllerV1) TransactionQuery(ctx context.Context, req *v1.TransactionQueryReq) (res *v1.TransactionQueryRes, err error) {
	// 从上下文获取商户ID
	merchantId := gconv.Int64(g.RequestFromCtx(ctx).GetCtxVar("merchant_id"))
	if merchantId == 0 {
		return &v1.TransactionQueryRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeUnauthorized,
				Message: v1.MsgUnauthorized,
			},
		}, nil
	}

	// 调用业务逻辑
	data, err := service.TransactionService().QueryTransactions(ctx, merchantId, req)
	if err != nil {
		return &v1.TransactionQueryRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeInternalError,
				Message: err.Error(),
			},
		}, nil
	}

	return &v1.TransactionQueryRes{
		CommonResponse: v1.CommonResponse{
			Code:    v1.CodeSuccess,
			Message: v1.MsgSuccess,
		},
		Data: data,
	}, nil
}
