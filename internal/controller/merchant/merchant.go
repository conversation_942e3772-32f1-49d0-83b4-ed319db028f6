// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package merchant

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "merchant-server/api/merchant/v1"
	"merchant-server/internal/service"
)

type ControllerV1 struct{}

func New() *ControllerV1 {
	return &ControllerV1{}
}


// TransactionDetail 获取交易详情
func (c *ControllerV1) TransactionDetail(ctx context.Context, req *v1.TransactionDetailReq) (res *v1.TransactionDetailRes, err error) {
	// 从上下文获取商户ID
	merchantId := gconv.Int64(g.RequestFromCtx(ctx).GetCtxVar("merchant_id"))
	if merchantId == 0 {
		return &v1.TransactionDetailRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeUnauthorized,
				Message: v1.MsgUnauthorized,
			},
		}, nil
	}

	// 调用业务逻辑
	data, err := service.TransactionService().GetTransactionDetail(ctx, merchantId, req)
	if err != nil {
		return &v1.TransactionDetailRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeInternalError,
				Message: err.Error(),
			},
		}, nil
	}

	return &v1.TransactionDetailRes{
		CommonResponse: v1.CommonResponse{
			Code:    v1.CodeSuccess,
			Message: v1.MsgSuccess,
		},
		Data: data,
	}, nil
}

// TransactionStats 获取交易统计
func (c *ControllerV1) TransactionStats(ctx context.Context, req *v1.TransactionStatsReq) (res *v1.TransactionStatsRes, err error) {
	// 从上下文获取商户ID
	merchantId := gconv.Int64(g.RequestFromCtx(ctx).GetCtxVar("merchant_id"))
	if merchantId == 0 {
		return &v1.TransactionStatsRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeUnauthorized,
				Message: v1.MsgUnauthorized,
			},
		}, nil
	}

	// 调用业务逻辑
	data, err := service.TransactionService().GetTransactionStats(ctx, merchantId, req)
	if err != nil {
		return &v1.TransactionStatsRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeInternalError,
				Message: err.Error(),
			},
		}, nil
	}

	return &v1.TransactionStatsRes{
		CommonResponse: v1.CommonResponse{
			Code:    v1.CodeSuccess,
			Message: v1.MsgSuccess,
		},
		Data: data,
	}, nil
}

