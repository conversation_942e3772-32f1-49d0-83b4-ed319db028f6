package merchant

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"

	"merchant-server/api/merchant/v1"
	"merchant-server/internal/logic/middleware"
)

func (c *ControllerV1) GetClientIP(ctx context.Context, req *v1.GetClientIPReq) (res *v1.GetClientIPRes, err error) {
	// 获取当前请求对象
	r := ghttp.RequestFromCtx(ctx)
	if r == nil {
		return &v1.GetClientIPRes{
			CommonResponse: v1.CommonResponse{
				Code:    v1.CodeInternalError,
				Message: "无法获取请求上下文",
			},
		}, nil
	}

	// 使用与认证中间件相同的方法获取客户端IP
	clientIP := middleware.GetClientIP(r)

	// 收集相关头信息（仅用于调试）
	headers := []string{}
	if xForwardedFor := r.Header.Get("X-Forwarded-For"); xForwardedFor != "" {
		headers = append(headers, "X-Forwarded-For: "+xForwardedFor)
	}
	if xRealIP := r.Header.Get("X-Real-IP"); xRealIP != "" {
		headers = append(headers, "X-Real-IP: "+xRealIP)
	}
	if cfConnectingIP := r.Header.Get("CF-Connecting-IP"); cfConnectingIP != "" {
		headers = append(headers, "CF-Connecting-IP: "+cfConnectingIP)
	}
	headers = append(headers, "RemoteAddr: "+r.RemoteAddr)

	return &v1.GetClientIPRes{
		CommonResponse: v1.CommonResponse{
			Code:    v1.CodeSuccess,
			Message: v1.MsgSuccess,
		},
		Data: &v1.GetClientIPData{
			IP:      clientIP,
			Headers: headers,
		},
	}, nil
}