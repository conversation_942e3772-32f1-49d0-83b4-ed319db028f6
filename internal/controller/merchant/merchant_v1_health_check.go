package merchant

import (
	"context"
	"time"

	"merchant-server/api/merchant/v1"
)

func (c *ControllerV1) HealthCheck(ctx context.Context, req *v1.HealthCheckReq) (res *v1.HealthCheckRes, err error) {
	return &v1.HealthCheckRes{
		CommonResponse: v1.CommonResponse{
			Code:    0,
			Message: "OK",
		},
		Data: &v1.HealthCheckData{
			Status:    "ok",
			Timestamp: time.Now().UnixMilli(),
		},
	}, nil
}
