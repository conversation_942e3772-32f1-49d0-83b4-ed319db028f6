{"MERCHANT_SERVER_SERVER_ADDRESS": ":9000", "MERCHANT_SERVER_SERVER_OPENAPIPATH": "/api.json", "MERCHANT_SERVER_SERVER_SWAGGERPATH": "/swagger", "MERCHANT_SERVER_SERVER_SERVERAGENT": "Merchant-Server/1.0", "MERCHANT_SERVER_SERVER_LOGPATH": "logs", "MERCHANT_SERVER_SERVER_DUMPROUTERMAP": false, "MERCHANT_SERVER_LOGGER_LEVEL": "info", "MERCHANT_SERVER_LOGGER_STDOUT": true, "MERCHANT_SERVER_LOGGER_PATH": "logs", "MERCHANT_SERVER_LOGGER_FILE": "server.log", "MERCHANT_SERVER_LOGGER_ROTATESIZE": "100M", "MERCHANT_SERVER_LOGGER_ROTATEEXPIRE": "7d", "MERCHANT_SERVER_LOGGER_FORMAT": "json", "MERCHANT_SERVER_DATABASE_LOGGER_PATH": "logs/database", "MERCHANT_SERVER_DATABASE_LOGGER_LEVEL": "error", "MERCHANT_SERVER_DATABASE_LOGGER_STDOUT": false, "MERCHANT_SERVER_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/merchant?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s", "MERCHANT_SERVER_DATABASE_DEFAULT_DEBUG": false, "MERCHANT_SERVER_DATABASE_DEFAULT_MAXIDLECONNCOUNT": 10, "MERCHANT_SERVER_DATABASE_DEFAULT_MAXOPENCONNCOUNT": 100, "MERCHANT_SERVER_DATABASE_DEFAULT_MAXCONNLIFETIME": "30s", "MERCHANT_SERVER_REDIS_DEFAULT_ADDRESS": "valkey:6379", "MERCHANT_SERVER_REDIS_DEFAULT_DB": 0, "MERCHANT_SERVER_REDIS_DEFAULT_PASS": "valkey_password", "MERCHANT_SERVER_REDIS_DEFAULT_IDLETIMEOUT": "60s", "MERCHANT_SERVER_REDIS_DEFAULT_MAXCONNLIFETIME": "90s", "MERCHANT_SERVER_REDIS_DEFAULT_WAITTIMEOUT": "60s", "MERCHANT_SERVER_REDIS_DEFAULT_DIALTIMEOUT": "30s", "MERCHANT_SERVER_REDIS_DEFAULT_READTIMEOUT": "30s", "MERCHANT_SERVER_REDIS_DEFAULT_WRITETIMEOUT": "30s", "MERCHANT_SERVER_REDIS_DEFAULT_MAXACTIVE": 100, "MERCHANT_SERVER_SECURITY_RATELIMIT_ENABLED": true, "MERCHANT_SERVER_SECURITY_RATELIMIT_DEFAULTLIMIT": 100, "MERCHANT_SERVER_SECURITY_RATELIMIT_WINDOWSECONDS": 60, "MERCHANT_SERVER_SECURITY_CORS_ALLOWORIGINS_0": "http://localhost:3000", "MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_0": "GET", "MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_1": "POST", "MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_2": "PUT", "MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_3": "DELETE", "MERCHANT_SERVER_SECURITY_CORS_ALLOWMETHODS_4": "OPTIONS", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_0": "Content-Type", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_1": "Authorization", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_2": "X-API-Key", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_3": "X-Timestamp", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_4": "X-<PERSON><PERSON>", "MERCHANT_SERVER_SECURITY_CORS_ALLOWHEADERS_5": "X-Signature", "MERCHANT_SERVER_SECURITY_CORS_ALLOWCREDENTIALS": false, "MERCHANT_SERVER_SECURITY_CORS_MAXAGE": 86400, "MERCHANT_SERVER_SECURITY_IPWHITELIST_ENABLED": true, "MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_0": "10.0.0.0/8", "MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_1": "**********/12", "MERCHANT_SERVER_SECURITY_IPWHITELIST_TRUSTEDPROXIES_2": "***********/16", "MERCHANT_SERVER_CONSUL_ADDRESS": "consul:8500", "MERCHANT_SERVER_CONSUL_TOKEN": "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874", "MERCHANT_SERVER_CONSUL_CONFIG_PREFIX": "xpay/config", "MERCHANT_SERVER_S3_ACCESSKEYID": "********************", "MERCHANT_SERVER_S3_SECRETACCESSKEY": "jRlPC3qDWMNvW7IX8MThWEalYSxeLupBcUKZCQHO", "MERCHANT_SERVER_S3_REGION": "ap-northeast-1", "MERCHANT_SERVER_S3_BUCKETNAME": "yescex1", "MERCHANT_SERVER_S3_USEPATHSTYLEENDPOINT": false}