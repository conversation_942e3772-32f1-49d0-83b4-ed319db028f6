#!/bin/bash

# Test script for auth payment callback robot URL

API_URL="http://localhost:9000"
API_KEY="btH__8gaw93GoiSwqPrfD6aiNrErJg3u0amh-yfKmiw="
MERCHANT_ID="2"

# Generate timestamp and nonce
TIMESTAMP=$(date +%s)
NONCE=$(openssl rand -base64 16)

# Create auth payment order (add type)
echo "Creating auth payment order (add type)..."
curl -X POST "${API_URL}/api/v1/auth-payment/create" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: ${API_KEY}" \
  -H "X-Timestamp: ${TIMESTAMP}" \
  -H "X-Nonce: ${NONCE}" \
  -d '{
    "userAccount": "user123",
    "orderType": "add",
    "tokenSymbol": "USDT",
    "amount": "100",
    "authReason": "Test add funds",
    "merchantOrderNo": "TEST_ADD_'${TIMESTAMP}'"
  }' | jq .

echo -e "\n\nCreating auth payment order (deduct type)..."
# Generate new timestamp and nonce for second request
TIMESTAMP2=$(date +%s)
NONCE2=$(openssl rand -base64 16)

# Create auth payment order (deduct type)
curl -X POST "${API_URL}/api/v1/auth-payment/create" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: ${API_KEY}" \
  -H "X-Timestamp: ${TIMESTAMP2}" \
  -H "X-Nonce: ${NONCE2}" \
  -d '{
    "userAccount": "user123",
    "orderType": "deduct",
    "tokenSymbol": "USDT",
    "amount": "50",
    "authReason": "Test deduct funds",
    "merchantOrderNo": "TEST_DEDUCT_'${TIMESTAMP2}'",
    "expireMinutes": 60
  }' | jq .

echo -e "\n\nNOTE: Check the 'callbackRobotUrl' field in the response."
echo "Expected format: https://t.me/aabwcbcc_bot?start=payment_req_{orderNo}"