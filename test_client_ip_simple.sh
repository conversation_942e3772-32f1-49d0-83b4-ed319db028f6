#!/bin/bash

# 简单测试获取客户端IP接口（无需认证）

BASE_URL="http://localhost:8081"
ENDPOINT="/api/v1/client-ip"

echo "测试获取客户端IP接口（无需认证）"
echo "================================"
echo "URL: ${BASE_URL}${ENDPOINT}"
echo ""

# 直接请求
echo "1. 直接请求:"
curl -s -X GET "${BASE_URL}${ENDPOINT}" | jq .

echo ""
echo "2. 带代理头的请求:"
curl -s -X GET \
  -H "X-Forwarded-For: *******, *******" \
  -H "X-Real-IP: **********" \
  -H "CF-Connecting-IP: ***********" \
  "${BASE_URL}${ENDPOINT}" | jq .

echo ""
echo "3. 仅带 X-Forwarded-For 的请求:"
curl -s -X GET \
  -H "X-Forwarded-For: *******" \
  "${BASE_URL}${ENDPOINT}" | jq .