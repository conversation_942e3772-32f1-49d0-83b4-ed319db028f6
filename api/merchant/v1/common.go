package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HealthCheckReq 健康检查请求
type HealthCheckReq struct {
	g.Meta `path:"/health" method:"get" tags:"系统" summary:"健康检查"`
}

// HealthCheckRes 健康检查响应
type HealthCheckRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *HealthCheckData `json:"data"`
}

// HealthCheckData 健康检查数据
type HealthCheckData struct {
	Status    string `json:"status" dc:"服务状态"`
	Timestamp int64  `json:"timestamp" dc:"当前时间戳"`
}

// GetClientIPReq 获取客户端IP请求
type GetClientIPReq struct {
	g.Meta `path:"/api/v1/client-ip" method:"get" tags:"工具" summary:"获取客户端IP地址"`
}

// GetClientIPRes 获取客户端IP响应
type GetClientIPRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *GetClientIPData `json:"data"`
}

// GetClientIPData 客户端IP数据
type GetClientIPData struct {
	IP      string   `json:"ip" dc:"客户端IP地址"`
	Headers []string `json:"headers,omitempty" dc:"相关请求头信息（调试用）"`
}

// CommonResponse 通用响应结构
type CommonResponse struct {
	Code    int         `json:"code" dc:"响应码，0表示成功"`
	Message string      `json:"message" dc:"响应消息"`
	Data    interface{} `json:"data,omitempty" dc:"响应数据"`
}

// PageRequest 分页请求结构
type PageRequest struct {
	Page     int `json:"page" v:"min:1" dc:"页码，从1开始" default:"1"`
	PageSize int `json:"page_size" v:"min:1|max:100" dc:"每页数量，最大100" default:"20"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Total    int64       `json:"total" dc:"总记录数"`
	Page     int         `json:"page" dc:"当前页码"`
	PageSize int         `json:"page_size" dc:"每页数量"`
	Data     interface{} `json:"data" dc:"数据列表"`
}

// TimeRange 时间范围查询结构
type TimeRange struct {
	StartTime string `json:"start_time" v:"datetime" dc:"开始时间，格式：2006-01-02 15:04:05"`
	EndTime   string `json:"end_time" v:"datetime" dc:"结束时间，格式：2006-01-02 15:04:05"`
}

// ChainTokenInfo 链和代币信息
type ChainTokenInfo struct {
	Chain string `json:"chain" v:"required|in:TRX,TRC20,ETH,ERC20" dc:"区块链类型：TRX-波场原生币，TRC20-波场代币，ETH-以太坊原生币，ERC20-以太坊代币"`
	Token string `json:"token" v:"required" dc:"代币类型：native-原生币，或具体代币合约地址"`
}

// AddressInfo 地址信息
type AddressInfo struct {
	Address    string `json:"address" dc:"区块链地址"`
	QrCode     string `json:"qr_code,omitempty" dc:"地址二维码（Base64编码）"`
	QrCodeUrl  string `json:"qr_code_url,omitempty" dc:"地址二维码URL（S3存储）"`
}

// TransactionStatus 交易状态枚举
type TransactionStatus struct {
	Pending   string `json:"pending" dc:"待处理"`
	Confirmed string `json:"confirmed" dc:"已确认"`
	Failed    string `json:"failed" dc:"失败"`
	Cancelled string `json:"cancelled" dc:"已取消"`
}

// AmountInfo 金额信息
type AmountInfo struct {
	Amount   string `json:"amount" v:"required|regex:^\\d+(\\.\\d+)?$" dc:"金额，支持小数"`
	Currency string `json:"currency" v:"required" dc:"币种"`
}

// FeeInfo 手续费信息
type FeeInfo struct {
	Fee         string `json:"fee" dc:"手续费金额"`
	FeeCurrency string `json:"fee_currency" dc:"手续费币种"`
	FeeRate     string `json:"fee_rate,omitempty" dc:"手续费率"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code    int    `json:"code" dc:"错误码"`
	Message string `json:"message" dc:"错误消息"`
	Details string `json:"details,omitempty" dc:"错误详情"`
}

// 常用错误码定义
const (
	CodeSuccess            = 0     // 成功
	CodeInvalidParams      = 40001 // 参数错误
	CodeUnauthorized       = 40101 // 未授权
	CodeForbidden          = 40301 // 禁止访问
	CodeNotFound           = 40401 // 资源不存在
	CodeInternalError      = 50001 // 内部错误
	CodeServiceUnavailable = 50301 // 服务不可用
	CodeInsufficientFunds  = 60001 // 余额不足
	CodeAddressNotFound    = 60002 // 地址不存在
	CodeTransactionFailed  = 60003 // 交易失败
)

// 常用错误消息
var (
	MsgSuccess            = "操作成功"
	MsgInvalidParams      = "参数错误"
	MsgUnauthorized       = "未授权访问"
	MsgForbidden          = "禁止访问"
	MsgNotFound           = "资源不存在"
	MsgInternalError      = "内部服务错误"
	MsgServiceUnavailable = "服务暂不可用"
	MsgInsufficientFunds  = "余额不足"
	MsgAddressNotFound    = "地址不存在"
	MsgTransactionFailed  = "交易失败"
)
