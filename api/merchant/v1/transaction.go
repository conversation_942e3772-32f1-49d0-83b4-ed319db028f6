package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// TransactionQueryReq 查询交易记录请求
type TransactionQueryReq struct {
	g.<PERSON>a `path:"/api/v1/transactions" method:"get" tags:"交易查询" summary:"查询交易记录"`
	PageRequest
	TimeRange
	Type      string `json:"type,omitempty" v:"in:deposit,withdraw" dc:"交易类型：deposit-充值，withdraw-提现"`
	UserLabel string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
	Address   string `json:"address,omitempty" v:"length:10,100" dc:"地址"`
	Chain     string `json:"chain,omitempty" v:"in:TRX,TRC20,ETH,ERC20" dc:"区块链类型"`
	Token     string `json:"token,omitempty" dc:"代币类型"`
	Status    string `json:"status,omitempty" dc:"交易状态"`
	TxHash    string `json:"tx_hash,omitempty" v:"length:10,100" dc:"交易哈希"`
	OrderID   string `json:"order_id,omitempty" v:"length:1,100" dc:"商户订单号（仅提现）"`
	MinAmount string `json:"min_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最小金额"`
	MaxAmount string `json:"max_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最大金额"`
}

// TransactionQueryRes 查询交易记录响应
type TransactionQueryRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *PageResponse `json:"data"`
}

// TransactionRecord 交易记录
type TransactionRecord struct {
	ID            int64  `json:"id" dc:"记录ID"`
	MerchantID    int64  `json:"merchant_id" dc:"商户ID"`
	Type          string `json:"type" dc:"交易类型：deposit-充值，withdraw-提现"`
	UserLabel     string `json:"user_label" dc:"用户标识"`
	OrderID       string `json:"order_id,omitempty" dc:"商户订单号（仅提现）"`
	Address       string `json:"address" dc:"地址"`
	ToAddress     string `json:"to_address,omitempty" dc:"目标地址（仅提现）"`
	Chain         string `json:"chain" dc:"区块链类型"`
	Token         string `json:"token" dc:"代币类型"`
	Amount        string `json:"amount" dc:"交易金额"`
	Fee           string `json:"fee,omitempty" dc:"手续费（仅提现）"`
	NetAmount     string `json:"net_amount,omitempty" dc:"实际金额"`
	TxHash        string `json:"tx_hash,omitempty" dc:"交易哈希"`
	BlockHeight   int64  `json:"block_height,omitempty" dc:"区块高度"`
	Confirmations int    `json:"confirmations" dc:"确认数"`
	Status        string `json:"status" dc:"交易状态"`
	CreatedAt     string `json:"created_at" dc:"创建时间"`
	UpdatedAt     string `json:"updated_at" dc:"更新时间"`
	ConfirmedAt   string `json:"confirmed_at,omitempty" dc:"确认时间"`
}

// TransactionDetailReq 获取交易详情请求
type TransactionDetailReq struct {
	g.Meta `path:"/api/v1/transactions/detail" method:"get" tags:"交易查询" summary:"获取交易详情"`
	ID     int64  `json:"id,omitempty" v:"min:1" dc:"交易记录ID"`
	TxHash string `json:"tx_hash,omitempty" v:"length:10,100" dc:"交易哈希"`
	Type   string `json:"type,omitempty" v:"in:deposit,withdraw" dc:"交易类型"`
}

// TransactionDetailRes 获取交易详情响应
type TransactionDetailRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *TransactionDetailData `json:"data"`
}

// TransactionDetailData 交易详情数据
type TransactionDetailData struct {
	TransactionRecord
	Memo         string `json:"memo,omitempty" dc:"备注信息"`
	FailReason   string `json:"fail_reason,omitempty" dc:"失败原因"`
	CallbackURL  string `json:"callback_url,omitempty" dc:"回调地址"`
	CallbackSent bool   `json:"callback_sent" dc:"是否已发送回调"`
	CallbackAt   string `json:"callback_at,omitempty" dc:"回调时间"`
}

// TransactionStatsReq 获取交易统计请求
type TransactionStatsReq struct {
	// g.Meta `path:"/api/v1/transactions/stats" method:"get" tags:"交易查询" summary:"获取交易统计"`
	TimeRange
	Type      string `json:"type,omitempty" v:"in:deposit,withdraw" dc:"交易类型"`
	Chain     string `json:"chain,omitempty" v:"in:TRX,TRC20,ETH,ERC20" dc:"区块链类型"`
	Token     string `json:"token,omitempty" dc:"代币类型"`
	UserLabel string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
}

// TransactionStatsRes 获取交易统计响应
type TransactionStatsRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *TransactionStatsData `json:"data"`
}

// TransactionStatsData 交易统计数据
type TransactionStatsData struct {
	TotalCount    int64                   `json:"total_count" dc:"总交易数"`
	TotalAmount   string                  `json:"total_amount" dc:"总交易金额"`
	SuccessCount  int64                   `json:"success_count" dc:"成功交易数"`
	SuccessAmount string                  `json:"success_amount" dc:"成功交易金额"`
	PendingCount  int64                   `json:"pending_count" dc:"待处理交易数"`
	PendingAmount string                  `json:"pending_amount" dc:"待处理交易金额"`
	FailedCount   int64                   `json:"failed_count" dc:"失败交易数"`
	FailedAmount  string                  `json:"failed_amount" dc:"失败交易金额"`
	ByChain       map[string]*ChainStats  `json:"by_chain,omitempty" dc:"按链统计"`
	ByToken       map[string]*TokenStats  `json:"by_token,omitempty" dc:"按代币统计"`
	ByDate        map[string]*DateStats   `json:"by_date,omitempty" dc:"按日期统计"`
	ByStatus      map[string]*StatusStats `json:"by_status,omitempty" dc:"按状态统计"`
}

// ChainStats 链统计
type ChainStats struct {
	Chain  string `json:"chain" dc:"区块链类型"`
	Count  int64  `json:"count" dc:"交易数"`
	Amount string `json:"amount" dc:"交易金额"`
}

// TokenStats 代币统计
type TokenStats struct {
	Token  string `json:"token" dc:"代币类型"`
	Count  int64  `json:"count" dc:"交易数"`
	Amount string `json:"amount" dc:"交易金额"`
}

// DateStats 日期统计
type DateStats struct {
	Date   string `json:"date" dc:"日期"`
	Count  int64  `json:"count" dc:"交易数"`
	Amount string `json:"amount" dc:"交易金额"`
}

// StatusStats 状态统计
type StatusStats struct {
	Status string `json:"status" dc:"状态"`
	Count  int64  `json:"count" dc:"交易数"`
	Amount string `json:"amount" dc:"交易金额"`
}
