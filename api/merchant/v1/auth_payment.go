package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// AuthPaymentCreateReq 创建授权支付订单请求
type AuthPaymentCreateReq struct {
	g.Meta          `path:"/api/v1/auth-payment/create" method:"post" tags:"授权支付" summary:"创建授权支付订单"`
	UserAccount     string          `json:"userAccount" v:"required#用户账户不能为空" dc:"用户账户标识(users.account)"`
	OrderType       string          `json:"orderType" v:"required|in:deduct,add#订单类型不能为空|订单类型必须是deduct或add" dc:"订单类型: deduct-扣款, add-加款"`
	TokenSymbol     string          `json:"tokenSymbol" v:"required#代币符号不能为空" dc:"代币符号(如USDT)"`
	Amount          decimal.Decimal `json:"amount" v:"required|min:0.000001#金额不能为空|金额必须大于0" dc:"订单金额"`
	AuthReason      string          `json:"authReason" v:"max-length:500#授权原因不能超过500字符" dc:"授权原因/说明"`
	MerchantOrderNo string          `json:"merchantOrderNo" v:"max-length:128#商户订单号不能超过128字符" dc:"商户订单号"`
	CallbackUrl     string          `json:"callbackUrl" v:"url#回调URL格式不正确" dc:"回调URL(可选，不填则使用商户默认)"`
	CallbackBot     string          `json:"callbackBot" v:"max-length:255#回调机器人不能超过255字符" dc:"回调telegram机器人(可选)"`
	ExpireMinutes   int             `json:"expireMinutes" v:"min:1|max:10080#过期时间必须在1-10080分钟之间" dc:"过期时间(分钟，仅扣款订单有效，默认30分钟)"`
}

// AuthPaymentCreateRes 创建授权支付订单响应
type AuthPaymentCreateRes struct {
	CommonResponse
	Data *AuthPaymentCreateData `json:"data"`
}

// AuthPaymentCreateData 创建授权支付订单数据
type AuthPaymentCreateData struct {
	OrderNo          string          `json:"orderNo" dc:"系统订单号"`
	MerchantOrderNo  string          `json:"merchantOrderNo" dc:"商户订单号"`
	UserAccount      string          `json:"userAccount" dc:"用户账户"`
	OrderType        string          `json:"orderType" dc:"订单类型"`
	TokenSymbol      string          `json:"tokenSymbol" dc:"代币符号"`
	Amount           decimal.Decimal `json:"amount" dc:"订单金额"`
	Status           string          `json:"status" dc:"订单状态"`
	ExpireAt         string          `json:"expireAt,omitempty" dc:"过期时间(仅扣款订单)"`
	CreatedAt        string          `json:"createdAt" dc:"创建时间"`
	CallbackRobotUrl string          `json:"callbackRobotUrl,omitempty" dc:"回调机器人URL"`
}

// AuthPaymentQueryReq 查询授权支付订单请求
type AuthPaymentQueryReq struct {
	g.Meta          `path:"/api/v1/auth-payment/query" method:"post" tags:"授权支付" summary:"查询授权支付订单"`
	OrderNo         string `json:"orderNo" v:"required-without:MerchantOrderNo#订单号和商户订单号至少填一个" dc:"系统订单号"`
	MerchantOrderNo string `json:"merchantOrderNo" v:"required-without:OrderNo#订单号和商户订单号至少填一个" dc:"商户订单号"`
}

// AuthPaymentQueryRes 查询授权支付订单响应
type AuthPaymentQueryRes struct {
	CommonResponse
	Data *AuthPaymentQueryData `json:"data"`
}

// AuthPaymentQueryData 查询授权支付订单数据
type AuthPaymentQueryData struct {
	OrderNo          string          `json:"orderNo" dc:"系统订单号"`
	MerchantOrderNo  string          `json:"merchantOrderNo" dc:"商户订单号"`
	UserAccount      string          `json:"userAccount" dc:"用户账户"`
	OrderType        string          `json:"orderType" dc:"订单类型"`
	TokenSymbol      string          `json:"tokenSymbol" dc:"代币符号"`
	Amount           decimal.Decimal `json:"amount" dc:"订单金额"`
	AuthReason       string          `json:"authReason" dc:"授权原因"`
	Status           string          `json:"status" dc:"订单状态"`
	CallbackStatus   string          `json:"callbackStatus" dc:"回调状态"`
	ErrorMessage     string          `json:"errorMessage,omitempty" dc:"错误信息"`
	ExpireAt         string          `json:"expireAt,omitempty" dc:"过期时间"`
	CompletedAt      string          `json:"completedAt,omitempty" dc:"完成时间"`
	CreatedAt        string          `json:"createdAt" dc:"创建时间"`
	CallbackRobotUrl string          `json:"callbackRobotUrl,omitempty" dc:"回调机器人URL"`
}

// AuthPaymentListReq 授权支付订单列表请求
type AuthPaymentListReq struct {
	g.Meta      `path:"/api/v1/auth-payment/list" method:"post" tags:"授权支付" summary:"授权支付订单列表"`
	PageRequest
	UserAccount string `json:"userAccount" dc:"用户账户(可选)"`
	OrderType   string `json:"orderType" v:"in:,deduct,add#订单类型必须是deduct或add" dc:"订单类型(可选)"`
	Status      string `json:"status" v:"in:,pending,completed,expired,failed,cancelled#状态值不正确" dc:"订单状态(可选)"`
	StartTime   string `json:"startTime" v:"datetime#开始时间格式不正确" dc:"开始时间(可选)"`
	EndTime     string `json:"endTime" v:"datetime#结束时间格式不正确" dc:"结束时间(可选)"`
}

// AuthPaymentListRes 授权支付订单列表响应
type AuthPaymentListRes struct {
	CommonResponse
	Data *AuthPaymentListData `json:"data"`
}

// AuthPaymentListData 授权支付订单列表数据
type AuthPaymentListData struct {
	PageResponse
	List []AuthPaymentQueryData `json:"list" dc:"订单列表"`
}

// AuthPaymentCallbackReq 授权支付回调请求(发送给商户的)
type AuthPaymentCallbackReq struct {
	OrderNo         string          `json:"orderNo" dc:"系统订单号"`
	MerchantOrderNo string          `json:"merchantOrderNo" dc:"商户订单号"`
	UserAccount     string          `json:"userAccount" dc:"用户账户"`
	OrderType       string          `json:"orderType" dc:"订单类型"`
	TokenSymbol     string          `json:"tokenSymbol" dc:"代币符号"`
	Amount          decimal.Decimal `json:"amount" dc:"订单金额"`
	Status          string          `json:"status" dc:"订单状态"`
	CompletedAt     string          `json:"completedAt" dc:"完成时间"`
	Timestamp       int64           `json:"timestamp" dc:"时间戳"`
	Sign            string          `json:"sign" dc:"签名"`
}