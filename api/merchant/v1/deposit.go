package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// DepositAddressReq 获取充值地址请求
type DepositAddressReq struct {
	g.Meta    `path:"/api/v1/deposits/address" method:"post" tags:"充值管理" summary:"获取充值地址"`
	UserLabel string `json:"user_label" v:"required|length:1,100" dc:"用户标识，用于地址复用"`
	ChainTokenInfo
}

// DepositAddressRes 获取充值地址响应
type DepositAddressRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *DepositAddressData `json:"data"`
}

// DepositAddressData 充值地址数据
type DepositAddressData struct {
	AddressInfo
	UserLabel  string `json:"user_label" dc:"用户标识"`
	Chain      string `json:"chain" dc:"区块链类型"`
	Token      string `json:"token" dc:"代币类型"`
	CreatedAt  string `json:"created_at" dc:"创建时间"`
	ExpiredAt  string `json:"expired_at,omitempty" dc:"过期时间"`
	IsReused   bool   `json:"is_reused" dc:"是否为复用地址"`
	MinDeposit string `json:"min_deposit,omitempty" dc:"最小充值金额"`
}

// DepositQueryReq 查询充值记录请求
type DepositQueryReq struct {
	g.Meta `path:"/api/v1/deposits" method:"post" tags:"充值管理" summary:"查询充值记录"`
	PageRequest
	TimeRange
	UserLabel string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
	Address   string `json:"address,omitempty" v:"length:10,100" dc:"充值地址"`
	Chain     string `json:"chain,omitempty" v:"in:TRX,TRC20,ETH,ERC20" dc:"区块链类型"`
	Token     string `json:"token,omitempty" dc:"代币类型"`
	Status    string `json:"status,omitempty" v:"in:pending,confirmed,failed" dc:"交易状态"`
	TxHash    string `json:"tx_hash,omitempty" v:"length:10,100" dc:"交易哈希"`
	MinAmount string `json:"min_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最小金额"`
	MaxAmount string `json:"max_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最大金额"`
}

// DepositQueryRes 查询充值记录响应
type DepositQueryRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *PageResponse `json:"data"`
}

// DepositRecord 充值记录
type DepositRecord struct {
	ID            int64  `json:"id" dc:"记录ID"`
	MerchantID    int64  `json:"merchant_id" dc:"商户ID"`
	UserLabel     string `json:"user_label" dc:"用户标识"`
	Address       string `json:"address" dc:"充值地址"`
	Chain         string `json:"chain" dc:"区块链类型"`
	Token         string `json:"token" dc:"代币类型"`
	Amount        string `json:"amount" dc:"充值金额"`
	TxHash        string `json:"tx_hash" dc:"交易哈希"`
	BlockHeight   int64  `json:"block_height" dc:"区块高度"`
	Confirmations int    `json:"confirmations" dc:"确认数"`
	Status        string `json:"status" dc:"状态：pending-待确认，confirmed-已确认，failed-失败"`
	CreatedAt     string `json:"created_at" dc:"创建时间"`
	UpdatedAt     string `json:"updated_at" dc:"更新时间"`
	ConfirmedAt   string `json:"confirmed_at,omitempty" dc:"确认时间"`
	CallbackSent  bool   `json:"callback_sent" dc:"是否已发送回调"`
	CallbackAt    string `json:"callback_at,omitempty" dc:"回调时间"`
}

// DepositDetailReq 获取充值详情请求
type DepositDetailReq struct {
	g.Meta `path:"/api/v1/deposits/detail" method:"post" tags:"充值管理" summary:"获取充值详情"`
	ID     int64  `json:"id,omitempty" v:"min:1" dc:"充值记录ID"`
	TxHash string `json:"tx_hash,omitempty" v:"length:10,100" dc:"交易哈希"`
}

// DepositDetailRes 获取充值详情响应
type DepositDetailRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *DepositRecord `json:"data"`
}

// DepositAddressListReq 获取地址列表请求
type DepositAddressListReq struct {
	g.Meta `path:"/api/v1/deposits/addresses" method:"get" tags:"充值管理" summary:"获取地址列表"`
	PageRequest
	UserLabel string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
	Chain     string `json:"chain,omitempty" v:"in:TRX,TRC20,ETH,ERC20" dc:"区块链类型"`
	Token     string `json:"token,omitempty" dc:"代币类型"`
	Status    string `json:"status,omitempty" v:"in:active,inactive,expired" dc:"地址状态"`
}

// DepositAddressListRes 获取地址列表响应
type DepositAddressListRes struct {
	g.Meta `mime:"application/json"`
	CommonResponse
	Data *PageResponse `json:"data"`
}

// MerchantAddress 商户地址信息
type MerchantAddress struct {
	ID           int64  `json:"id" dc:"地址ID"`
	MerchantID   int64  `json:"merchant_id" dc:"商户ID"`
	UserLabel    string `json:"user_label" dc:"用户标识"`
	Address      string `json:"address" dc:"区块链地址"`
	Chain        string `json:"chain" dc:"区块链类型"`
	Token        string `json:"token" dc:"代币类型"`
	Status       string `json:"status" dc:"状态：active-活跃，inactive-非活跃，expired-已过期"`
	CreatedAt    string `json:"created_at" dc:"创建时间"`
	UpdatedAt    string `json:"updated_at" dc:"更新时间"`
	ExpiredAt    string `json:"expired_at,omitempty" dc:"过期时间"`
	LastUsedAt   string `json:"last_used_at,omitempty" dc:"最后使用时间"`
	DepositCount int    `json:"deposit_count" dc:"充值次数"`
	TotalAmount  string `json:"total_amount" dc:"总充值金额"`
}
