package v1

import "github.com/gogf/gf/v2/frame/g"

// "github.com/gogf/gf/v2/frame/g"

// WithdrawCreateReq 创建提现申请请求
type WithdrawCreateReq struct {
	//g.Meta      `path:"/api/v1/withdraws" method:"post" tags:"提现管理" summary:"创建提现申请"`
	OrderID     string `json:"order_id" v:"required|length:1,100" dc:"商户订单号，唯一标识"`
	ToAddress   string `json:"to_address" v:"required|length:10,100" dc:"提现目标地址"`
	UserLabel   string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
	CallbackURL string `json:"callback_url,omitempty" v:"url" dc:"回调地址"`
	ChainTokenInfo
	AmountInfo
	Memo string `json:"memo,omitempty" v:"length:0,200" dc:"备注信息"`
}

// WithdrawCreateRes 创建提现申请响应
type WithdrawCreateRes struct {
	//g.Meta `mime:"application/json"`
	CommonResponse
	Data *WithdrawCreateData `json:"data"`
}

// WithdrawCreateData 提现申请数据
type WithdrawCreateData struct {
	ID          int64  `json:"id" dc:"提现记录ID"`
	OrderID     string `json:"order_id" dc:"商户订单号"`
	Status      string `json:"status" dc:"状态：pending-待处理，processing-处理中，completed-已完成，failed-失败，cancelled-已取消"`
	ToAddress   string `json:"to_address" dc:"提现目标地址"`
	Chain       string `json:"chain" dc:"区块链类型"`
	Token       string `json:"token" dc:"代币类型"`
	Amount      string `json:"amount" dc:"提现金额"`
	Fee         string `json:"fee" dc:"手续费"`
	NetAmount   string `json:"net_amount" dc:"实际到账金额"`
	CreatedAt   string `json:"created_at" dc:"创建时间"`
	EstimatedAt string `json:"estimated_at,omitempty" dc:"预计完成时间"`
}

// WithdrawQueryReq 查询提现记录请求
type WithdrawQueryReq struct {
	g.Meta `path:"/api/v1/withdraws" method:"get" tags:"提现管理" summary:"查询提现记录"`
	PageRequest
	TimeRange
	OrderID   string `json:"order_id,omitempty" v:"length:1,100" dc:"商户订单号"`
	UserLabel string `json:"user_label,omitempty" v:"length:1,100" dc:"用户标识"`
	ToAddress string `json:"to_address,omitempty" v:"length:10,100" dc:"提现目标地址"`
	Chain     string `json:"chain,omitempty" v:"in:TRX,TRC20,ETH,ERC20" dc:"区块链类型"`
	Token     string `json:"token,omitempty" dc:"代币类型"`
	Status    string `json:"status,omitempty" v:"in:pending,processing,completed,failed,cancelled" dc:"提现状态"`
	TxHash    string `json:"tx_hash,omitempty" v:"length:10,100" dc:"交易哈希"`
	MinAmount string `json:"min_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最小金额"`
	MaxAmount string `json:"max_amount,omitempty" v:"regex:^\\d+(\\.\\d+)?$" dc:"最大金额"`
}

// WithdrawQueryRes 查询提现记录响应
type WithdrawQueryRes struct {
	//g.Meta `mime:"application/json"`
	CommonResponse
	Data *PageResponse `json:"data"`
}

// WithdrawRecord 提现记录
type WithdrawRecord struct {
	ID            int64  `json:"id" dc:"记录ID"`
	MerchantID    int64  `json:"merchant_id" dc:"商户ID"`
	OrderID       string `json:"order_id" dc:"商户订单号"`
	UserLabel     string `json:"user_label" dc:"用户标识"`
	ToAddress     string `json:"to_address" dc:"提现目标地址"`
	FromAddress   string `json:"from_address,omitempty" dc:"提现源地址"`
	Chain         string `json:"chain" dc:"区块链类型"`
	Token         string `json:"token" dc:"代币类型"`
	Amount        string `json:"amount" dc:"提现金额"`
	Fee           string `json:"fee" dc:"手续费"`
	NetAmount     string `json:"net_amount" dc:"实际到账金额"`
	TxHash        string `json:"tx_hash,omitempty" dc:"交易哈希"`
	BlockHeight   int64  `json:"block_height,omitempty" dc:"区块高度"`
	Confirmations int    `json:"confirmations" dc:"确认数"`
	Status        string `json:"status" dc:"状态：pending-待处理，processing-处理中，completed-已完成，failed-失败，cancelled-已取消"`
	FailReason    string `json:"fail_reason,omitempty" dc:"失败原因"`
	Memo          string `json:"memo,omitempty" dc:"备注信息"`
	CallbackURL   string `json:"callback_url,omitempty" dc:"回调地址"`
	CallbackSent  bool   `json:"callback_sent" dc:"是否已发送回调"`
	CallbackAt    string `json:"callback_at,omitempty" dc:"回调时间"`
	CreatedAt     string `json:"created_at" dc:"创建时间"`
	UpdatedAt     string `json:"updated_at" dc:"更新时间"`
	ProcessedAt   string `json:"processed_at,omitempty" dc:"处理时间"`
	CompletedAt   string `json:"completed_at,omitempty" dc:"完成时间"`
}

// WithdrawDetailReq 获取提现详情请求
type WithdrawDetailReq struct {
	g.Meta  `path:"/api/v1/withdraws/detail" method:"get" tags:"提现管理" summary:"获取提现详情"`
	ID      int64  `json:"id,omitempty" v:"min:1" dc:"提现记录ID"`
	OrderID string `json:"order_id,omitempty" v:"length:1,100" dc:"商户订单号"`
}

// WithdrawDetailRes 获取提现详情响应
type WithdrawDetailRes struct {
	//g.Meta `mime:"application/json"`
	CommonResponse
	Data *WithdrawRecord `json:"data"`
}

// WithdrawCancelReq 取消提现申请请求
type WithdrawCancelReq struct {
	//g.Meta  `path:"/api/v1/withdraws/cancel" method:"post" tags:"提现管理" summary:"取消提现申请"`
	ID      int64  `json:"id,omitempty" v:"min:1" dc:"提现记录ID"`
	OrderID string `json:"order_id,omitempty" v:"length:1,100" dc:"商户订单号"`
	Reason  string `json:"reason,omitempty" v:"length:0,200" dc:"取消原因"`
}

// WithdrawCancelRes 取消提现申请响应
type WithdrawCancelRes struct {
	//g.Meta `mime:"application/json"`
	CommonResponse
	Data *WithdrawCancelData `json:"data"`
}

// WithdrawCancelData 取消提现数据
type WithdrawCancelData struct {
	ID          int64  `json:"id" dc:"提现记录ID"`
	OrderID     string `json:"order_id" dc:"商户订单号"`
	Status      string `json:"status" dc:"状态"`
	Reason      string `json:"reason" dc:"取消原因"`
	CancelledAt string `json:"cancelled_at" dc:"取消时间"`
}

// WithdrawFeeReq 获取提现手续费请求
type WithdrawFeeReq struct {
	//g.Meta `path:"/api/v1/withdraws/fee" method:"post" tags:"提现管理" summary:"获取提现手续费"`
	ChainTokenInfo
	AmountInfo
}

// WithdrawFeeRes 获取提现手续费响应
type WithdrawFeeRes struct {
	//g.Meta `mime:"application/json"`
	CommonResponse
	Data *WithdrawFeeData `json:"data"`
}

// WithdrawFeeData 提现手续费数据
type WithdrawFeeData struct {
	FeeInfo
	NetAmount     string `json:"net_amount" dc:"实际到账金额"`
	MinAmount     string `json:"min_amount" dc:"最小提现金额"`
	MaxAmount     string `json:"max_amount" dc:"最大提现金额"`
	Available     string `json:"available" dc:"可用余额"`
	EstimatedTime string `json:"estimated_time,omitempty" dc:"预计到账时间"`
}
