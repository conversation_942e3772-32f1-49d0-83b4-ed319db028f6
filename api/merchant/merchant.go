// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package merchant

import (
	"context"

	"merchant-server/api/merchant/v1"
)

type IMerchantV1 interface {
	AuthPaymentCreate(ctx context.Context, req *v1.AuthPaymentCreateReq) (res *v1.AuthPaymentCreateRes, err error)
	AuthPaymentQuery(ctx context.Context, req *v1.AuthPaymentQueryReq) (res *v1.AuthPaymentQueryRes, err error)
	AuthPaymentList(ctx context.Context, req *v1.AuthPaymentListReq) (res *v1.AuthPaymentListRes, err error)
	HealthCheck(ctx context.Context, req *v1.HealthCheckReq) (res *v1.HealthCheckRes, err error)
	GetClientIP(ctx context.Context, req *v1.GetClientIPReq) (res *v1.GetClientIPRes, err error)
	DepositAddress(ctx context.Context, req *v1.DepositAddressReq) (res *v1.DepositAddressRes, err error)
	DepositQuery(ctx context.Context, req *v1.DepositQueryReq) (res *v1.DepositQueryRes, err error)
	DepositDetail(ctx context.Context, req *v1.DepositDetailReq) (res *v1.DepositDetailRes, err error)
	DepositAddressList(ctx context.Context, req *v1.DepositAddressListReq) (res *v1.DepositAddressListRes, err error)
	TransactionQuery(ctx context.Context, req *v1.TransactionQueryReq) (res *v1.TransactionQueryRes, err error)
	TransactionDetail(ctx context.Context, req *v1.TransactionDetailReq) (res *v1.TransactionDetailRes, err error)
	TransactionStats(ctx context.Context, req *v1.TransactionStatsReq) (res *v1.TransactionStatsRes, err error)
	WithdrawCreate(ctx context.Context, req *v1.WithdrawCreateReq) (res *v1.WithdrawCreateRes, err error)
	WithdrawQuery(ctx context.Context, req *v1.WithdrawQueryReq) (res *v1.WithdrawQueryRes, err error)
	WithdrawDetail(ctx context.Context, req *v1.WithdrawDetailReq) (res *v1.WithdrawDetailRes, err error)
	WithdrawCancel(ctx context.Context, req *v1.WithdrawCancelReq) (res *v1.WithdrawCancelRes, err error)
	WithdrawFee(ctx context.Context, req *v1.WithdrawFeeReq) (res *v1.WithdrawFeeRes, err error)
}
