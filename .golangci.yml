run:
  timeout: 5m

issues:
  exclude-dirs:
    - SuperClaude
    - resource
    - manifest

linters-settings:
  errcheck:
    # Report about not checking of errors in type assertions: `a, ok := b.(T)`.
    check-type-assertions: true
  govet:
    # Enable all analyzers.
    # Enable all govet analyzers.
    enable-all: true
    # Disable specific analyzers.
    disable:
      - fieldalignment # too strict
  gocyclo:
    # Minimal complexity of a function to report, 15 is a good value.
    min-complexity: 15
  dupl:
    # Tokens count to trigger issue, 100 is a good value.
    threshold: 100
  goconst:
    # Minimal length of string constant.
    min-len: 2
    # Minimal occurrences of constant string.
    min-occurrences: 2
  misspell:
    # Correct spellings using locale preferences.
    locale: US
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

linters:
  disable-all: true
  enable:
    - bodyclose
    - depguard
    - dogsled
    - dupl
    - errcheck
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - gosimple
    - ineffassign
    - misspell
    - nakedret
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused